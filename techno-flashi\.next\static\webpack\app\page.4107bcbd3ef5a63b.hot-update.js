"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/SimpleHeroSection.tsx":
/*!**********************************************!*\
  !*** ./src/components/SimpleHeroSection.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SimpleHeroSection: () => (/* binding */ SimpleHeroSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ SimpleHeroSection auto */ \nvar _s = $RefreshSig$();\n\n\nfunction SimpleHeroSection() {\n    _s();\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const animationRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    const mouseRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        x: 0,\n        y: 0\n    });\n    const particlesRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SimpleHeroSection.useEffect\": ()=>{\n            const canvas = canvasRef.current;\n            if (!canvas) return;\n            const ctx = canvas.getContext('2d');\n            if (!ctx) return;\n            // إعداد الكانفاس\n            const resizeCanvas = {\n                \"SimpleHeroSection.useEffect.resizeCanvas\": ()=>{\n                    canvas.width = window.innerWidth;\n                    canvas.height = window.innerHeight;\n                }\n            }[\"SimpleHeroSection.useEffect.resizeCanvas\"];\n            resizeCanvas();\n            window.addEventListener('resize', resizeCanvas);\n            // إنشاء الجسيمات الذكية المتطورة مع تحسينات جديدة\n            const createParticles = {\n                \"SimpleHeroSection.useEffect.createParticles\": ()=>{\n                    const particles = [];\n                    const colors = [\n                        '#8b5cf6',\n                        '#a855f7',\n                        '#c084fc',\n                        '#d8b4fe',\n                        '#e879f9',\n                        '#f0abfc',\n                        '#fbbf24',\n                        '#f59e0b',\n                        '#06b6d4',\n                        '#0891b2',\n                        '#10b981',\n                        '#059669',\n                        '#f97316',\n                        '#ea580c',\n                        '#dc2626',\n                        '#b91c1c',\n                        '#7c3aed',\n                        '#6d28d9'\n                    ];\n                    for(let i = 0; i < 200; i++){\n                        particles.push({\n                            x: Math.random() * canvas.width,\n                            y: Math.random() * canvas.height,\n                            vx: (Math.random() - 0.5) * 2,\n                            vy: (Math.random() - 0.5) * 2,\n                            size: Math.random() * 8 + 2,\n                            color: colors[Math.floor(Math.random() * colors.length)],\n                            opacity: Math.random() * 0.8 + 0.2,\n                            pulse: Math.random() * Math.PI * 2,\n                            pulseSpeed: 0.008 + Math.random() * 0.04,\n                            rotationSpeed: (Math.random() - 0.5) * 0.05,\n                            magnetism: Math.random() * 0.8 + 0.6,\n                            trail: [],\n                            energy: Math.random() * 100 + 50,\n                            type: Math.floor(Math.random() * 3),\n                            phase: Math.random() * Math.PI * 2,\n                            amplitude: Math.random() * 20 + 10 // سعة الحركة الموجية\n                        });\n                    }\n                    particlesRef.current = particles;\n                }\n            }[\"SimpleHeroSection.useEffect.createParticles\"];\n            createParticles();\n            // معالج حركة الماوس\n            const handleMouseMove = {\n                \"SimpleHeroSection.useEffect.handleMouseMove\": (e)=>{\n                    mouseRef.current = {\n                        x: e.clientX / window.innerWidth * 2 - 1,\n                        y: -(e.clientY / window.innerHeight) * 2 + 1\n                    };\n                }\n            }[\"SimpleHeroSection.useEffect.handleMouseMove\"];\n            window.addEventListener('mousemove', handleMouseMove);\n            // دالة رسم السداسي\n            const drawHexagon = {\n                \"SimpleHeroSection.useEffect.drawHexagon\": (ctx, x, y, size)=>{\n                    ctx.beginPath();\n                    for(let i = 0; i < 6; i++){\n                        const angle = i * Math.PI / 3;\n                        const px = x + size * Math.cos(angle);\n                        const py = y + size * Math.sin(angle);\n                        if (i === 0) {\n                            ctx.moveTo(px, py);\n                        } else {\n                            ctx.lineTo(px, py);\n                        }\n                    }\n                    ctx.closePath();\n                    ctx.stroke();\n                }\n            }[\"SimpleHeroSection.useEffect.drawHexagon\"];\n            // حلقة الرسم المتطورة مع تأثيرات مبهرة\n            const animate = {\n                \"SimpleHeroSection.useEffect.animate\": ()=>{\n                    // مسح تدريجي بدلاً من المسح الكامل\n                    ctx.fillStyle = 'rgba(255, 255, 255, 0.05)';\n                    ctx.fillRect(0, 0, canvas.width, canvas.height);\n                    const time = Date.now() * 0.001;\n                    const mouseX = mouseRef.current.x * canvas.width * 0.5 + canvas.width * 0.5;\n                    const mouseY = mouseRef.current.y * canvas.height * 0.5 + canvas.height * 0.5;\n                    // رسم هالة تتبع الماوس المتوهجة المحسنة\n                    if (mouseRef.current.x !== 0 || mouseRef.current.y !== 0) {\n                        // هالة خارجية كبيرة\n                        const outerGlowSize = 150 + Math.sin(time * 2) * 30;\n                        const outerGradient = ctx.createRadialGradient(mouseX, mouseY, 0, mouseX, mouseY, outerGlowSize);\n                        outerGradient.addColorStop(0, \"rgba(168, 85, 247, \".concat(0.2 + Math.sin(time * 1.5) * 0.08, \")\"));\n                        outerGradient.addColorStop(0.2, \"rgba(236, 72, 153, \".concat(0.15 + Math.sin(time * 2) * 0.06, \")\"));\n                        outerGradient.addColorStop(0.4, \"rgba(251, 191, 36, \".concat(0.1 + Math.sin(time * 2.5) * 0.04, \")\"));\n                        outerGradient.addColorStop(0.6, \"rgba(6, 182, 212, \".concat(0.08 + Math.sin(time * 3) * 0.03, \")\"));\n                        outerGradient.addColorStop(1, 'rgba(255, 255, 255, 0)');\n                        ctx.fillStyle = outerGradient;\n                        ctx.beginPath();\n                        ctx.arc(mouseX, mouseY, outerGlowSize, 0, Math.PI * 2);\n                        ctx.fill();\n                        // هالة متوسطة\n                        const midGlowSize = 80 + Math.sin(time * 3) * 15;\n                        const midGradient = ctx.createRadialGradient(mouseX, mouseY, 0, mouseX, mouseY, midGlowSize);\n                        midGradient.addColorStop(0, \"rgba(139, 92, 246, \".concat(0.3 + Math.sin(time * 2.5) * 0.1, \")\"));\n                        midGradient.addColorStop(0.5, \"rgba(168, 85, 247, \".concat(0.2 + Math.sin(time * 3) * 0.08, \")\"));\n                        midGradient.addColorStop(1, 'rgba(168, 85, 247, 0)');\n                        ctx.fillStyle = midGradient;\n                        ctx.beginPath();\n                        ctx.arc(mouseX, mouseY, midGlowSize, 0, Math.PI * 2);\n                        ctx.fill();\n                        // دائرة مركزية نابضة\n                        const centerSize = 20 + Math.sin(time * 4) * 8;\n                        const centerGradient = ctx.createRadialGradient(mouseX, mouseY, 0, mouseX, mouseY, centerSize);\n                        centerGradient.addColorStop(0, \"rgba(255, 255, 255, \".concat(0.9 + Math.sin(time * 5) * 0.1, \")\"));\n                        centerGradient.addColorStop(0.7, \"rgba(168, 85, 247, \".concat(0.6 + Math.sin(time * 4) * 0.2, \")\"));\n                        centerGradient.addColorStop(1, 'rgba(168, 85, 247, 0)');\n                        ctx.fillStyle = centerGradient;\n                        ctx.beginPath();\n                        ctx.arc(mouseX, mouseY, centerSize, 0, Math.PI * 2);\n                        ctx.fill();\n                        // رسم موجات متطورة تنتشر من موضع الماوس\n                        for(let i = 0; i < 5; i++){\n                            const waveRadius = 30 + (time * 80 + i * 40) % 250;\n                            const waveAlpha = Math.max(0, 0.4 - waveRadius / 250 * 0.4);\n                            if (waveAlpha > 0.01) {\n                                // موجة رئيسية\n                                ctx.strokeStyle = \"rgba(168, 85, 247, \".concat(waveAlpha, \")\");\n                                ctx.lineWidth = 3 - waveRadius / 250 * 2;\n                                ctx.beginPath();\n                                ctx.arc(mouseX, mouseY, waveRadius, 0, Math.PI * 2);\n                                ctx.stroke();\n                                // موجة ثانوية متداخلة\n                                const secondaryRadius = waveRadius * 0.7;\n                                const secondaryAlpha = waveAlpha * 0.6;\n                                ctx.strokeStyle = \"rgba(236, 72, 153, \".concat(secondaryAlpha, \")\");\n                                ctx.lineWidth = 2 - secondaryRadius / 250 * 1.5;\n                                ctx.beginPath();\n                                ctx.arc(mouseX, mouseY, secondaryRadius, 0, Math.PI * 2);\n                                ctx.stroke();\n                                // موجة داخلية\n                                if (i % 2 === 0) {\n                                    const innerRadius = waveRadius * 0.4;\n                                    const innerAlpha = waveAlpha * 0.8;\n                                    ctx.strokeStyle = \"rgba(251, 191, 36, \".concat(innerAlpha, \")\");\n                                    ctx.lineWidth = 1.5;\n                                    ctx.beginPath();\n                                    ctx.arc(mouseX, mouseY, innerRadius, 0, Math.PI * 2);\n                                    ctx.stroke();\n                                }\n                            }\n                        }\n                    }\n                    // رسم موجات ديناميكية في الخلفية\n                    const waveCount = 5;\n                    for(let i = 0; i < waveCount; i++){\n                        ctx.beginPath();\n                        ctx.strokeStyle = \"rgba(168, 85, 247, \".concat(Math.max(0.01, 0.02 + Math.sin(time + i) * 0.01), \")\");\n                        ctx.lineWidth = 2;\n                        for(let x = 0; x <= canvas.width; x += 10){\n                            const y = canvas.height * 0.5 + Math.sin(x * 0.01 + time + i) * 50 * (i + 1) + Math.sin(x * 0.005 + time * 0.5 + i) * 30;\n                            if (x === 0) {\n                                ctx.moveTo(x, y);\n                            } else {\n                                ctx.lineTo(x, y);\n                            }\n                        }\n                        ctx.stroke();\n                    }\n                    // رسم شبكة سداسية متحركة\n                    const hexSize = 60;\n                    const hexRows = Math.ceil(canvas.height / (hexSize * 0.75)) + 2;\n                    const hexCols = Math.ceil(canvas.width / (hexSize * Math.sqrt(3))) + 2;\n                    ctx.strokeStyle = \"rgba(139, 92, 246, \".concat(Math.max(0.01, 0.04 + Math.sin(time * 0.5) * 0.02), \")\");\n                    ctx.lineWidth = 1;\n                    for(let row = 0; row < hexRows; row++){\n                        for(let col = 0; col < hexCols; col++){\n                            const x = col * hexSize * Math.sqrt(3) + row % 2 * hexSize * Math.sqrt(3) * 0.5;\n                            const y = row * hexSize * 0.75;\n                            const distanceToMouse = Math.sqrt((x - mouseX) ** 2 + (y - mouseY) ** 2);\n                            const influence = Math.max(0, 1 - distanceToMouse / 300); // زيادة نطاق التأثير\n                            if (influence > 0.05) {\n                                // تحسين الحركة والحجم حسب قرب الماوس\n                                const moveIntensity = influence * 15;\n                                const sizeMultiplier = 0.2 + influence * 0.8;\n                                const alpha = 0.02 + influence * 0.1;\n                                ctx.strokeStyle = \"rgba(139, 92, 246, \".concat(alpha, \")\");\n                                ctx.lineWidth = 1 + influence * 2;\n                                drawHexagon(ctx, x + Math.sin(time + row + col) * moveIntensity, y + Math.cos(time + row + col) * moveIntensity, hexSize * sizeMultiplier);\n                            }\n                        }\n                    }\n                    // رسم دوائر متوهجة ناعمة\n                    const glowCircles = 5;\n                    for(let i = 0; i < glowCircles; i++){\n                        const x = canvas.width * (0.1 + i * 0.2);\n                        const y = canvas.height * (0.2 + Math.sin(time * 0.3 + i) * 0.3);\n                        const radius = Math.max(10, 150 + Math.sin(time * 0.4 + i) * 50);\n                        const gradient = ctx.createRadialGradient(x, y, 0, x, y, radius);\n                        const colors = [\n                            \"rgba(168, 85, 247, \".concat(Math.max(0.01, 0.05 + Math.sin(time + i) * 0.02), \")\"),\n                            \"rgba(236, 72, 153, \".concat(Math.max(0.01, 0.03 + Math.sin(time + i + 1) * 0.015), \")\"),\n                            \"rgba(251, 191, 36, \".concat(Math.max(0.01, 0.02 + Math.sin(time + i + 2) * 0.01), \")\")\n                        ];\n                        gradient.addColorStop(0, colors[i % colors.length]);\n                        gradient.addColorStop(0.7, colors[(i + 1) % colors.length].replace(/[\\d.]+\\)/, '0.01)'));\n                        gradient.addColorStop(1, 'rgba(255, 255, 255, 0)');\n                        ctx.fillStyle = gradient;\n                        ctx.beginPath();\n                        ctx.arc(x, y, radius, 0, Math.PI * 2);\n                        ctx.fill();\n                    }\n                    // رسم أشكال هندسية متحركة\n                    const shapes = 3;\n                    for(let i = 0; i < shapes; i++){\n                        const x = canvas.width * (0.3 + i * 0.2);\n                        const y = canvas.height * (0.4 + Math.cos(time * 0.2 + i) * 0.2);\n                        const size = Math.max(10, 30 + Math.sin(time + i) * 10);\n                        const rotation = time * 0.5 + i;\n                        ctx.save();\n                        ctx.translate(x, y);\n                        ctx.rotate(rotation);\n                        const gradient = ctx.createLinearGradient(-size, -size, size, size);\n                        gradient.addColorStop(0, \"rgba(168, 85, 247, \".concat(Math.max(0.01, 0.1 + Math.sin(time + i) * 0.05), \")\"));\n                        gradient.addColorStop(1, \"rgba(236, 72, 153, \".concat(Math.max(0.01, 0.05 + Math.cos(time + i) * 0.03), \")\"));\n                        ctx.fillStyle = gradient;\n                        ctx.beginPath();\n                        ctx.roundRect(-size / 2, -size / 2, size, size, 8);\n                        ctx.fill();\n                        ctx.restore();\n                    }\n                    // تحديث ورسم الجسيمات الذكية المتطورة\n                    particlesRef.current.forEach({\n                        \"SimpleHeroSection.useEffect.animate\": (particle, index)=>{\n                            // التأكد من صحة القيم\n                            particle.size = Math.max(1, particle.size || 2);\n                            particle.energy = Math.max(50, Math.min(100, particle.energy || 50));\n                            particle.opacity = Math.max(0.1, Math.min(1, particle.opacity || 0.5));\n                            // حفظ الموضع السابق للمسار\n                            particle.trail.push({\n                                x: particle.x,\n                                y: particle.y\n                            });\n                            if (particle.trail.length > 5) {\n                                particle.trail.shift();\n                            }\n                            // تحديث الموضع مع فيزياء متقدمة\n                            particle.x += particle.vx;\n                            particle.y += particle.vy;\n                            particle.pulse += particle.pulseSpeed;\n                            // تأثير الماوس المغناطيسي المتطور والأكثر ذكاءً\n                            const mouseInfluence = 250; // نطاق تأثير أوسع\n                            const dx = mouseX - particle.x;\n                            const dy = mouseY - particle.y;\n                            const distance = Math.sqrt(dx * dx + dy * dy);\n                            if (distance < mouseInfluence) {\n                                const force = (mouseInfluence - distance) / mouseInfluence * particle.magnetism;\n                                const angle = Math.atan2(dy, dx);\n                                // قوة جذب متدرجة حسب المسافة\n                                const attractionForce = force * 0.012;\n                                particle.vx += Math.cos(angle) * attractionForce;\n                                particle.vy += Math.sin(angle) * attractionForce;\n                                // تأثير الطاقة المحسن مع تسارع\n                                particle.energy = Math.min(100, particle.energy + force * 6);\n                                // حركة موجية إضافية\n                                particle.phase += 0.1;\n                                const waveForce = Math.sin(particle.phase) * force * 0.003;\n                                particle.vx += Math.cos(angle + Math.PI / 2) * waveForce;\n                                particle.vy += Math.sin(angle + Math.PI / 2) * waveForce;\n                                // تأثير اهتزاز ذكي\n                                const vibrationIntensity = force * 0.004;\n                                particle.vx += (Math.random() - 0.5) * vibrationIntensity;\n                                particle.vy += (Math.random() - 0.5) * vibrationIntensity;\n                                // تسريع الدوران عند القرب من الماوس\n                                particle.rotationSpeed += force * 0.001;\n                            } else {\n                                // تقليل الطاقة والدوران تدريجياً\n                                particle.energy = Math.max(50, particle.energy - 0.2);\n                                particle.rotationSpeed *= 0.98;\n                            }\n                            // تطبيق الاحتكاك\n                            particle.vx *= 0.99;\n                            particle.vy *= 0.99;\n                            // حدود الشاشة مع ارتداد ديناميكي\n                            if (particle.x < 0 || particle.x > canvas.width) {\n                                particle.vx *= -0.7;\n                                particle.x = Math.max(0, Math.min(canvas.width, particle.x));\n                            }\n                            if (particle.y < 0 || particle.y > canvas.height) {\n                                particle.vy *= -0.7;\n                                particle.y = Math.max(0, Math.min(canvas.height, particle.y));\n                            }\n                            // رسم مسار الجسيمة\n                            if (particle.trail.length > 1) {\n                                ctx.beginPath();\n                                ctx.strokeStyle = particle.color.replace(')', ', 0.2)').replace('rgb', 'rgba');\n                                ctx.lineWidth = 1;\n                                for(let i = 0; i < particle.trail.length - 1; i++){\n                                    const alpha = i / particle.trail.length;\n                                    ctx.globalAlpha = alpha * 0.3;\n                                    if (i === 0) {\n                                        ctx.moveTo(particle.trail[i].x, particle.trail[i].y);\n                                    } else {\n                                        ctx.lineTo(particle.trail[i].x, particle.trail[i].y);\n                                    }\n                                }\n                                ctx.stroke();\n                            }\n                            // رسم الجسيمة مع تأثيرات متقدمة\n                            const energyFactor = particle.energy / 100;\n                            const pulseSize = Math.max(1, particle.size + Math.sin(particle.pulse) * 2 * energyFactor);\n                            const pulseOpacity = Math.max(0.1, particle.opacity + Math.sin(particle.pulse) * 0.3 * energyFactor);\n                            // رسم الهالة الخارجية\n                            const outerRadius = Math.max(1, pulseSize * 4);\n                            const outerGradient = ctx.createRadialGradient(particle.x, particle.y, 0, particle.x, particle.y, outerRadius);\n                            outerGradient.addColorStop(0, particle.color.replace(')', ', 0.4)').replace('rgb', 'rgba'));\n                            outerGradient.addColorStop(0.5, particle.color.replace(')', ', 0.2)').replace('rgb', 'rgba'));\n                            outerGradient.addColorStop(1, particle.color.replace(')', ', 0)').replace('rgb', 'rgba'));\n                            ctx.fillStyle = outerGradient;\n                            ctx.globalAlpha = pulseOpacity * 0.6 * energyFactor;\n                            ctx.beginPath();\n                            ctx.arc(particle.x, particle.y, outerRadius, 0, Math.PI * 2);\n                            ctx.fill();\n                            // رسم الهالة الداخلية\n                            const innerRadius = Math.max(1, pulseSize * 2);\n                            const innerGradient = ctx.createRadialGradient(particle.x, particle.y, 0, particle.x, particle.y, innerRadius);\n                            innerGradient.addColorStop(0, particle.color.replace(')', ', 0.8)').replace('rgb', 'rgba'));\n                            innerGradient.addColorStop(1, particle.color.replace(')', ', 0.2)').replace('rgb', 'rgba'));\n                            ctx.fillStyle = innerGradient;\n                            ctx.globalAlpha = pulseOpacity * 0.8;\n                            ctx.beginPath();\n                            ctx.arc(particle.x, particle.y, innerRadius, 0, Math.PI * 2);\n                            ctx.fill();\n                            // رسم الجسيمة الأساسية بأشكال مختلفة\n                            ctx.save();\n                            ctx.translate(particle.x, particle.y);\n                            ctx.rotate(particle.pulse * particle.rotationSpeed);\n                            ctx.fillStyle = particle.color;\n                            ctx.globalAlpha = Math.max(0.1, Math.min(1, pulseOpacity));\n                            const finalSize = Math.max(0.5, pulseSize);\n                            // رسم حسب نوع الجسيمة\n                            if (particle.type === 0) {\n                                // دائرة\n                                ctx.beginPath();\n                                ctx.arc(0, 0, finalSize, 0, Math.PI * 2);\n                                ctx.fill();\n                            } else if (particle.type === 1) {\n                                // مربع مستدير الزوايا\n                                ctx.beginPath();\n                                ctx.roundRect(-finalSize, -finalSize, finalSize * 2, finalSize * 2, finalSize * 0.3);\n                                ctx.fill();\n                            } else {\n                                // نجمة\n                                ctx.beginPath();\n                                const spikes = 5;\n                                const outerRadius = finalSize;\n                                const innerRadius = finalSize * 0.5;\n                                for(let i = 0; i < spikes * 2; i++){\n                                    const radius = i % 2 === 0 ? outerRadius : innerRadius;\n                                    const angle = i * Math.PI / spikes;\n                                    const x = Math.cos(angle) * radius;\n                                    const y = Math.sin(angle) * radius;\n                                    if (i === 0) {\n                                        ctx.moveTo(x, y);\n                                    } else {\n                                        ctx.lineTo(x, y);\n                                    }\n                                }\n                                ctx.closePath();\n                                ctx.fill();\n                            }\n                            ctx.restore();\n                            // رسم خطوط الاتصال الذكية المتطورة\n                            particlesRef.current.forEach({\n                                \"SimpleHeroSection.useEffect.animate\": (otherParticle, otherIndex)=>{\n                                    if (index !== otherIndex && index < otherIndex) {\n                                        const dx = particle.x - otherParticle.x;\n                                        const dy = particle.y - otherParticle.y;\n                                        const distance = Math.sqrt(dx * dx + dy * dy);\n                                        if (distance < 150) {\n                                            const opacity = Math.max(0, 0.2 * (1 - distance / 150));\n                                            const energyBonus = (particle.energy + otherParticle.energy) / 200;\n                                            const finalOpacity = Math.max(0.01, Math.min(1, opacity * (1 + energyBonus)));\n                                            // خط متدرج ديناميكي\n                                            const gradient = ctx.createLinearGradient(particle.x, particle.y, otherParticle.x, otherParticle.y);\n                                            const color1 = particle.color.replace(')', ', ' + finalOpacity + ')').replace('rgb', 'rgba');\n                                            const color2 = otherParticle.color.replace(')', ', ' + finalOpacity + ')').replace('rgb', 'rgba');\n                                            const midColor = \"rgba(192, 132, 252, \".concat(finalOpacity * 1.2, \")\");\n                                            gradient.addColorStop(0, color1);\n                                            gradient.addColorStop(0.5, midColor);\n                                            gradient.addColorStop(1, color2);\n                                            // رسم خط متموج\n                                            ctx.beginPath();\n                                            ctx.strokeStyle = gradient;\n                                            ctx.lineWidth = 1 + finalOpacity * 3;\n                                            const steps = 10;\n                                            for(let i = 0; i <= steps; i++){\n                                                const t = i / steps;\n                                                const x = particle.x + (otherParticle.x - particle.x) * t;\n                                                const y = particle.y + (otherParticle.y - particle.y) * t;\n                                                // إضافة تموج ناعم\n                                                const waveOffset = Math.sin(t * Math.PI * 2 + time) * 5 * finalOpacity;\n                                                const perpX = -(otherParticle.y - particle.y) / distance;\n                                                const perpY = (otherParticle.x - particle.x) / distance;\n                                                const finalX = x + perpX * waveOffset;\n                                                const finalY = y + perpY * waveOffset;\n                                                if (i === 0) {\n                                                    ctx.moveTo(finalX, finalY);\n                                                } else {\n                                                    ctx.lineTo(finalX, finalY);\n                                                }\n                                            }\n                                            ctx.stroke();\n                                            // إضافة نقاط ضوئية على الخط\n                                            if (finalOpacity > 0.1) {\n                                                const midX = (particle.x + otherParticle.x) / 2;\n                                                const midY = (particle.y + otherParticle.y) / 2;\n                                                ctx.beginPath();\n                                                ctx.arc(midX, midY, Math.max(0.5, 2 * finalOpacity), 0, Math.PI * 2);\n                                                ctx.fillStyle = \"rgba(255, 255, 255, \".concat(Math.max(0.1, finalOpacity * 0.8), \")\");\n                                                ctx.fill();\n                                            }\n                                        }\n                                    }\n                                }\n                            }[\"SimpleHeroSection.useEffect.animate\"]);\n                        }\n                    }[\"SimpleHeroSection.useEffect.animate\"]);\n                    ctx.globalAlpha = 1;\n                    animationRef.current = requestAnimationFrame(animate);\n                }\n            }[\"SimpleHeroSection.useEffect.animate\"];\n            animate();\n            // التنظيف\n            return ({\n                \"SimpleHeroSection.useEffect\": ()=>{\n                    if (animationRef.current) {\n                        cancelAnimationFrame(animationRef.current);\n                    }\n                    window.removeEventListener('resize', resizeCanvas);\n                    window.removeEventListener('mousemove', handleMouseMove);\n                }\n            })[\"SimpleHeroSection.useEffect\"];\n        }\n    }[\"SimpleHeroSection.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-white via-purple-50 to-pink-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                ref: canvasRef,\n                className: \"absolute inset-0 w-full h-full\",\n                style: {\n                    zIndex: 1\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                lineNumber: 558,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-t from-white/30 via-transparent to-purple-50/20\",\n                style: {\n                    zIndex: 2\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                lineNumber: 565,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 text-center px-4 max-w-6xl mx-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-5xl md:text-7xl font-bold text-gray-900 mb-6 leading-tight\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"bg-gradient-to-r from-purple-600 via-pink-600 to-purple-600 bg-clip-text text-transparent\",\n                                    children: \"TechnoFlash\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                    lineNumber: 571,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 570,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl md:text-2xl text-gray-700 mb-8 max-w-3xl mx-auto leading-relaxed\",\n                                children: \"منصتك الشاملة لأحدث التقنيات وأدوات الذكاء الاصطناعي\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 575,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                        lineNumber: 569,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row gap-4 justify-center items-center mb-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/articles\",\n                                className: \"group bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-4 rounded-xl font-semibold text-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"استكشف المقالات\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 586,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5 group-hover:translate-x-1 transition-transform\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M13 7l5 5m0 0l-5 5m5-5H6\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                            lineNumber: 588,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 587,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 582,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/ai-tools\",\n                                className: \"group border-2 border-purple-400 text-purple-400 px-8 py-4 rounded-xl font-semibold text-lg hover:bg-purple-400 hover:text-white transition-all duration-300 flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"أدوات الذكاء الاصطناعي\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 596,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5 group-hover:rotate-12 transition-transform\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M13 10V3L4 14h7v7l9-11h-7z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                            lineNumber: 598,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 597,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 592,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                        lineNumber: 581,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-2\",\n                                        children: \"100+\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 606,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-600\",\n                                        children: \"مقال تقني\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 607,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 605,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-2\",\n                                        children: \"50+\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 610,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-600\",\n                                        children: \"أداة ذكاء اصطناعي\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 611,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 609,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-2\",\n                                        children: \"1000+\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 614,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-600\",\n                                        children: \"قارئ نشط\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 615,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 613,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                        lineNumber: 604,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                lineNumber: 568,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-8 left-1/2 transform -translate-x-1/2 z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-bounce\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-8 h-8 border-2 border-gray-400/60 rounded-full flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-4 h-4 text-gray-600/80\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M19 14l-7 7m0 0l-7-7m7 7V3\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 625,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                            lineNumber: 624,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                        lineNumber: 623,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                    lineNumber: 622,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                lineNumber: 621,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n        lineNumber: 556,\n        columnNumber: 5\n    }, this);\n}\n_s(SimpleHeroSection, \"/8IKySTmGZxFymijsBTHUGtuaQs=\");\n_c = SimpleHeroSection;\nvar _c;\n$RefreshReg$(_c, \"SimpleHeroSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/SimpleHeroSection.tsx\n"));

/***/ })

});