"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/SimpleHeroSection.tsx":
/*!**********************************************!*\
  !*** ./src/components/SimpleHeroSection.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SimpleHeroSection: () => (/* binding */ SimpleHeroSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ SimpleHeroSection auto */ \nvar _s = $RefreshSig$();\n\n\nfunction SimpleHeroSection() {\n    _s();\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const animationRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    const mouseRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        x: 0,\n        y: 0\n    });\n    const particlesRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SimpleHeroSection.useEffect\": ()=>{\n            const canvas = canvasRef.current;\n            if (!canvas) return;\n            const ctx = canvas.getContext('2d');\n            if (!ctx) return;\n            // إعداد الكانفاس\n            const resizeCanvas = {\n                \"SimpleHeroSection.useEffect.resizeCanvas\": ()=>{\n                    canvas.width = window.innerWidth;\n                    canvas.height = window.innerHeight;\n                }\n            }[\"SimpleHeroSection.useEffect.resizeCanvas\"];\n            resizeCanvas();\n            window.addEventListener('resize', resizeCanvas);\n            // إنشاء الجسيمات الذكية المتطورة\n            const createParticles = {\n                \"SimpleHeroSection.useEffect.createParticles\": ()=>{\n                    const particles = [];\n                    const colors = [\n                        '#8b5cf6',\n                        '#a855f7',\n                        '#c084fc',\n                        '#d8b4fe',\n                        '#e879f9',\n                        '#f0abfc',\n                        '#fbbf24',\n                        '#f59e0b',\n                        '#06b6d4',\n                        '#0891b2',\n                        '#10b981',\n                        '#059669'\n                    ];\n                    for(let i = 0; i < 150; i++){\n                        particles.push({\n                            x: Math.random() * canvas.width,\n                            y: Math.random() * canvas.height,\n                            vx: (Math.random() - 0.5) * 1.5,\n                            vy: (Math.random() - 0.5) * 1.5,\n                            size: Math.random() * 6 + 1,\n                            color: colors[Math.floor(Math.random() * colors.length)],\n                            opacity: Math.random() * 0.6 + 0.2,\n                            pulse: Math.random() * Math.PI * 2,\n                            pulseSpeed: 0.01 + Math.random() * 0.03,\n                            rotationSpeed: (Math.random() - 0.5) * 0.03,\n                            magnetism: Math.random() * 0.5 + 0.5,\n                            trail: [],\n                            energy: Math.random() * 100 + 50 // طاقة الجسيمة\n                        });\n                    }\n                    particlesRef.current = particles;\n                }\n            }[\"SimpleHeroSection.useEffect.createParticles\"];\n            createParticles();\n            // معالج حركة الماوس\n            const handleMouseMove = {\n                \"SimpleHeroSection.useEffect.handleMouseMove\": (e)=>{\n                    mouseRef.current = {\n                        x: e.clientX / window.innerWidth * 2 - 1,\n                        y: -(e.clientY / window.innerHeight) * 2 + 1\n                    };\n                }\n            }[\"SimpleHeroSection.useEffect.handleMouseMove\"];\n            window.addEventListener('mousemove', handleMouseMove);\n            // دالة رسم السداسي\n            const drawHexagon = {\n                \"SimpleHeroSection.useEffect.drawHexagon\": (ctx, x, y, size)=>{\n                    ctx.beginPath();\n                    for(let i = 0; i < 6; i++){\n                        const angle = i * Math.PI / 3;\n                        const px = x + size * Math.cos(angle);\n                        const py = y + size * Math.sin(angle);\n                        if (i === 0) {\n                            ctx.moveTo(px, py);\n                        } else {\n                            ctx.lineTo(px, py);\n                        }\n                    }\n                    ctx.closePath();\n                    ctx.stroke();\n                }\n            }[\"SimpleHeroSection.useEffect.drawHexagon\"];\n            // حلقة الرسم المتطورة مع تأثيرات مبهرة\n            const animate = {\n                \"SimpleHeroSection.useEffect.animate\": ()=>{\n                    // مسح تدريجي بدلاً من المسح الكامل\n                    ctx.fillStyle = 'rgba(255, 255, 255, 0.05)';\n                    ctx.fillRect(0, 0, canvas.width, canvas.height);\n                    const time = Date.now() * 0.001;\n                    const mouseX = mouseRef.current.x * canvas.width * 0.5 + canvas.width * 0.5;\n                    const mouseY = mouseRef.current.y * canvas.height * 0.5 + canvas.height * 0.5;\n                    // رسم موجات ديناميكية في الخلفية\n                    const waveCount = 5;\n                    for(let i = 0; i < waveCount; i++){\n                        ctx.beginPath();\n                        ctx.strokeStyle = \"rgba(168, 85, 247, \".concat(0.02 + Math.sin(time + i) * 0.01, \")\");\n                        ctx.lineWidth = 2;\n                        for(let x = 0; x <= canvas.width; x += 10){\n                            const y = canvas.height * 0.5 + Math.sin(x * 0.01 + time + i) * 50 * (i + 1) + Math.sin(x * 0.005 + time * 0.5 + i) * 30;\n                            if (x === 0) {\n                                ctx.moveTo(x, y);\n                            } else {\n                                ctx.lineTo(x, y);\n                            }\n                        }\n                        ctx.stroke();\n                    }\n                    // رسم شبكة سداسية متحركة\n                    const hexSize = 60;\n                    const hexRows = Math.ceil(canvas.height / (hexSize * 0.75)) + 2;\n                    const hexCols = Math.ceil(canvas.width / (hexSize * Math.sqrt(3))) + 2;\n                    ctx.strokeStyle = \"rgba(139, 92, 246, \".concat(0.04 + Math.sin(time * 0.5) * 0.02, \")\");\n                    ctx.lineWidth = 1;\n                    for(let row = 0; row < hexRows; row++){\n                        for(let col = 0; col < hexCols; col++){\n                            const x = col * hexSize * Math.sqrt(3) + row % 2 * hexSize * Math.sqrt(3) * 0.5;\n                            const y = row * hexSize * 0.75;\n                            const distanceToMouse = Math.sqrt((x - mouseX) ** 2 + (y - mouseY) ** 2);\n                            const influence = Math.max(0, 1 - distanceToMouse / 200);\n                            if (influence > 0.1) {\n                                drawHexagon(ctx, x + Math.sin(time + row + col) * 5, y + Math.cos(time + row + col) * 5, hexSize * 0.3);\n                            }\n                        }\n                    }\n                    // رسم دوائر متوهجة ناعمة\n                    const glowCircles = 5;\n                    for(let i = 0; i < glowCircles; i++){\n                        const x = canvas.width * (0.1 + i * 0.2);\n                        const y = canvas.height * (0.2 + Math.sin(time * 0.3 + i) * 0.3);\n                        const radius = 150 + Math.sin(time * 0.4 + i) * 50;\n                        const gradient = ctx.createRadialGradient(x, y, 0, x, y, radius);\n                        const colors = [\n                            \"rgba(168, 85, 247, \".concat(0.05 + Math.sin(time + i) * 0.02, \")\"),\n                            \"rgba(236, 72, 153, \".concat(0.03 + Math.sin(time + i + 1) * 0.015, \")\"),\n                            \"rgba(251, 191, 36, \".concat(0.02 + Math.sin(time + i + 2) * 0.01, \")\")\n                        ];\n                        gradient.addColorStop(0, colors[i % colors.length]);\n                        gradient.addColorStop(0.7, colors[(i + 1) % colors.length].replace(/[\\d.]+\\)/, '0.01)'));\n                        gradient.addColorStop(1, 'rgba(255, 255, 255, 0)');\n                        ctx.fillStyle = gradient;\n                        ctx.beginPath();\n                        ctx.arc(x, y, radius, 0, Math.PI * 2);\n                        ctx.fill();\n                    }\n                    // رسم أشكال هندسية متحركة\n                    const shapes = 3;\n                    for(let i = 0; i < shapes; i++){\n                        const x = canvas.width * (0.3 + i * 0.2);\n                        const y = canvas.height * (0.4 + Math.cos(time * 0.2 + i) * 0.2);\n                        const size = 30 + Math.sin(time + i) * 10;\n                        const rotation = time * 0.5 + i;\n                        ctx.save();\n                        ctx.translate(x, y);\n                        ctx.rotate(rotation);\n                        const gradient = ctx.createLinearGradient(-size, -size, size, size);\n                        gradient.addColorStop(0, \"rgba(168, 85, 247, \".concat(0.1 + Math.sin(time + i) * 0.05, \")\"));\n                        gradient.addColorStop(1, \"rgba(236, 72, 153, \".concat(0.05 + Math.cos(time + i) * 0.03, \")\"));\n                        ctx.fillStyle = gradient;\n                        ctx.beginPath();\n                        ctx.roundRect(-size / 2, -size / 2, size, size, 8);\n                        ctx.fill();\n                        ctx.restore();\n                    }\n                    // تحديث ورسم الجسيمات الذكية المتطورة\n                    particlesRef.current.forEach({\n                        \"SimpleHeroSection.useEffect.animate\": (particle, index)=>{\n                            // حفظ الموضع السابق للمسار\n                            particle.trail.push({\n                                x: particle.x,\n                                y: particle.y\n                            });\n                            if (particle.trail.length > 5) {\n                                particle.trail.shift();\n                            }\n                            // تحديث الموضع مع فيزياء متقدمة\n                            particle.x += particle.vx;\n                            particle.y += particle.vy;\n                            particle.pulse += particle.pulseSpeed;\n                            // تأثير الماوس المغناطيسي المتقدم\n                            const mouseInfluence = 120;\n                            const dx = mouseX - particle.x;\n                            const dy = mouseY - particle.y;\n                            const distance = Math.sqrt(dx * dx + dy * dy);\n                            if (distance < mouseInfluence) {\n                                const force = (mouseInfluence - distance) / mouseInfluence * particle.magnetism;\n                                const angle = Math.atan2(dy, dx);\n                                particle.vx += Math.cos(angle) * force * 0.003;\n                                particle.vy += Math.sin(angle) * force * 0.003;\n                                // تأثير الطاقة\n                                particle.energy = Math.min(100, particle.energy + force * 2);\n                            } else {\n                                // تقليل الطاقة تدريجياً\n                                particle.energy = Math.max(50, particle.energy - 0.5);\n                            }\n                            // تطبيق الاحتكاك\n                            particle.vx *= 0.99;\n                            particle.vy *= 0.99;\n                            // حدود الشاشة مع ارتداد ديناميكي\n                            if (particle.x < 0 || particle.x > canvas.width) {\n                                particle.vx *= -0.7;\n                                particle.x = Math.max(0, Math.min(canvas.width, particle.x));\n                            }\n                            if (particle.y < 0 || particle.y > canvas.height) {\n                                particle.vy *= -0.7;\n                                particle.y = Math.max(0, Math.min(canvas.height, particle.y));\n                            }\n                            // رسم مسار الجسيمة\n                            if (particle.trail.length > 1) {\n                                ctx.beginPath();\n                                ctx.strokeStyle = particle.color.replace(')', ', 0.2)').replace('rgb', 'rgba');\n                                ctx.lineWidth = 1;\n                                for(let i = 0; i < particle.trail.length - 1; i++){\n                                    const alpha = i / particle.trail.length;\n                                    ctx.globalAlpha = alpha * 0.3;\n                                    if (i === 0) {\n                                        ctx.moveTo(particle.trail[i].x, particle.trail[i].y);\n                                    } else {\n                                        ctx.lineTo(particle.trail[i].x, particle.trail[i].y);\n                                    }\n                                }\n                                ctx.stroke();\n                            }\n                            // رسم الجسيمة مع تأثيرات متقدمة\n                            const energyFactor = particle.energy / 100;\n                            const pulseSize = Math.max(1, particle.size + Math.sin(particle.pulse) * 2 * energyFactor);\n                            const pulseOpacity = Math.max(0.1, particle.opacity + Math.sin(particle.pulse) * 0.3 * energyFactor);\n                            // رسم الهالة الخارجية\n                            const outerRadius = Math.max(1, pulseSize * 4);\n                            const outerGradient = ctx.createRadialGradient(particle.x, particle.y, 0, particle.x, particle.y, outerRadius);\n                            outerGradient.addColorStop(0, particle.color.replace(')', ', 0.4)').replace('rgb', 'rgba'));\n                            outerGradient.addColorStop(0.5, particle.color.replace(')', ', 0.2)').replace('rgb', 'rgba'));\n                            outerGradient.addColorStop(1, particle.color.replace(')', ', 0)').replace('rgb', 'rgba'));\n                            ctx.fillStyle = outerGradient;\n                            ctx.globalAlpha = pulseOpacity * 0.6 * energyFactor;\n                            ctx.beginPath();\n                            ctx.arc(particle.x, particle.y, pulseSize * 4, 0, Math.PI * 2);\n                            ctx.fill();\n                            // رسم الهالة الداخلية\n                            const innerGradient = ctx.createRadialGradient(particle.x, particle.y, 0, particle.x, particle.y, pulseSize * 2);\n                            innerGradient.addColorStop(0, particle.color.replace(')', ', 0.8)').replace('rgb', 'rgba'));\n                            innerGradient.addColorStop(1, particle.color.replace(')', ', 0.2)').replace('rgb', 'rgba'));\n                            ctx.fillStyle = innerGradient;\n                            ctx.globalAlpha = pulseOpacity * 0.8;\n                            ctx.beginPath();\n                            ctx.arc(particle.x, particle.y, pulseSize * 2, 0, Math.PI * 2);\n                            ctx.fill();\n                            // رسم الجسيمة الأساسية\n                            ctx.beginPath();\n                            ctx.arc(particle.x, particle.y, pulseSize, 0, Math.PI * 2);\n                            ctx.fillStyle = particle.color;\n                            ctx.globalAlpha = pulseOpacity;\n                            ctx.fill();\n                            // رسم خطوط الاتصال الذكية المتطورة\n                            particlesRef.current.forEach({\n                                \"SimpleHeroSection.useEffect.animate\": (otherParticle, otherIndex)=>{\n                                    if (index !== otherIndex && index < otherIndex) {\n                                        const dx = particle.x - otherParticle.x;\n                                        const dy = particle.y - otherParticle.y;\n                                        const distance = Math.sqrt(dx * dx + dy * dy);\n                                        if (distance < 150) {\n                                            const opacity = 0.2 * (1 - distance / 150);\n                                            const energyBonus = (particle.energy + otherParticle.energy) / 200;\n                                            const finalOpacity = opacity * (1 + energyBonus);\n                                            // خط متدرج ديناميكي\n                                            const gradient = ctx.createLinearGradient(particle.x, particle.y, otherParticle.x, otherParticle.y);\n                                            const color1 = particle.color.replace(')', ', ' + finalOpacity + ')').replace('rgb', 'rgba');\n                                            const color2 = otherParticle.color.replace(')', ', ' + finalOpacity + ')').replace('rgb', 'rgba');\n                                            const midColor = \"rgba(192, 132, 252, \".concat(finalOpacity * 1.2, \")\");\n                                            gradient.addColorStop(0, color1);\n                                            gradient.addColorStop(0.5, midColor);\n                                            gradient.addColorStop(1, color2);\n                                            // رسم خط متموج\n                                            ctx.beginPath();\n                                            ctx.strokeStyle = gradient;\n                                            ctx.lineWidth = 1 + finalOpacity * 3;\n                                            const steps = 10;\n                                            for(let i = 0; i <= steps; i++){\n                                                const t = i / steps;\n                                                const x = particle.x + (otherParticle.x - particle.x) * t;\n                                                const y = particle.y + (otherParticle.y - particle.y) * t;\n                                                // إضافة تموج ناعم\n                                                const waveOffset = Math.sin(t * Math.PI * 2 + time) * 5 * finalOpacity;\n                                                const perpX = -(otherParticle.y - particle.y) / distance;\n                                                const perpY = (otherParticle.x - particle.x) / distance;\n                                                const finalX = x + perpX * waveOffset;\n                                                const finalY = y + perpY * waveOffset;\n                                                if (i === 0) {\n                                                    ctx.moveTo(finalX, finalY);\n                                                } else {\n                                                    ctx.lineTo(finalX, finalY);\n                                                }\n                                            }\n                                            ctx.stroke();\n                                            // إضافة نقاط ضوئية على الخط\n                                            if (finalOpacity > 0.1) {\n                                                const midX = (particle.x + otherParticle.x) / 2;\n                                                const midY = (particle.y + otherParticle.y) / 2;\n                                                ctx.beginPath();\n                                                ctx.arc(midX, midY, 2 * finalOpacity, 0, Math.PI * 2);\n                                                ctx.fillStyle = \"rgba(255, 255, 255, \".concat(finalOpacity * 0.8, \")\");\n                                                ctx.fill();\n                                            }\n                                        }\n                                    }\n                                }\n                            }[\"SimpleHeroSection.useEffect.animate\"]);\n                        }\n                    }[\"SimpleHeroSection.useEffect.animate\"]);\n                    ctx.globalAlpha = 1;\n                    animationRef.current = requestAnimationFrame(animate);\n                }\n            }[\"SimpleHeroSection.useEffect.animate\"];\n            animate();\n            // التنظيف\n            return ({\n                \"SimpleHeroSection.useEffect\": ()=>{\n                    if (animationRef.current) {\n                        cancelAnimationFrame(animationRef.current);\n                    }\n                    window.removeEventListener('resize', resizeCanvas);\n                    window.removeEventListener('mousemove', handleMouseMove);\n                }\n            })[\"SimpleHeroSection.useEffect\"];\n        }\n    }[\"SimpleHeroSection.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-white via-purple-50 to-pink-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                ref: canvasRef,\n                className: \"absolute inset-0 w-full h-full\",\n                style: {\n                    zIndex: 1\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                lineNumber: 391,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-t from-white/30 via-transparent to-purple-50/20\",\n                style: {\n                    zIndex: 2\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                lineNumber: 398,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 text-center px-4 max-w-6xl mx-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-5xl md:text-7xl font-bold text-gray-900 mb-6 leading-tight\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"bg-gradient-to-r from-purple-600 via-pink-600 to-purple-600 bg-clip-text text-transparent\",\n                                    children: \"TechnoFlash\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                    lineNumber: 404,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 403,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl md:text-2xl text-gray-700 mb-8 max-w-3xl mx-auto leading-relaxed\",\n                                children: \"منصتك الشاملة لأحدث التقنيات وأدوات الذكاء الاصطناعي\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 408,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                        lineNumber: 402,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row gap-4 justify-center items-center mb-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/articles\",\n                                className: \"group bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-4 rounded-xl font-semibold text-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"استكشف المقالات\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 419,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5 group-hover:translate-x-1 transition-transform\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M13 7l5 5m0 0l-5 5m5-5H6\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                            lineNumber: 421,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 420,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 415,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/ai-tools\",\n                                className: \"group border-2 border-purple-400 text-purple-400 px-8 py-4 rounded-xl font-semibold text-lg hover:bg-purple-400 hover:text-white transition-all duration-300 flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"أدوات الذكاء الاصطناعي\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 429,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5 group-hover:rotate-12 transition-transform\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M13 10V3L4 14h7v7l9-11h-7z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                            lineNumber: 431,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 430,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 425,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                        lineNumber: 414,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-2\",\n                                        children: \"100+\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 439,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-600\",\n                                        children: \"مقال تقني\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 440,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 438,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-2\",\n                                        children: \"50+\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 443,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-600\",\n                                        children: \"أداة ذكاء اصطناعي\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 444,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 442,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-2\",\n                                        children: \"1000+\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 447,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-600\",\n                                        children: \"قارئ نشط\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 448,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 446,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                        lineNumber: 437,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                lineNumber: 401,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-8 left-1/2 transform -translate-x-1/2 z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-bounce\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-8 h-8 border-2 border-gray-400/60 rounded-full flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-4 h-4 text-gray-600/80\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M19 14l-7 7m0 0l-7-7m7 7V3\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 458,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                            lineNumber: 457,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                        lineNumber: 456,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                    lineNumber: 455,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                lineNumber: 454,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n        lineNumber: 389,\n        columnNumber: 5\n    }, this);\n}\n_s(SimpleHeroSection, \"/8IKySTmGZxFymijsBTHUGtuaQs=\");\n_c = SimpleHeroSection;\nvar _c;\n$RefreshReg$(_c, \"SimpleHeroSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/SimpleHeroSection.tsx\n"));

/***/ })

});