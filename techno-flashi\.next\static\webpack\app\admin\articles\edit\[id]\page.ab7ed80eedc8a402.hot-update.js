"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/articles/edit/[id]/page",{

/***/ "(app-pages-browser)/./src/components/AdvancedImageManager.tsx":
/*!*************************************************!*\
  !*** ./src/components/AdvancedImageManager.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdvancedImageManager)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _utils_dateUtils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/dateUtils */ \"(app-pages-browser)/./src/utils/dateUtils.ts\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction AdvancedImageManager(param) {\n    let { articleId, images, onImagesChange, onImageInsert, maxImages = 20, allowedTypes = [\n        'image/jpeg',\n        'image/png',\n        'image/webp',\n        'image/gif'\n    ], content = '', onContentChange } = param;\n    _s();\n    const [isUploading, setIsUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [uploadProgress, setUploadProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [draggedImageIndex, setDraggedImageIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [draggedOverEditor, setDraggedOverEditor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedImages, setSelectedImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [showImageReferences, setShowImageReferences] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // إنشاء مرجع للصورة\n    const generateImageReference = (index, imageData)=>{\n        return \"[صورة:\".concat(index + 1, \"]\");\n    };\n    // إدراج مرجع الصورة في النص (يدوياً فقط)\n    const insertImageReference = (imageData, index)=>{\n        const reference = generateImageReference(index, imageData);\n        if (onImageInsert) {\n            onImageInsert(reference, imageData);\n        }\n        // لا يتم إدراج تلقائي في المحتوى - يجب على المستخدم استخدام السحب والإفلات أو الأزرار\n        // هذا يمنع الإضافة العشوائية للصور\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success(\"تم إنشاء مرجع الصورة: \".concat(reference, \" - استخدم السحب والإفلات لوضعه في المكان المناسب\"));\n    };\n    // رفع الصور\n    const handleFileUpload = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AdvancedImageManager.useCallback[handleFileUpload]\": async (files)=>{\n            if (files.length === 0) return;\n            if (images.length + files.length > maxImages) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(\"يمكن رفع \".concat(maxImages, \" صورة كحد أقصى\"));\n                return;\n            }\n            setIsUploading(true);\n            const newImages = [];\n            try {\n                for(let i = 0; i < files.length; i++){\n                    const file = files[i];\n                    if (!allowedTypes.includes(file.type)) {\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(\"نوع الملف \".concat(file.type, \" غير مدعوم\"));\n                        continue;\n                    }\n                    const fileName = (0,_utils_dateUtils__WEBPACK_IMPORTED_MODULE_4__.createUniqueFileName)(file.name);\n                    const filePath = \"articles/\".concat(articleId || 'temp', \"/\").concat(fileName);\n                    // تحديث التقدم\n                    setUploadProgress({\n                        \"AdvancedImageManager.useCallback[handleFileUpload]\": (prev)=>({\n                                ...prev,\n                                [file.name]: 0\n                            })\n                    }[\"AdvancedImageManager.useCallback[handleFileUpload]\"]);\n                    // رفع الملف\n                    const { data: uploadData, error: uploadError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.storage.from('article-images').upload(filePath, file, {\n                        cacheControl: '3600',\n                        upsert: false\n                    });\n                    if (uploadError) {\n                        console.error('Upload error:', uploadError);\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(\"فشل رفع \".concat(file.name, \": \").concat(uploadError.message));\n                        continue;\n                    }\n                    // الحصول على URL العام\n                    const { data: urlData } = _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.storage.from('article-images').getPublicUrl(filePath);\n                    // إنشاء بيانات الصورة\n                    const imageData = {\n                        id: crypto.randomUUID(),\n                        image_url: urlData.publicUrl,\n                        image_path: filePath,\n                        alt_text: file.name.split('.')[0],\n                        caption: '',\n                        file_size: file.size,\n                        mime_type: file.type,\n                        display_order: images.length + newImages.length\n                    };\n                    // الحصول على أبعاد الصورة\n                    const img = new window.Image();\n                    img.onload = ({\n                        \"AdvancedImageManager.useCallback[handleFileUpload]\": ()=>{\n                            imageData.width = img.width;\n                            imageData.height = img.height;\n                        }\n                    })[\"AdvancedImageManager.useCallback[handleFileUpload]\"];\n                    img.src = urlData.publicUrl;\n                    newImages.push(imageData);\n                    setUploadProgress({\n                        \"AdvancedImageManager.useCallback[handleFileUpload]\": (prev)=>({\n                                ...prev,\n                                [file.name]: 100\n                            })\n                    }[\"AdvancedImageManager.useCallback[handleFileUpload]\"]);\n                    // حفظ في قاعدة البيانات إذا كان articleId متاحاً\n                    if (articleId) {\n                        const { error: dbError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.from('article_images').insert({\n                            article_id: articleId,\n                            image_url: urlData.publicUrl,\n                            image_path: filePath,\n                            alt_text: imageData.alt_text,\n                            caption: imageData.caption,\n                            file_size: imageData.file_size,\n                            mime_type: imageData.mime_type,\n                            width: imageData.width,\n                            height: imageData.height,\n                            display_order: imageData.display_order\n                        });\n                        if (dbError) {\n                            console.error('Database error:', dbError);\n                            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(\"فشل حفظ بيانات \".concat(file.name, \" في قاعدة البيانات\"));\n                        }\n                    }\n                }\n                // تحديث قائمة الصور\n                const updatedImages = [\n                    ...images,\n                    ...newImages\n                ];\n                onImagesChange(updatedImages);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success(\"تم رفع \".concat(newImages.length, \" صورة بنجاح\"));\n            } catch (error) {\n                console.error('Upload process error:', error);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error('حدث خطأ أثناء رفع الصور');\n            } finally{\n                setIsUploading(false);\n                setUploadProgress({});\n            }\n        }\n    }[\"AdvancedImageManager.useCallback[handleFileUpload]\"], [\n        images,\n        articleId,\n        maxImages,\n        allowedTypes,\n        onImagesChange\n    ]);\n    // حذف صورة\n    const handleDeleteImage = async (imageId)=>{\n        const imageToDelete = images.find((img)=>img.id === imageId);\n        if (!imageToDelete) return;\n        try {\n            // حذف من التخزين\n            const { error: storageError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.storage.from('article-images').remove([\n                imageToDelete.image_path\n            ]);\n            if (storageError) {\n                console.error('Storage deletion error:', storageError);\n            }\n            // حذف من قاعدة البيانات\n            if (articleId) {\n                const { error: dbError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.from('article_images').delete().eq('id', imageId);\n                if (dbError) {\n                    console.error('Database deletion error:', dbError);\n                }\n            }\n            // تحديث قائمة الصور\n            const updatedImages = images.filter((img)=>img.id !== imageId);\n            onImagesChange(updatedImages);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success('تم حذف الصورة بنجاح');\n        } catch (error) {\n            console.error('Delete error:', error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error('فشل حذف الصورة');\n        }\n    };\n    // إعادة ترتيب الصور\n    const handleReorderImages = (fromIndex, toIndex)=>{\n        const reorderedImages = [\n            ...images\n        ];\n        const [movedImage] = reorderedImages.splice(fromIndex, 1);\n        reorderedImages.splice(toIndex, 0, movedImage);\n        // تحديث display_order\n        const updatedImages = reorderedImages.map((img, index)=>({\n                ...img,\n                display_order: index\n            }));\n        onImagesChange(updatedImages);\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success('تم إعادة ترتيب الصور');\n    };\n    // معالج السحب والإفلات\n    const handleDragStart = (e, index)=>{\n        setDraggedImageIndex(index);\n        e.dataTransfer.effectAllowed = 'move';\n        e.dataTransfer.setData('text/plain', index.toString());\n    };\n    const handleDragOver = (e)=>{\n        e.preventDefault();\n        e.dataTransfer.dropEffect = 'move';\n    };\n    const handleDrop = (e, dropIndex)=>{\n        e.preventDefault();\n        if (draggedImageIndex !== null && draggedImageIndex !== dropIndex) {\n            handleReorderImages(draggedImageIndex, dropIndex);\n        }\n        setDraggedImageIndex(null);\n    };\n    // تحديث تسمية الصورة\n    const updateImageCaption = async (imageId, caption)=>{\n        const updatedImages = images.map((img)=>img.id === imageId ? {\n                ...img,\n                caption\n            } : img);\n        onImagesChange(updatedImages);\n        // تحديث في قاعدة البيانات\n        if (articleId) {\n            await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.from('article_images').update({\n                caption\n            }).eq('id', imageId);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-wrap items-center justify-between gap-4 p-4 bg-gray-800 rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    var _fileInputRef_current;\n                                    return (_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click();\n                                },\n                                disabled: isUploading || images.length >= maxImages,\n                                className: \"px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                                children: isUploading ? 'جاري الرفع...' : 'رفع صور'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\AdvancedImageManager.tsx\",\n                                lineNumber: 275,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-400 text-sm\",\n                                children: [\n                                    images.length,\n                                    \" / \",\n                                    maxImages,\n                                    \" صورة\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\AdvancedImageManager.tsx\",\n                                lineNumber: 283,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\AdvancedImageManager.tsx\",\n                        lineNumber: 274,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowImageReferences(!showImageReferences),\n                                className: \"px-3 py-1 bg-gray-700 text-white rounded text-sm hover:bg-gray-600 transition-colors\",\n                                children: showImageReferences ? 'إخفاء المراجع' : 'عرض المراجع'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\AdvancedImageManager.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 11\n                            }, this),\n                            selectedImages.size > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    selectedImages.forEach((imageId)=>handleDeleteImage(imageId));\n                                    setSelectedImages(new Set());\n                                },\n                                className: \"px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700 transition-colors\",\n                                children: [\n                                    \"حذف المحدد (\",\n                                    selectedImages.size,\n                                    \")\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\AdvancedImageManager.tsx\",\n                                lineNumber: 297,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\AdvancedImageManager.tsx\",\n                        lineNumber: 288,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\AdvancedImageManager.tsx\",\n                lineNumber: 273,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                ref: fileInputRef,\n                type: \"file\",\n                multiple: true,\n                accept: allowedTypes.join(','),\n                onChange: (e)=>e.target.files && handleFileUpload(e.target.files),\n                className: \"hidden\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\AdvancedImageManager.tsx\",\n                lineNumber: 311,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                children: images.map((image, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        draggable: true,\n                        onDragStart: (e)=>handleDragStart(e, index),\n                        onDragOver: handleDragOver,\n                        onDrop: (e)=>handleDrop(e, index),\n                        className: \"relative group bg-gray-800 rounded-lg overflow-hidden border-2 transition-all duration-200 \".concat(draggedImageIndex === index ? 'border-purple-500 opacity-50' : 'border-gray-700 hover:border-gray-600'),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-2 left-2 z-10 bg-purple-600 text-white text-xs px-2 py-1 rounded\",\n                                children: [\n                                    \"#\",\n                                    index + 1\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\AdvancedImageManager.tsx\",\n                                lineNumber: 334,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-2 right-2 z-10\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"checkbox\",\n                                    checked: selectedImages.has(image.id),\n                                    onChange: (e)=>{\n                                        const newSelected = new Set(selectedImages);\n                                        if (e.target.checked) {\n                                            newSelected.add(image.id);\n                                        } else {\n                                            newSelected.delete(image.id);\n                                        }\n                                        setSelectedImages(newSelected);\n                                    },\n                                    className: \"w-4 h-4 text-purple-600 bg-gray-700 border-gray-600 rounded focus:ring-purple-500\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\AdvancedImageManager.tsx\",\n                                    lineNumber: 340,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\AdvancedImageManager.tsx\",\n                                lineNumber: 339,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative aspect-video\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    src: image.image_url,\n                                    alt: image.alt_text || \"صورة \".concat(index + 1),\n                                    fill: true,\n                                    className: \"object-cover\",\n                                    sizes: \"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\AdvancedImageManager.tsx\",\n                                    lineNumber: 358,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\AdvancedImageManager.tsx\",\n                                lineNumber: 357,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-3 space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: image.caption || '',\n                                        onChange: (e)=>updateImageCaption(image.id, e.target.value),\n                                        placeholder: \"إضافة تسمية توضيحية...\",\n                                        className: \"w-full px-2 py-1 bg-gray-700 text-white text-sm rounded border border-gray-600 focus:border-purple-500 focus:outline-none\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\AdvancedImageManager.tsx\",\n                                        lineNumber: 369,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between text-xs text-gray-400\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: image.file_size ? \"\".concat((image.file_size / 1024).toFixed(1), \" KB\") : ''\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\AdvancedImageManager.tsx\",\n                                                lineNumber: 378,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: image.width && image.height ? \"\".concat(image.width, \"\\xd7\").concat(image.height) : ''\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\AdvancedImageManager.tsx\",\n                                                lineNumber: 379,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\AdvancedImageManager.tsx\",\n                                        lineNumber: 377,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\AdvancedImageManager.tsx\",\n                                lineNumber: 368,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>insertImageReference(image, index),\n                                        className: \"flex items-center gap-1 px-3 py-2 bg-purple-600 text-white text-sm rounded-lg hover:bg-purple-700 transition-colors shadow-lg\",\n                                        title: \"إدراج مرجع [صورة:\".concat(index + 1, \"]\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M12 6v6m0 0v6m0-6h6m-6 0H6\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\AdvancedImageManager.tsx\",\n                                                    lineNumber: 391,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\AdvancedImageManager.tsx\",\n                                                lineNumber: 390,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"إدراج\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\AdvancedImageManager.tsx\",\n                                        lineNumber: 385,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleDeleteImage(image.id),\n                                        className: \"flex items-center gap-1 px-3 py-2 bg-red-600 text-white text-sm rounded-lg hover:bg-red-700 transition-colors shadow-lg\",\n                                        title: \"حذف الصورة\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\AdvancedImageManager.tsx\",\n                                                    lineNumber: 402,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\AdvancedImageManager.tsx\",\n                                                lineNumber: 401,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"حذف\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\AdvancedImageManager.tsx\",\n                                        lineNumber: 396,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\AdvancedImageManager.tsx\",\n                                lineNumber: 384,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>insertImageReference(image, index),\n                                className: \"absolute bottom-2 right-2 w-8 h-8 bg-purple-600 text-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity shadow-lg hover:bg-purple-700\",\n                                title: \"إدراج [صورة:\".concat(index + 1, \"]\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-4 h-4\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M12 6v6m0 0v6m0-6h6m-6 0H6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\AdvancedImageManager.tsx\",\n                                        lineNumber: 415,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\AdvancedImageManager.tsx\",\n                                    lineNumber: 414,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\AdvancedImageManager.tsx\",\n                                lineNumber: 409,\n                                columnNumber: 13\n                            }, this),\n                            showImageReferences && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bottom-0 left-0 right-0 bg-purple-600/90 text-white text-xs p-2 text-center\",\n                                children: generateImageReference(index, image)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\AdvancedImageManager.tsx\",\n                                lineNumber: 421,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, image.id, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\AdvancedImageManager.tsx\",\n                        lineNumber: 323,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\AdvancedImageManager.tsx\",\n                lineNumber: 321,\n                columnNumber: 7\n            }, this),\n            images.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-12 text-gray-400\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"mx-auto h-12 w-12 mb-4\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\AdvancedImageManager.tsx\",\n                            lineNumber: 433,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\AdvancedImageManager.tsx\",\n                        lineNumber: 432,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg font-medium mb-2\",\n                        children: \"لا توجد صور مرفوعة\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\AdvancedImageManager.tsx\",\n                        lineNumber: 435,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm\",\n                        children: 'اضغط على \"رفع صور\" لإضافة صور للمقال'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\AdvancedImageManager.tsx\",\n                        lineNumber: 436,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\AdvancedImageManager.tsx\",\n                lineNumber: 431,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-blue-900/20 border border-blue-500/30 rounded-lg p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-blue-400 font-semibold mb-2\",\n                        children: \"كيفية استخدام مراجع الصور:\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\AdvancedImageManager.tsx\",\n                        lineNumber: 442,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"text-blue-300 text-sm space-y-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: '• اضغط على \"إدراج مرجع\" لإضافة مرجع الصورة في النص'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\AdvancedImageManager.tsx\",\n                                lineNumber: 444,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• استخدم المراجع مثل [صورة:1] في أي مكان في النص\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\AdvancedImageManager.tsx\",\n                                lineNumber: 445,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• سيتم عرض الصورة المقابلة في المكان المحدد\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\AdvancedImageManager.tsx\",\n                                lineNumber: 446,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• يمكن إعادة ترتيب الصور بالسحب والإفلات\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\AdvancedImageManager.tsx\",\n                                lineNumber: 447,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\AdvancedImageManager.tsx\",\n                        lineNumber: 443,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\AdvancedImageManager.tsx\",\n                lineNumber: 441,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\AdvancedImageManager.tsx\",\n        lineNumber: 271,\n        columnNumber: 5\n    }, this);\n}\n_s(AdvancedImageManager, \"h7Bg8TD211fMzkfF7ifMgLQKTNs=\");\n_c = AdvancedImageManager;\nvar _c;\n$RefreshReg$(_c, \"AdvancedImageManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL0FkdmFuY2VkSW1hZ2VNYW5hZ2VyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBRWlFO0FBQ3ZCO0FBQ047QUFDcUI7QUFDMUI7QUEwQmhCLFNBQVNPLHFCQUFxQixLQVNqQjtRQVRpQixFQUMzQ0MsU0FBUyxFQUNUQyxNQUFNLEVBQ05DLGNBQWMsRUFDZEMsYUFBYSxFQUNiQyxZQUFZLEVBQUUsRUFDZEMsZUFBZTtRQUFDO1FBQWM7UUFBYTtRQUFjO0tBQVksRUFDckVDLFVBQVUsRUFBRSxFQUNaQyxlQUFlLEVBQ1csR0FUaUI7O0lBVTNDLE1BQU0sQ0FBQ0MsYUFBYUMsZUFBZSxHQUFHakIsK0NBQVFBLENBQUM7SUFDL0MsTUFBTSxDQUFDa0IsZ0JBQWdCQyxrQkFBa0IsR0FBR25CLCtDQUFRQSxDQUE0QixDQUFDO0lBQ2pGLE1BQU0sQ0FBQ29CLG1CQUFtQkMscUJBQXFCLEdBQUdyQiwrQ0FBUUEsQ0FBZ0I7SUFDMUUsTUFBTSxDQUFDc0IsbUJBQW1CQyxxQkFBcUIsR0FBR3ZCLCtDQUFRQSxDQUFDO0lBQzNELE1BQU0sQ0FBQ3dCLGdCQUFnQkMsa0JBQWtCLEdBQUd6QiwrQ0FBUUEsQ0FBYyxJQUFJMEI7SUFDdEUsTUFBTSxDQUFDQyxxQkFBcUJDLHVCQUF1QixHQUFHNUIsK0NBQVFBLENBQUM7SUFDL0QsTUFBTTZCLGVBQWU1Qiw2Q0FBTUEsQ0FBbUI7SUFFOUMsb0JBQW9CO0lBQ3BCLE1BQU02Qix5QkFBeUIsQ0FBQ0MsT0FBZUM7UUFDN0MsT0FBTyxTQUFtQixPQUFWRCxRQUFRLEdBQUU7SUFDNUI7SUFFQSx5Q0FBeUM7SUFDekMsTUFBTUUsdUJBQXVCLENBQUNELFdBQXNCRDtRQUNsRCxNQUFNRyxZQUFZSix1QkFBdUJDLE9BQU9DO1FBQ2hELElBQUlyQixlQUFlO1lBQ2pCQSxjQUFjdUIsV0FBV0Y7UUFDM0I7UUFFQSxzRkFBc0Y7UUFDdEYsbUNBQW1DO1FBRW5DNUIsdURBQUtBLENBQUMrQixPQUFPLENBQUMseUJBQW1DLE9BQVZELFdBQVU7SUFDbkQ7SUFFQSxZQUFZO0lBQ1osTUFBTUUsbUJBQW1CbEMsa0RBQVdBOzhEQUFDLE9BQU9tQztZQUMxQyxJQUFJQSxNQUFNQyxNQUFNLEtBQUssR0FBRztZQUV4QixJQUFJN0IsT0FBTzZCLE1BQU0sR0FBR0QsTUFBTUMsTUFBTSxHQUFHMUIsV0FBVztnQkFDNUNSLHVEQUFLQSxDQUFDbUMsS0FBSyxDQUFDLFlBQXNCLE9BQVYzQixXQUFVO2dCQUNsQztZQUNGO1lBRUFLLGVBQWU7WUFDZixNQUFNdUIsWUFBeUIsRUFBRTtZQUVqQyxJQUFJO2dCQUNGLElBQUssSUFBSUMsSUFBSSxHQUFHQSxJQUFJSixNQUFNQyxNQUFNLEVBQUVHLElBQUs7b0JBQ3JDLE1BQU1DLE9BQU9MLEtBQUssQ0FBQ0ksRUFBRTtvQkFFckIsSUFBSSxDQUFDNUIsYUFBYThCLFFBQVEsQ0FBQ0QsS0FBS0UsSUFBSSxHQUFHO3dCQUNyQ3hDLHVEQUFLQSxDQUFDbUMsS0FBSyxDQUFDLGFBQXVCLE9BQVZHLEtBQUtFLElBQUksRUFBQzt3QkFDbkM7b0JBQ0Y7b0JBRUEsTUFBTUMsV0FBV3hDLHNFQUFvQkEsQ0FBQ3FDLEtBQUtJLElBQUk7b0JBQy9DLE1BQU1DLFdBQVcsWUFBbUNGLE9BQXZCckMsYUFBYSxRQUFPLEtBQVksT0FBVHFDO29CQUVwRCxlQUFlO29CQUNmMUI7OEVBQWtCNkIsQ0FBQUEsT0FBUztnQ0FBRSxHQUFHQSxJQUFJO2dDQUFFLENBQUNOLEtBQUtJLElBQUksQ0FBQyxFQUFFOzRCQUFFOztvQkFFckQsWUFBWTtvQkFDWixNQUFNLEVBQUVHLE1BQU1DLFVBQVUsRUFBRVgsT0FBT1ksV0FBVyxFQUFFLEdBQUcsTUFBTWhELG1EQUFRQSxDQUFDaUQsT0FBTyxDQUNwRUMsSUFBSSxDQUFDLGtCQUNMQyxNQUFNLENBQUNQLFVBQVVMLE1BQU07d0JBQ3RCYSxjQUFjO3dCQUNkQyxRQUFRO29CQUNWO29CQUVGLElBQUlMLGFBQWE7d0JBQ2ZNLFFBQVFsQixLQUFLLENBQUMsaUJBQWlCWTt3QkFDL0IvQyx1REFBS0EsQ0FBQ21DLEtBQUssQ0FBQyxXQUF5QlksT0FBZFQsS0FBS0ksSUFBSSxFQUFDLE1BQXdCLE9BQXBCSyxZQUFZTyxPQUFPO3dCQUN4RDtvQkFDRjtvQkFFQSx1QkFBdUI7b0JBQ3ZCLE1BQU0sRUFBRVQsTUFBTVUsT0FBTyxFQUFFLEdBQUd4RCxtREFBUUEsQ0FBQ2lELE9BQU8sQ0FDdkNDLElBQUksQ0FBQyxrQkFDTE8sWUFBWSxDQUFDYjtvQkFFaEIsc0JBQXNCO29CQUN0QixNQUFNZixZQUF1Qjt3QkFDM0I2QixJQUFJQyxPQUFPQyxVQUFVO3dCQUNyQkMsV0FBV0wsUUFBUU0sU0FBUzt3QkFDNUJDLFlBQVluQjt3QkFDWm9CLFVBQVV6QixLQUFLSSxJQUFJLENBQUNzQixLQUFLLENBQUMsSUFBSSxDQUFDLEVBQUU7d0JBQ2pDQyxTQUFTO3dCQUNUQyxXQUFXNUIsS0FBSzZCLElBQUk7d0JBQ3BCQyxXQUFXOUIsS0FBS0UsSUFBSTt3QkFDcEI2QixlQUFlaEUsT0FBTzZCLE1BQU0sR0FBR0UsVUFBVUYsTUFBTTtvQkFDakQ7b0JBRUEsMEJBQTBCO29CQUMxQixNQUFNb0MsTUFBTSxJQUFJQyxPQUFPckUsS0FBSztvQkFDNUJvRSxJQUFJRSxNQUFNOzhFQUFHOzRCQUNYNUMsVUFBVTZDLEtBQUssR0FBR0gsSUFBSUcsS0FBSzs0QkFDM0I3QyxVQUFVOEMsTUFBTSxHQUFHSixJQUFJSSxNQUFNO3dCQUMvQjs7b0JBQ0FKLElBQUlLLEdBQUcsR0FBR3BCLFFBQVFNLFNBQVM7b0JBRTNCekIsVUFBVXdDLElBQUksQ0FBQ2hEO29CQUNmYjs4RUFBa0I2QixDQUFBQSxPQUFTO2dDQUFFLEdBQUdBLElBQUk7Z0NBQUUsQ0FBQ04sS0FBS0ksSUFBSSxDQUFDLEVBQUU7NEJBQUk7O29CQUV2RCxpREFBaUQ7b0JBQ2pELElBQUl0QyxXQUFXO3dCQUNiLE1BQU0sRUFBRStCLE9BQU8wQyxPQUFPLEVBQUUsR0FBRyxNQUFNOUUsbURBQVFBLENBQ3RDa0QsSUFBSSxDQUFDLGtCQUNMNkIsTUFBTSxDQUFDOzRCQUNOQyxZQUFZM0U7NEJBQ1p3RCxXQUFXTCxRQUFRTSxTQUFTOzRCQUM1QkMsWUFBWW5COzRCQUNab0IsVUFBVW5DLFVBQVVtQyxRQUFROzRCQUM1QkUsU0FBU3JDLFVBQVVxQyxPQUFPOzRCQUMxQkMsV0FBV3RDLFVBQVVzQyxTQUFTOzRCQUM5QkUsV0FBV3hDLFVBQVV3QyxTQUFTOzRCQUM5QkssT0FBTzdDLFVBQVU2QyxLQUFLOzRCQUN0QkMsUUFBUTlDLFVBQVU4QyxNQUFNOzRCQUN4QkwsZUFBZXpDLFVBQVV5QyxhQUFhO3dCQUN4Qzt3QkFFRixJQUFJUSxTQUFTOzRCQUNYeEIsUUFBUWxCLEtBQUssQ0FBQyxtQkFBbUIwQzs0QkFDakM3RSx1REFBS0EsQ0FBQ21DLEtBQUssQ0FBQyxrQkFBNEIsT0FBVkcsS0FBS0ksSUFBSSxFQUFDO3dCQUMxQztvQkFDRjtnQkFDRjtnQkFFQSxvQkFBb0I7Z0JBQ3BCLE1BQU1zQyxnQkFBZ0I7dUJBQUkzRTt1QkFBVytCO2lCQUFVO2dCQUMvQzlCLGVBQWUwRTtnQkFFZmhGLHVEQUFLQSxDQUFDK0IsT0FBTyxDQUFDLFVBQTJCLE9BQWpCSyxVQUFVRixNQUFNLEVBQUM7WUFFM0MsRUFBRSxPQUFPQyxPQUFZO2dCQUNuQmtCLFFBQVFsQixLQUFLLENBQUMseUJBQXlCQTtnQkFDdkNuQyx1REFBS0EsQ0FBQ21DLEtBQUssQ0FBQztZQUNkLFNBQVU7Z0JBQ1J0QixlQUFlO2dCQUNmRSxrQkFBa0IsQ0FBQztZQUNyQjtRQUNGOzZEQUFHO1FBQUNWO1FBQVFEO1FBQVdJO1FBQVdDO1FBQWNIO0tBQWU7SUFFL0QsV0FBVztJQUNYLE1BQU0yRSxvQkFBb0IsT0FBT0M7UUFDL0IsTUFBTUMsZ0JBQWdCOUUsT0FBTytFLElBQUksQ0FBQ2QsQ0FBQUEsTUFBT0EsSUFBSWIsRUFBRSxLQUFLeUI7UUFDcEQsSUFBSSxDQUFDQyxlQUFlO1FBRXBCLElBQUk7WUFDRixpQkFBaUI7WUFDakIsTUFBTSxFQUFFaEQsT0FBT2tELFlBQVksRUFBRSxHQUFHLE1BQU10RixtREFBUUEsQ0FBQ2lELE9BQU8sQ0FDbkRDLElBQUksQ0FBQyxrQkFDTHFDLE1BQU0sQ0FBQztnQkFBQ0gsY0FBY3JCLFVBQVU7YUFBQztZQUVwQyxJQUFJdUIsY0FBYztnQkFDaEJoQyxRQUFRbEIsS0FBSyxDQUFDLDJCQUEyQmtEO1lBQzNDO1lBRUEsd0JBQXdCO1lBQ3hCLElBQUlqRixXQUFXO2dCQUNiLE1BQU0sRUFBRStCLE9BQU8wQyxPQUFPLEVBQUUsR0FBRyxNQUFNOUUsbURBQVFBLENBQ3RDa0QsSUFBSSxDQUFDLGtCQUNMc0MsTUFBTSxHQUNOQyxFQUFFLENBQUMsTUFBTU47Z0JBRVosSUFBSUwsU0FBUztvQkFDWHhCLFFBQVFsQixLQUFLLENBQUMsNEJBQTRCMEM7Z0JBQzVDO1lBQ0Y7WUFFQSxvQkFBb0I7WUFDcEIsTUFBTUcsZ0JBQWdCM0UsT0FBT29GLE1BQU0sQ0FBQ25CLENBQUFBLE1BQU9BLElBQUliLEVBQUUsS0FBS3lCO1lBQ3RENUUsZUFBZTBFO1lBRWZoRix1REFBS0EsQ0FBQytCLE9BQU8sQ0FBQztRQUVoQixFQUFFLE9BQU9JLE9BQVk7WUFDbkJrQixRQUFRbEIsS0FBSyxDQUFDLGlCQUFpQkE7WUFDL0JuQyx1REFBS0EsQ0FBQ21DLEtBQUssQ0FBQztRQUNkO0lBQ0Y7SUFFQSxvQkFBb0I7SUFDcEIsTUFBTXVELHNCQUFzQixDQUFDQyxXQUFtQkM7UUFDOUMsTUFBTUMsa0JBQWtCO2VBQUl4RjtTQUFPO1FBQ25DLE1BQU0sQ0FBQ3lGLFdBQVcsR0FBR0QsZ0JBQWdCRSxNQUFNLENBQUNKLFdBQVc7UUFDdkRFLGdCQUFnQkUsTUFBTSxDQUFDSCxTQUFTLEdBQUdFO1FBRW5DLHNCQUFzQjtRQUN0QixNQUFNZCxnQkFBZ0JhLGdCQUFnQkcsR0FBRyxDQUFDLENBQUMxQixLQUFLM0MsUUFBVztnQkFDekQsR0FBRzJDLEdBQUc7Z0JBQ05ELGVBQWUxQztZQUNqQjtRQUVBckIsZUFBZTBFO1FBQ2ZoRix1REFBS0EsQ0FBQytCLE9BQU8sQ0FBQztJQUNoQjtJQUVBLHVCQUF1QjtJQUN2QixNQUFNa0Usa0JBQWtCLENBQUNDLEdBQW9CdkU7UUFDM0NWLHFCQUFxQlU7UUFDckJ1RSxFQUFFQyxZQUFZLENBQUNDLGFBQWEsR0FBRztRQUMvQkYsRUFBRUMsWUFBWSxDQUFDRSxPQUFPLENBQUMsY0FBYzFFLE1BQU0yRSxRQUFRO0lBQ3JEO0lBRUEsTUFBTUMsaUJBQWlCLENBQUNMO1FBQ3RCQSxFQUFFTSxjQUFjO1FBQ2hCTixFQUFFQyxZQUFZLENBQUNNLFVBQVUsR0FBRztJQUM5QjtJQUVBLE1BQU1DLGFBQWEsQ0FBQ1IsR0FBb0JTO1FBQ3RDVCxFQUFFTSxjQUFjO1FBRWhCLElBQUl4RixzQkFBc0IsUUFBUUEsc0JBQXNCMkYsV0FBVztZQUNqRWpCLG9CQUFvQjFFLG1CQUFtQjJGO1FBQ3pDO1FBRUExRixxQkFBcUI7SUFDdkI7SUFFQSxxQkFBcUI7SUFDckIsTUFBTTJGLHFCQUFxQixPQUFPMUIsU0FBaUJqQjtRQUNqRCxNQUFNZSxnQkFBZ0IzRSxPQUFPMkYsR0FBRyxDQUFDMUIsQ0FBQUEsTUFDL0JBLElBQUliLEVBQUUsS0FBS3lCLFVBQVU7Z0JBQUUsR0FBR1osR0FBRztnQkFBRUw7WUFBUSxJQUFJSztRQUU3Q2hFLGVBQWUwRTtRQUVmLDBCQUEwQjtRQUMxQixJQUFJNUUsV0FBVztZQUNiLE1BQU1MLG1EQUFRQSxDQUNYa0QsSUFBSSxDQUFDLGtCQUNMNEQsTUFBTSxDQUFDO2dCQUFFNUM7WUFBUSxHQUNqQnVCLEVBQUUsQ0FBQyxNQUFNTjtRQUNkO0lBQ0Y7SUFFQSxxQkFDRSw4REFBQzRCO1FBQUlDLFdBQVU7OzBCQUViLDhEQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0M7Z0NBQ0NDLFNBQVM7d0NBQU14Rjs0Q0FBQUEsd0JBQUFBLGFBQWF5RixPQUFPLGNBQXBCekYsNENBQUFBLHNCQUFzQjBGLEtBQUs7O2dDQUMxQ0MsVUFBVXhHLGVBQWVQLE9BQU82QixNQUFNLElBQUkxQjtnQ0FDMUN1RyxXQUFVOzBDQUVUbkcsY0FBYyxrQkFBa0I7Ozs7OzswQ0FHbkMsOERBQUN5RztnQ0FBS04sV0FBVTs7b0NBQ2IxRyxPQUFPNkIsTUFBTTtvQ0FBQztvQ0FBSTFCO29DQUFVOzs7Ozs7Ozs7Ozs7O2tDQUlqQyw4REFBQ3NHO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0M7Z0NBQ0NDLFNBQVMsSUFBTXpGLHVCQUF1QixDQUFDRDtnQ0FDdkN3RixXQUFVOzBDQUVUeEYsc0JBQXNCLGtCQUFrQjs7Ozs7OzRCQUcxQ0gsZUFBZStDLElBQUksR0FBRyxtQkFDckIsOERBQUM2QztnQ0FDQ0MsU0FBUztvQ0FDUDdGLGVBQWVrRyxPQUFPLENBQUNwQyxDQUFBQSxVQUFXRCxrQkFBa0JDO29DQUNwRDdELGtCQUFrQixJQUFJQztnQ0FDeEI7Z0NBQ0F5RixXQUFVOztvQ0FDWDtvQ0FDYzNGLGVBQWUrQyxJQUFJO29DQUFDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQU96Qyw4REFBQ29EO2dCQUNDQyxLQUFLL0Y7Z0JBQ0xlLE1BQUs7Z0JBQ0xpRixRQUFRO2dCQUNSQyxRQUFRakgsYUFBYWtILElBQUksQ0FBQztnQkFDMUJDLFVBQVUsQ0FBQzFCLElBQU1BLEVBQUUyQixNQUFNLENBQUM1RixLQUFLLElBQUlELGlCQUFpQmtFLEVBQUUyQixNQUFNLENBQUM1RixLQUFLO2dCQUNsRThFLFdBQVU7Ozs7OzswQkFJWiw4REFBQ0Q7Z0JBQUlDLFdBQVU7MEJBQ1oxRyxPQUFPMkYsR0FBRyxDQUFDLENBQUM4QixPQUFPbkcsc0JBQ2xCLDhEQUFDbUY7d0JBRUNpQixTQUFTO3dCQUNUQyxhQUFhLENBQUM5QixJQUFNRCxnQkFBZ0JDLEdBQUd2RTt3QkFDdkNzRyxZQUFZMUI7d0JBQ1oyQixRQUFRLENBQUNoQyxJQUFNUSxXQUFXUixHQUFHdkU7d0JBQzdCb0YsV0FBVyw4RkFFVixPQURDL0Ysc0JBQXNCVyxRQUFRLGlDQUFpQzs7MENBSWpFLDhEQUFDbUY7Z0NBQUlDLFdBQVU7O29DQUFnRjtvQ0FDM0ZwRixRQUFROzs7Ozs7OzBDQUlaLDhEQUFDbUY7Z0NBQUlDLFdBQVU7MENBQ2IsNEVBQUNRO29DQUNDL0UsTUFBSztvQ0FDTDJGLFNBQVMvRyxlQUFlZ0gsR0FBRyxDQUFDTixNQUFNckUsRUFBRTtvQ0FDcENtRSxVQUFVLENBQUMxQjt3Q0FDVCxNQUFNbUMsY0FBYyxJQUFJL0csSUFBSUY7d0NBQzVCLElBQUk4RSxFQUFFMkIsTUFBTSxDQUFDTSxPQUFPLEVBQUU7NENBQ3BCRSxZQUFZQyxHQUFHLENBQUNSLE1BQU1yRSxFQUFFO3dDQUMxQixPQUFPOzRDQUNMNEUsWUFBWTlDLE1BQU0sQ0FBQ3VDLE1BQU1yRSxFQUFFO3dDQUM3Qjt3Q0FDQXBDLGtCQUFrQmdIO29DQUNwQjtvQ0FDQXRCLFdBQVU7Ozs7Ozs7Ozs7OzBDQUtkLDhEQUFDRDtnQ0FBSUMsV0FBVTswQ0FDYiw0RUFBQzdHLGtEQUFLQTtvQ0FDSnlFLEtBQUttRCxNQUFNbEUsU0FBUztvQ0FDcEIyRSxLQUFLVCxNQUFNL0QsUUFBUSxJQUFJLFFBQWtCLE9BQVZwQyxRQUFRO29DQUN2QzZHLElBQUk7b0NBQ0p6QixXQUFVO29DQUNWMEIsT0FBTTs7Ozs7Ozs7Ozs7MENBS1YsOERBQUMzQjtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNRO3dDQUNDL0UsTUFBSzt3Q0FDTGtHLE9BQU9aLE1BQU03RCxPQUFPLElBQUk7d0NBQ3hCMkQsVUFBVSxDQUFDMUIsSUFBTVUsbUJBQW1Ca0IsTUFBTXJFLEVBQUUsRUFBRXlDLEVBQUUyQixNQUFNLENBQUNhLEtBQUs7d0NBQzVEQyxhQUFZO3dDQUNaNUIsV0FBVTs7Ozs7O2tEQUdaLDhEQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNNOzBEQUFNUyxNQUFNNUQsU0FBUyxHQUFHLEdBQXVDLE9BQXBDLENBQUM0RCxNQUFNNUQsU0FBUyxHQUFHLElBQUcsRUFBRzBFLE9BQU8sQ0FBQyxJQUFHLFNBQU87Ozs7OzswREFDdkUsOERBQUN2QjswREFBTVMsTUFBTXJELEtBQUssSUFBSXFELE1BQU1wRCxNQUFNLEdBQUcsR0FBa0JvRCxPQUFmQSxNQUFNckQsS0FBSyxFQUFDLFFBQWdCLE9BQWJxRCxNQUFNcEQsTUFBTSxJQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBSzVFLDhEQUFDb0M7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDQzt3Q0FDQ0MsU0FBUyxJQUFNcEYscUJBQXFCaUcsT0FBT25HO3dDQUMzQ29GLFdBQVU7d0NBQ1Y4QixPQUFPLG9CQUE4QixPQUFWbEgsUUFBUSxHQUFFOzswREFFckMsOERBQUNtSDtnREFBSS9CLFdBQVU7Z0RBQVV5QixNQUFLO2dEQUFPTyxRQUFPO2dEQUFlQyxTQUFROzBEQUNqRSw0RUFBQ0M7b0RBQUtDLGVBQWM7b0RBQVFDLGdCQUFlO29EQUFRQyxhQUFhO29EQUFHQyxHQUFFOzs7Ozs7Ozs7Ozs0Q0FDakU7Ozs7Ozs7a0RBSVIsOERBQUNyQzt3Q0FDQ0MsU0FBUyxJQUFNaEMsa0JBQWtCNkMsTUFBTXJFLEVBQUU7d0NBQ3pDc0QsV0FBVTt3Q0FDVjhCLE9BQU07OzBEQUVOLDhEQUFDQztnREFBSS9CLFdBQVU7Z0RBQVV5QixNQUFLO2dEQUFPTyxRQUFPO2dEQUFlQyxTQUFROzBEQUNqRSw0RUFBQ0M7b0RBQUtDLGVBQWM7b0RBQVFDLGdCQUFlO29EQUFRQyxhQUFhO29EQUFHQyxHQUFFOzs7Ozs7Ozs7Ozs0Q0FDakU7Ozs7Ozs7Ozs7Ozs7MENBTVYsOERBQUNyQztnQ0FDQ0MsU0FBUyxJQUFNcEYscUJBQXFCaUcsT0FBT25HO2dDQUMzQ29GLFdBQVU7Z0NBQ1Y4QixPQUFPLGVBQXlCLE9BQVZsSCxRQUFRLEdBQUU7MENBRWhDLDRFQUFDbUg7b0NBQUkvQixXQUFVO29DQUFVeUIsTUFBSztvQ0FBT08sUUFBTztvQ0FBZUMsU0FBUTs4Q0FDakUsNEVBQUNDO3dDQUFLQyxlQUFjO3dDQUFRQyxnQkFBZTt3Q0FBUUMsYUFBYTt3Q0FBR0MsR0FBRTs7Ozs7Ozs7Ozs7Ozs7Ozs0QkFLeEU5SCxxQ0FDQyw4REFBQ3VGO2dDQUFJQyxXQUFVOzBDQUNackYsdUJBQXVCQyxPQUFPbUc7Ozs7Ozs7dUJBbEc5QkEsTUFBTXJFLEVBQUU7Ozs7Ozs7Ozs7WUEwR2xCcEQsT0FBTzZCLE1BQU0sS0FBSyxtQkFDakIsOERBQUM0RTtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUMrQjt3QkFBSS9CLFdBQVU7d0JBQXlCeUIsTUFBSzt3QkFBT08sUUFBTzt3QkFBZUMsU0FBUTtrQ0FDaEYsNEVBQUNDOzRCQUFLQyxlQUFjOzRCQUFRQyxnQkFBZTs0QkFBUUMsYUFBYTs0QkFBR0MsR0FBRTs7Ozs7Ozs7Ozs7a0NBRXZFLDhEQUFDQzt3QkFBRXZDLFdBQVU7a0NBQTJCOzs7Ozs7a0NBQ3hDLDhEQUFDdUM7d0JBQUV2QyxXQUFVO2tDQUFVOzs7Ozs7Ozs7Ozs7MEJBSzNCLDhEQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUN3Qzt3QkFBR3hDLFdBQVU7a0NBQW1DOzs7Ozs7a0NBQ2pELDhEQUFDeUM7d0JBQUd6QyxXQUFVOzswQ0FDWiw4REFBQzBDOzBDQUFHOzs7Ozs7MENBQ0osOERBQUNBOzBDQUFHOzs7Ozs7MENBQ0osOERBQUNBOzBDQUFHOzs7Ozs7MENBQ0osOERBQUNBOzBDQUFHOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFLZDtHQW5hd0J0SjtLQUFBQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxpc21haWxcXERvd25sb2Fkc1xcMTExMTExMTExMTExMTFcXHRlY2huby1mbGFzaGlcXHNyY1xcY29tcG9uZW50c1xcQWR2YW5jZWRJbWFnZU1hbmFnZXIudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZVJlZiwgdXNlQ2FsbGJhY2ssIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IHN1cGFiYXNlIH0gZnJvbSAnQC9saWIvc3VwYWJhc2UnO1xuaW1wb3J0IHRvYXN0IGZyb20gJ3JlYWN0LWhvdC10b2FzdCc7XG5pbXBvcnQgeyBjcmVhdGVVbmlxdWVGaWxlTmFtZSB9IGZyb20gJ0AvdXRpbHMvZGF0ZVV0aWxzJztcbmltcG9ydCBJbWFnZSBmcm9tICduZXh0L2ltYWdlJztcblxuaW50ZXJmYWNlIEltYWdlRGF0YSB7XG4gIGlkOiBzdHJpbmc7XG4gIGltYWdlX3VybDogc3RyaW5nO1xuICBpbWFnZV9wYXRoOiBzdHJpbmc7XG4gIGFsdF90ZXh0Pzogc3RyaW5nO1xuICBjYXB0aW9uPzogc3RyaW5nO1xuICBmaWxlX3NpemU/OiBudW1iZXI7XG4gIG1pbWVfdHlwZT86IHN0cmluZztcbiAgd2lkdGg/OiBudW1iZXI7XG4gIGhlaWdodD86IG51bWJlcjtcbiAgZGlzcGxheV9vcmRlcjogbnVtYmVyO1xufVxuXG5pbnRlcmZhY2UgQWR2YW5jZWRJbWFnZU1hbmFnZXJQcm9wcyB7XG4gIGFydGljbGVJZD86IHN0cmluZztcbiAgaW1hZ2VzOiBJbWFnZURhdGFbXTtcbiAgb25JbWFnZXNDaGFuZ2U6IChpbWFnZXM6IEltYWdlRGF0YVtdKSA9PiB2b2lkO1xuICBvbkltYWdlSW5zZXJ0PzogKGltYWdlUmVmZXJlbmNlOiBzdHJpbmcsIGltYWdlRGF0YTogSW1hZ2VEYXRhKSA9PiB2b2lkO1xuICBtYXhJbWFnZXM/OiBudW1iZXI7XG4gIGFsbG93ZWRUeXBlcz86IHN0cmluZ1tdO1xuICBjb250ZW50Pzogc3RyaW5nO1xuICBvbkNvbnRlbnRDaGFuZ2U/OiAoY29udGVudDogc3RyaW5nKSA9PiB2b2lkO1xufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBBZHZhbmNlZEltYWdlTWFuYWdlcih7XG4gIGFydGljbGVJZCxcbiAgaW1hZ2VzLFxuICBvbkltYWdlc0NoYW5nZSxcbiAgb25JbWFnZUluc2VydCxcbiAgbWF4SW1hZ2VzID0gMjAsXG4gIGFsbG93ZWRUeXBlcyA9IFsnaW1hZ2UvanBlZycsICdpbWFnZS9wbmcnLCAnaW1hZ2Uvd2VicCcsICdpbWFnZS9naWYnXSxcbiAgY29udGVudCA9ICcnLFxuICBvbkNvbnRlbnRDaGFuZ2Vcbn06IEFkdmFuY2VkSW1hZ2VNYW5hZ2VyUHJvcHMpIHtcbiAgY29uc3QgW2lzVXBsb2FkaW5nLCBzZXRJc1VwbG9hZGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFt1cGxvYWRQcm9ncmVzcywgc2V0VXBsb2FkUHJvZ3Jlc3NdID0gdXNlU3RhdGU8eyBba2V5OiBzdHJpbmddOiBudW1iZXIgfT4oe30pO1xuICBjb25zdCBbZHJhZ2dlZEltYWdlSW5kZXgsIHNldERyYWdnZWRJbWFnZUluZGV4XSA9IHVzZVN0YXRlPG51bWJlciB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbZHJhZ2dlZE92ZXJFZGl0b3IsIHNldERyYWdnZWRPdmVyRWRpdG9yXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW3NlbGVjdGVkSW1hZ2VzLCBzZXRTZWxlY3RlZEltYWdlc10gPSB1c2VTdGF0ZTxTZXQ8c3RyaW5nPj4obmV3IFNldCgpKTtcbiAgY29uc3QgW3Nob3dJbWFnZVJlZmVyZW5jZXMsIHNldFNob3dJbWFnZVJlZmVyZW5jZXNdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBmaWxlSW5wdXRSZWYgPSB1c2VSZWY8SFRNTElucHV0RWxlbWVudD4obnVsbCk7XG5cbiAgLy8g2KXZhti02KfYoSDZhdix2KzYuSDZhNmE2LXZiNix2KlcbiAgY29uc3QgZ2VuZXJhdGVJbWFnZVJlZmVyZW5jZSA9IChpbmRleDogbnVtYmVyLCBpbWFnZURhdGE6IEltYWdlRGF0YSkgPT4ge1xuICAgIHJldHVybiBgW9i12YjYsdipOiR7aW5kZXggKyAxfV1gO1xuICB9O1xuXG4gIC8vINil2K/Ysdin2Kwg2YXYsdis2Lkg2KfZhNi12YjYsdipINmB2Yog2KfZhNmG2LUgKNmK2K/ZiNmK2KfZiyDZgdmC2LcpXG4gIGNvbnN0IGluc2VydEltYWdlUmVmZXJlbmNlID0gKGltYWdlRGF0YTogSW1hZ2VEYXRhLCBpbmRleDogbnVtYmVyKSA9PiB7XG4gICAgY29uc3QgcmVmZXJlbmNlID0gZ2VuZXJhdGVJbWFnZVJlZmVyZW5jZShpbmRleCwgaW1hZ2VEYXRhKTtcbiAgICBpZiAob25JbWFnZUluc2VydCkge1xuICAgICAgb25JbWFnZUluc2VydChyZWZlcmVuY2UsIGltYWdlRGF0YSk7XG4gICAgfVxuXG4gICAgLy8g2YTYpyDZitiq2YUg2KXYr9ix2KfYrCDYqtmE2YLYp9im2Yog2YHZiiDYp9mE2YXYrdiq2YjZiSAtINmK2KzYqCDYudmE2Ykg2KfZhNmF2LPYqtiu2K/ZhSDYp9iz2KrYrtiv2KfZhSDYp9mE2LPYrdioINmI2KfZhNil2YHZhNin2Kog2KPZiCDYp9mE2KPYstix2KfYsVxuICAgIC8vINmH2LDYpyDZitmF2YbYuSDYp9mE2KXYttin2YHYqSDYp9mE2LnYtNmI2KfYptmK2Kkg2YTZhNi12YjYsVxuXG4gICAgdG9hc3Quc3VjY2Vzcyhg2KrZhSDYpdmG2LTYp9ihINmF2LHYrNi5INin2YTYtdmI2LHYqTogJHtyZWZlcmVuY2V9IC0g2KfYs9iq2K7Yr9mFINin2YTYs9it2Kgg2YjYp9mE2KXZgdmE2KfYqiDZhNmI2LbYudmHINmB2Yog2KfZhNmF2YPYp9mGINin2YTZhdmG2KfYs9ioYCk7XG4gIH07XG5cbiAgLy8g2LHZgdi5INin2YTYtdmI2LFcbiAgY29uc3QgaGFuZGxlRmlsZVVwbG9hZCA9IHVzZUNhbGxiYWNrKGFzeW5jIChmaWxlczogRmlsZUxpc3QpID0+IHtcbiAgICBpZiAoZmlsZXMubGVuZ3RoID09PSAwKSByZXR1cm47XG5cbiAgICBpZiAoaW1hZ2VzLmxlbmd0aCArIGZpbGVzLmxlbmd0aCA+IG1heEltYWdlcykge1xuICAgICAgdG9hc3QuZXJyb3IoYNmK2YXZg9mGINix2YHYuSAke21heEltYWdlc30g2LXZiNix2Kkg2YPYrdivINij2YLYtdmJYCk7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgc2V0SXNVcGxvYWRpbmcodHJ1ZSk7XG4gICAgY29uc3QgbmV3SW1hZ2VzOiBJbWFnZURhdGFbXSA9IFtdO1xuXG4gICAgdHJ5IHtcbiAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgZmlsZXMubGVuZ3RoOyBpKyspIHtcbiAgICAgICAgY29uc3QgZmlsZSA9IGZpbGVzW2ldO1xuICAgICAgICBcbiAgICAgICAgaWYgKCFhbGxvd2VkVHlwZXMuaW5jbHVkZXMoZmlsZS50eXBlKSkge1xuICAgICAgICAgIHRvYXN0LmVycm9yKGDZhtmI2Lkg2KfZhNmF2YTZgSAke2ZpbGUudHlwZX0g2LrZitixINmF2K/YudmI2YVgKTtcbiAgICAgICAgICBjb250aW51ZTtcbiAgICAgICAgfVxuXG4gICAgICAgIGNvbnN0IGZpbGVOYW1lID0gY3JlYXRlVW5pcXVlRmlsZU5hbWUoZmlsZS5uYW1lKTtcbiAgICAgICAgY29uc3QgZmlsZVBhdGggPSBgYXJ0aWNsZXMvJHthcnRpY2xlSWQgfHwgJ3RlbXAnfS8ke2ZpbGVOYW1lfWA7XG5cbiAgICAgICAgLy8g2KrYrdiv2YrYqyDYp9mE2KrZgtiv2YVcbiAgICAgICAgc2V0VXBsb2FkUHJvZ3Jlc3MocHJldiA9PiAoeyAuLi5wcmV2LCBbZmlsZS5uYW1lXTogMCB9KSk7XG5cbiAgICAgICAgLy8g2LHZgdi5INin2YTZhdmE2YFcbiAgICAgICAgY29uc3QgeyBkYXRhOiB1cGxvYWREYXRhLCBlcnJvcjogdXBsb2FkRXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlLnN0b3JhZ2VcbiAgICAgICAgICAuZnJvbSgnYXJ0aWNsZS1pbWFnZXMnKVxuICAgICAgICAgIC51cGxvYWQoZmlsZVBhdGgsIGZpbGUsIHtcbiAgICAgICAgICAgIGNhY2hlQ29udHJvbDogJzM2MDAnLFxuICAgICAgICAgICAgdXBzZXJ0OiBmYWxzZVxuICAgICAgICAgIH0pO1xuXG4gICAgICAgIGlmICh1cGxvYWRFcnJvcikge1xuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ1VwbG9hZCBlcnJvcjonLCB1cGxvYWRFcnJvcik7XG4gICAgICAgICAgdG9hc3QuZXJyb3IoYNmB2LTZhCDYsdmB2LkgJHtmaWxlLm5hbWV9OiAke3VwbG9hZEVycm9yLm1lc3NhZ2V9YCk7XG4gICAgICAgICAgY29udGludWU7XG4gICAgICAgIH1cblxuICAgICAgICAvLyDYp9mE2K3YtdmI2YQg2LnZhNmJIFVSTCDYp9mE2LnYp9mFXG4gICAgICAgIGNvbnN0IHsgZGF0YTogdXJsRGF0YSB9ID0gc3VwYWJhc2Uuc3RvcmFnZVxuICAgICAgICAgIC5mcm9tKCdhcnRpY2xlLWltYWdlcycpXG4gICAgICAgICAgLmdldFB1YmxpY1VybChmaWxlUGF0aCk7XG5cbiAgICAgICAgLy8g2KXZhti02KfYoSDYqNmK2KfZhtin2Kog2KfZhNi12YjYsdipXG4gICAgICAgIGNvbnN0IGltYWdlRGF0YTogSW1hZ2VEYXRhID0ge1xuICAgICAgICAgIGlkOiBjcnlwdG8ucmFuZG9tVVVJRCgpLFxuICAgICAgICAgIGltYWdlX3VybDogdXJsRGF0YS5wdWJsaWNVcmwsXG4gICAgICAgICAgaW1hZ2VfcGF0aDogZmlsZVBhdGgsXG4gICAgICAgICAgYWx0X3RleHQ6IGZpbGUubmFtZS5zcGxpdCgnLicpWzBdLFxuICAgICAgICAgIGNhcHRpb246ICcnLFxuICAgICAgICAgIGZpbGVfc2l6ZTogZmlsZS5zaXplLFxuICAgICAgICAgIG1pbWVfdHlwZTogZmlsZS50eXBlLFxuICAgICAgICAgIGRpc3BsYXlfb3JkZXI6IGltYWdlcy5sZW5ndGggKyBuZXdJbWFnZXMubGVuZ3RoXG4gICAgICAgIH07XG5cbiAgICAgICAgLy8g2KfZhNit2LXZiNmEINi52YTZiSDYo9io2LnYp9ivINin2YTYtdmI2LHYqVxuICAgICAgICBjb25zdCBpbWcgPSBuZXcgd2luZG93LkltYWdlKCk7XG4gICAgICAgIGltZy5vbmxvYWQgPSAoKSA9PiB7XG4gICAgICAgICAgaW1hZ2VEYXRhLndpZHRoID0gaW1nLndpZHRoO1xuICAgICAgICAgIGltYWdlRGF0YS5oZWlnaHQgPSBpbWcuaGVpZ2h0O1xuICAgICAgICB9O1xuICAgICAgICBpbWcuc3JjID0gdXJsRGF0YS5wdWJsaWNVcmw7XG5cbiAgICAgICAgbmV3SW1hZ2VzLnB1c2goaW1hZ2VEYXRhKTtcbiAgICAgICAgc2V0VXBsb2FkUHJvZ3Jlc3MocHJldiA9PiAoeyAuLi5wcmV2LCBbZmlsZS5uYW1lXTogMTAwIH0pKTtcblxuICAgICAgICAvLyDYrdmB2Lgg2YHZiiDZgtin2LnYr9ipINin2YTYqNmK2KfZhtin2Kog2KXYsNinINmD2KfZhiBhcnRpY2xlSWQg2YXYqtin2K3Yp9mLXG4gICAgICAgIGlmIChhcnRpY2xlSWQpIHtcbiAgICAgICAgICBjb25zdCB7IGVycm9yOiBkYkVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgICAgICAgLmZyb20oJ2FydGljbGVfaW1hZ2VzJylcbiAgICAgICAgICAgIC5pbnNlcnQoe1xuICAgICAgICAgICAgICBhcnRpY2xlX2lkOiBhcnRpY2xlSWQsXG4gICAgICAgICAgICAgIGltYWdlX3VybDogdXJsRGF0YS5wdWJsaWNVcmwsXG4gICAgICAgICAgICAgIGltYWdlX3BhdGg6IGZpbGVQYXRoLFxuICAgICAgICAgICAgICBhbHRfdGV4dDogaW1hZ2VEYXRhLmFsdF90ZXh0LFxuICAgICAgICAgICAgICBjYXB0aW9uOiBpbWFnZURhdGEuY2FwdGlvbixcbiAgICAgICAgICAgICAgZmlsZV9zaXplOiBpbWFnZURhdGEuZmlsZV9zaXplLFxuICAgICAgICAgICAgICBtaW1lX3R5cGU6IGltYWdlRGF0YS5taW1lX3R5cGUsXG4gICAgICAgICAgICAgIHdpZHRoOiBpbWFnZURhdGEud2lkdGgsXG4gICAgICAgICAgICAgIGhlaWdodDogaW1hZ2VEYXRhLmhlaWdodCxcbiAgICAgICAgICAgICAgZGlzcGxheV9vcmRlcjogaW1hZ2VEYXRhLmRpc3BsYXlfb3JkZXJcbiAgICAgICAgICAgIH0pO1xuXG4gICAgICAgICAgaWYgKGRiRXJyb3IpIHtcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0RhdGFiYXNlIGVycm9yOicsIGRiRXJyb3IpO1xuICAgICAgICAgICAgdG9hc3QuZXJyb3IoYNmB2LTZhCDYrdmB2Lgg2KjZitin2YbYp9iqICR7ZmlsZS5uYW1lfSDZgdmKINmC2KfYudiv2Kkg2KfZhNio2YrYp9mG2KfYqmApO1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICAvLyDYqtit2K/ZitirINmC2KfYptmF2Kkg2KfZhNi12YjYsVxuICAgICAgY29uc3QgdXBkYXRlZEltYWdlcyA9IFsuLi5pbWFnZXMsIC4uLm5ld0ltYWdlc107XG4gICAgICBvbkltYWdlc0NoYW5nZSh1cGRhdGVkSW1hZ2VzKTtcbiAgICAgIFxuICAgICAgdG9hc3Quc3VjY2Vzcyhg2KrZhSDYsdmB2LkgJHtuZXdJbWFnZXMubGVuZ3RofSDYtdmI2LHYqSDYqNmG2KzYp9itYCk7XG4gICAgICBcbiAgICB9IGNhdGNoIChlcnJvcjogYW55KSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdVcGxvYWQgcHJvY2VzcyBlcnJvcjonLCBlcnJvcik7XG4gICAgICB0b2FzdC5lcnJvcign2K3Yr9irINiu2LfYoyDYo9ir2YbYp9ihINix2YHYuSDYp9mE2LXZiNixJyk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldElzVXBsb2FkaW5nKGZhbHNlKTtcbiAgICAgIHNldFVwbG9hZFByb2dyZXNzKHt9KTtcbiAgICB9XG4gIH0sIFtpbWFnZXMsIGFydGljbGVJZCwgbWF4SW1hZ2VzLCBhbGxvd2VkVHlwZXMsIG9uSW1hZ2VzQ2hhbmdlXSk7XG5cbiAgLy8g2K3YsNmBINi12YjYsdipXG4gIGNvbnN0IGhhbmRsZURlbGV0ZUltYWdlID0gYXN5bmMgKGltYWdlSWQ6IHN0cmluZykgPT4ge1xuICAgIGNvbnN0IGltYWdlVG9EZWxldGUgPSBpbWFnZXMuZmluZChpbWcgPT4gaW1nLmlkID09PSBpbWFnZUlkKTtcbiAgICBpZiAoIWltYWdlVG9EZWxldGUpIHJldHVybjtcblxuICAgIHRyeSB7XG4gICAgICAvLyDYrdiw2YEg2YXZhiDYp9mE2KrYrtiy2YrZhlxuICAgICAgY29uc3QgeyBlcnJvcjogc3RvcmFnZUVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZS5zdG9yYWdlXG4gICAgICAgIC5mcm9tKCdhcnRpY2xlLWltYWdlcycpXG4gICAgICAgIC5yZW1vdmUoW2ltYWdlVG9EZWxldGUuaW1hZ2VfcGF0aF0pO1xuXG4gICAgICBpZiAoc3RvcmFnZUVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ1N0b3JhZ2UgZGVsZXRpb24gZXJyb3I6Jywgc3RvcmFnZUVycm9yKTtcbiAgICAgIH1cblxuICAgICAgLy8g2K3YsNmBINmF2YYg2YLYp9i52K/YqSDYp9mE2KjZitin2YbYp9iqXG4gICAgICBpZiAoYXJ0aWNsZUlkKSB7XG4gICAgICAgIGNvbnN0IHsgZXJyb3I6IGRiRXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAgICAgLmZyb20oJ2FydGljbGVfaW1hZ2VzJylcbiAgICAgICAgICAuZGVsZXRlKClcbiAgICAgICAgICAuZXEoJ2lkJywgaW1hZ2VJZCk7XG5cbiAgICAgICAgaWYgKGRiRXJyb3IpIHtcbiAgICAgICAgICBjb25zb2xlLmVycm9yKCdEYXRhYmFzZSBkZWxldGlvbiBlcnJvcjonLCBkYkVycm9yKTtcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICAvLyDYqtit2K/ZitirINmC2KfYptmF2Kkg2KfZhNi12YjYsVxuICAgICAgY29uc3QgdXBkYXRlZEltYWdlcyA9IGltYWdlcy5maWx0ZXIoaW1nID0+IGltZy5pZCAhPT0gaW1hZ2VJZCk7XG4gICAgICBvbkltYWdlc0NoYW5nZSh1cGRhdGVkSW1hZ2VzKTtcbiAgICAgIFxuICAgICAgdG9hc3Quc3VjY2Vzcygn2KrZhSDYrdiw2YEg2KfZhNi12YjYsdipINio2YbYrNin2K0nKTtcbiAgICAgIFxuICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0RlbGV0ZSBlcnJvcjonLCBlcnJvcik7XG4gICAgICB0b2FzdC5lcnJvcign2YHYtNmEINit2LDZgSDYp9mE2LXZiNix2KknKTtcbiAgICB9XG4gIH07XG5cbiAgLy8g2KXYudin2K/YqSDYqtix2KrZitioINin2YTYtdmI2LFcbiAgY29uc3QgaGFuZGxlUmVvcmRlckltYWdlcyA9IChmcm9tSW5kZXg6IG51bWJlciwgdG9JbmRleDogbnVtYmVyKSA9PiB7XG4gICAgY29uc3QgcmVvcmRlcmVkSW1hZ2VzID0gWy4uLmltYWdlc107XG4gICAgY29uc3QgW21vdmVkSW1hZ2VdID0gcmVvcmRlcmVkSW1hZ2VzLnNwbGljZShmcm9tSW5kZXgsIDEpO1xuICAgIHJlb3JkZXJlZEltYWdlcy5zcGxpY2UodG9JbmRleCwgMCwgbW92ZWRJbWFnZSk7XG4gICAgXG4gICAgLy8g2KrYrdiv2YrYqyBkaXNwbGF5X29yZGVyXG4gICAgY29uc3QgdXBkYXRlZEltYWdlcyA9IHJlb3JkZXJlZEltYWdlcy5tYXAoKGltZywgaW5kZXgpID0+ICh7XG4gICAgICAuLi5pbWcsXG4gICAgICBkaXNwbGF5X29yZGVyOiBpbmRleFxuICAgIH0pKTtcbiAgICBcbiAgICBvbkltYWdlc0NoYW5nZSh1cGRhdGVkSW1hZ2VzKTtcbiAgICB0b2FzdC5zdWNjZXNzKCfYqtmFINil2LnYp9iv2Kkg2KrYsdiq2YrYqCDYp9mE2LXZiNixJyk7XG4gIH07XG5cbiAgLy8g2YXYudin2YTYrCDYp9mE2LPYrdioINmI2KfZhNil2YHZhNin2KpcbiAgY29uc3QgaGFuZGxlRHJhZ1N0YXJ0ID0gKGU6IFJlYWN0LkRyYWdFdmVudCwgaW5kZXg6IG51bWJlcikgPT4ge1xuICAgIHNldERyYWdnZWRJbWFnZUluZGV4KGluZGV4KTtcbiAgICBlLmRhdGFUcmFuc2Zlci5lZmZlY3RBbGxvd2VkID0gJ21vdmUnO1xuICAgIGUuZGF0YVRyYW5zZmVyLnNldERhdGEoJ3RleHQvcGxhaW4nLCBpbmRleC50b1N0cmluZygpKTtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVEcmFnT3ZlciA9IChlOiBSZWFjdC5EcmFnRXZlbnQpID0+IHtcbiAgICBlLnByZXZlbnREZWZhdWx0KCk7XG4gICAgZS5kYXRhVHJhbnNmZXIuZHJvcEVmZmVjdCA9ICdtb3ZlJztcbiAgfTtcblxuICBjb25zdCBoYW5kbGVEcm9wID0gKGU6IFJlYWN0LkRyYWdFdmVudCwgZHJvcEluZGV4OiBudW1iZXIpID0+IHtcbiAgICBlLnByZXZlbnREZWZhdWx0KCk7XG4gICAgXG4gICAgaWYgKGRyYWdnZWRJbWFnZUluZGV4ICE9PSBudWxsICYmIGRyYWdnZWRJbWFnZUluZGV4ICE9PSBkcm9wSW5kZXgpIHtcbiAgICAgIGhhbmRsZVJlb3JkZXJJbWFnZXMoZHJhZ2dlZEltYWdlSW5kZXgsIGRyb3BJbmRleCk7XG4gICAgfVxuICAgIFxuICAgIHNldERyYWdnZWRJbWFnZUluZGV4KG51bGwpO1xuICB9O1xuXG4gIC8vINiq2K3Yr9mK2Ksg2KrYs9mF2YrYqSDYp9mE2LXZiNix2KlcbiAgY29uc3QgdXBkYXRlSW1hZ2VDYXB0aW9uID0gYXN5bmMgKGltYWdlSWQ6IHN0cmluZywgY2FwdGlvbjogc3RyaW5nKSA9PiB7XG4gICAgY29uc3QgdXBkYXRlZEltYWdlcyA9IGltYWdlcy5tYXAoaW1nID0+IFxuICAgICAgaW1nLmlkID09PSBpbWFnZUlkID8geyAuLi5pbWcsIGNhcHRpb24gfSA6IGltZ1xuICAgICk7XG4gICAgb25JbWFnZXNDaGFuZ2UodXBkYXRlZEltYWdlcyk7XG5cbiAgICAvLyDYqtit2K/ZitirINmB2Yog2YLYp9i52K/YqSDYp9mE2KjZitin2YbYp9iqXG4gICAgaWYgKGFydGljbGVJZCkge1xuICAgICAgYXdhaXQgc3VwYWJhc2VcbiAgICAgICAgLmZyb20oJ2FydGljbGVfaW1hZ2VzJylcbiAgICAgICAgLnVwZGF0ZSh7IGNhcHRpb24gfSlcbiAgICAgICAgLmVxKCdpZCcsIGltYWdlSWQpO1xuICAgIH1cbiAgfTtcblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICB7Lyog2LTYsdmK2Lcg2KfZhNij2K/ZiNin2KogKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC13cmFwIGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gZ2FwLTQgcC00IGJnLWdyYXktODAwIHJvdW5kZWQtbGdcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtNFwiPlxuICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGZpbGVJbnB1dFJlZi5jdXJyZW50Py5jbGljaygpfVxuICAgICAgICAgICAgZGlzYWJsZWQ9e2lzVXBsb2FkaW5nIHx8IGltYWdlcy5sZW5ndGggPj0gbWF4SW1hZ2VzfVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwicHgtNCBweS0yIGJnLXB1cnBsZS02MDAgdGV4dC13aGl0ZSByb3VuZGVkLWxnIGhvdmVyOmJnLXB1cnBsZS03MDAgZGlzYWJsZWQ6b3BhY2l0eS01MCBkaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWQgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIHtpc1VwbG9hZGluZyA/ICfYrNin2LHZiiDYp9mE2LHZgdi5Li4uJyA6ICfYsdmB2Lkg2LXZiNixJ31cbiAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICBcbiAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwIHRleHQtc21cIj5cbiAgICAgICAgICAgIHtpbWFnZXMubGVuZ3RofSAvIHttYXhJbWFnZXN9INi12YjYsdipXG4gICAgICAgICAgPC9zcGFuPlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC00XCI+XG4gICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2hvd0ltYWdlUmVmZXJlbmNlcyghc2hvd0ltYWdlUmVmZXJlbmNlcyl9XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJweC0zIHB5LTEgYmctZ3JheS03MDAgdGV4dC13aGl0ZSByb3VuZGVkIHRleHQtc20gaG92ZXI6YmctZ3JheS02MDAgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIHtzaG93SW1hZ2VSZWZlcmVuY2VzID8gJ9il2K7Zgdin2KEg2KfZhNmF2LHYp9is2LknIDogJ9i52LHYtiDYp9mE2YXYsdin2KzYuSd9XG4gICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgXG4gICAgICAgICAge3NlbGVjdGVkSW1hZ2VzLnNpemUgPiAwICYmIChcbiAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xuICAgICAgICAgICAgICAgIHNlbGVjdGVkSW1hZ2VzLmZvckVhY2goaW1hZ2VJZCA9PiBoYW5kbGVEZWxldGVJbWFnZShpbWFnZUlkKSk7XG4gICAgICAgICAgICAgICAgc2V0U2VsZWN0ZWRJbWFnZXMobmV3IFNldCgpKTtcbiAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicHgtMyBweS0xIGJnLXJlZC02MDAgdGV4dC13aGl0ZSByb3VuZGVkIHRleHQtc20gaG92ZXI6YmctcmVkLTcwMCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgINit2LDZgSDYp9mE2YXYrdiv2K8gKHtzZWxlY3RlZEltYWdlcy5zaXplfSlcbiAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICl9XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiDZhdiv2K7ZhCDYp9mE2YXZhNmB2KfYqiDYp9mE2YXYrtmB2YogKi99XG4gICAgICA8aW5wdXRcbiAgICAgICAgcmVmPXtmaWxlSW5wdXRSZWZ9XG4gICAgICAgIHR5cGU9XCJmaWxlXCJcbiAgICAgICAgbXVsdGlwbGVcbiAgICAgICAgYWNjZXB0PXthbGxvd2VkVHlwZXMuam9pbignLCcpfVxuICAgICAgICBvbkNoYW5nZT17KGUpID0+IGUudGFyZ2V0LmZpbGVzICYmIGhhbmRsZUZpbGVVcGxvYWQoZS50YXJnZXQuZmlsZXMpfVxuICAgICAgICBjbGFzc05hbWU9XCJoaWRkZW5cIlxuICAgICAgLz5cblxuICAgICAgey8qINmC2KfYptmF2Kkg2KfZhNi12YjYsSAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBsZzpncmlkLWNvbHMtMyBnYXAtNFwiPlxuICAgICAgICB7aW1hZ2VzLm1hcCgoaW1hZ2UsIGluZGV4KSA9PiAoXG4gICAgICAgICAgPGRpdlxuICAgICAgICAgICAga2V5PXtpbWFnZS5pZH1cbiAgICAgICAgICAgIGRyYWdnYWJsZVxuICAgICAgICAgICAgb25EcmFnU3RhcnQ9eyhlKSA9PiBoYW5kbGVEcmFnU3RhcnQoZSwgaW5kZXgpfVxuICAgICAgICAgICAgb25EcmFnT3Zlcj17aGFuZGxlRHJhZ092ZXJ9XG4gICAgICAgICAgICBvbkRyb3A9eyhlKSA9PiBoYW5kbGVEcm9wKGUsIGluZGV4KX1cbiAgICAgICAgICAgIGNsYXNzTmFtZT17YHJlbGF0aXZlIGdyb3VwIGJnLWdyYXktODAwIHJvdW5kZWQtbGcgb3ZlcmZsb3ctaGlkZGVuIGJvcmRlci0yIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMCAke1xuICAgICAgICAgICAgICBkcmFnZ2VkSW1hZ2VJbmRleCA9PT0gaW5kZXggPyAnYm9yZGVyLXB1cnBsZS01MDAgb3BhY2l0eS01MCcgOiAnYm9yZGVyLWdyYXktNzAwIGhvdmVyOmJvcmRlci1ncmF5LTYwMCdcbiAgICAgICAgICAgIH1gfVxuICAgICAgICAgID5cbiAgICAgICAgICAgIHsvKiDYsdmC2YUg2KfZhNi12YjYsdipICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSB0b3AtMiBsZWZ0LTIgei0xMCBiZy1wdXJwbGUtNjAwIHRleHQtd2hpdGUgdGV4dC14cyBweC0yIHB5LTEgcm91bmRlZFwiPlxuICAgICAgICAgICAgICAje2luZGV4ICsgMX1cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7Lyog2K7Yp9mG2Kkg2KfYrtiq2YrYp9ixICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSB0b3AtMiByaWdodC0yIHotMTBcIj5cbiAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgdHlwZT1cImNoZWNrYm94XCJcbiAgICAgICAgICAgICAgICBjaGVja2VkPXtzZWxlY3RlZEltYWdlcy5oYXMoaW1hZ2UuaWQpfVxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4ge1xuICAgICAgICAgICAgICAgICAgY29uc3QgbmV3U2VsZWN0ZWQgPSBuZXcgU2V0KHNlbGVjdGVkSW1hZ2VzKTtcbiAgICAgICAgICAgICAgICAgIGlmIChlLnRhcmdldC5jaGVja2VkKSB7XG4gICAgICAgICAgICAgICAgICAgIG5ld1NlbGVjdGVkLmFkZChpbWFnZS5pZCk7XG4gICAgICAgICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICBuZXdTZWxlY3RlZC5kZWxldGUoaW1hZ2UuaWQpO1xuICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgc2V0U2VsZWN0ZWRJbWFnZXMobmV3U2VsZWN0ZWQpO1xuICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy00IGgtNCB0ZXh0LXB1cnBsZS02MDAgYmctZ3JheS03MDAgYm9yZGVyLWdyYXktNjAwIHJvdW5kZWQgZm9jdXM6cmluZy1wdXJwbGUtNTAwXCJcbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7Lyog2KfZhNi12YjYsdipICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZSBhc3BlY3QtdmlkZW9cIj5cbiAgICAgICAgICAgICAgPEltYWdlXG4gICAgICAgICAgICAgICAgc3JjPXtpbWFnZS5pbWFnZV91cmx9XG4gICAgICAgICAgICAgICAgYWx0PXtpbWFnZS5hbHRfdGV4dCB8fCBg2LXZiNix2KkgJHtpbmRleCArIDF9YH1cbiAgICAgICAgICAgICAgICBmaWxsXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwib2JqZWN0LWNvdmVyXCJcbiAgICAgICAgICAgICAgICBzaXplcz1cIihtYXgtd2lkdGg6IDc2OHB4KSAxMDB2dywgKG1heC13aWR0aDogMTIwMHB4KSA1MHZ3LCAzM3Z3XCJcbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7Lyog2YXYudmE2YjZhdin2Kog2KfZhNi12YjYsdipICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTMgc3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgICB2YWx1ZT17aW1hZ2UuY2FwdGlvbiB8fCAnJ31cbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHVwZGF0ZUltYWdlQ2FwdGlvbihpbWFnZS5pZCwgZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwi2KXYttin2YHYqSDYqtiz2YXZitipINiq2YjYttmK2K3ZitipLi4uXCJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgcHgtMiBweS0xIGJnLWdyYXktNzAwIHRleHQtd2hpdGUgdGV4dC1zbSByb3VuZGVkIGJvcmRlciBib3JkZXItZ3JheS02MDAgZm9jdXM6Ym9yZGVyLXB1cnBsZS01MDAgZm9jdXM6b3V0bGluZS1ub25lXCJcbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHRleHQteHMgdGV4dC1ncmF5LTQwMFwiPlxuICAgICAgICAgICAgICAgIDxzcGFuPntpbWFnZS5maWxlX3NpemUgPyBgJHsoaW1hZ2UuZmlsZV9zaXplIC8gMTAyNCkudG9GaXhlZCgxKX0gS0JgIDogJyd9PC9zcGFuPlxuICAgICAgICAgICAgICAgIDxzcGFuPntpbWFnZS53aWR0aCAmJiBpbWFnZS5oZWlnaHQgPyBgJHtpbWFnZS53aWR0aH3DlyR7aW1hZ2UuaGVpZ2h0fWAgOiAnJ308L3NwYW4+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIHsvKiDYo9iy2LHYp9ixINin2YTYpdis2LHYp9ih2KfYqiAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCBiZy1ibGFjay81MCBvcGFjaXR5LTAgZ3JvdXAtaG92ZXI6b3BhY2l0eS0xMDAgdHJhbnNpdGlvbi1vcGFjaXR5IGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGdhcC0yXCI+XG4gICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBpbnNlcnRJbWFnZVJlZmVyZW5jZShpbWFnZSwgaW5kZXgpfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0xIHB4LTMgcHktMiBiZy1wdXJwbGUtNjAwIHRleHQtd2hpdGUgdGV4dC1zbSByb3VuZGVkLWxnIGhvdmVyOmJnLXB1cnBsZS03MDAgdHJhbnNpdGlvbi1jb2xvcnMgc2hhZG93LWxnXCJcbiAgICAgICAgICAgICAgICB0aXRsZT17YNil2K/Ysdin2Kwg2YXYsdis2LkgW9i12YjYsdipOiR7aW5kZXggKyAxfV1gfVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTQgaC00XCIgZmlsbD1cIm5vbmVcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgICAgICAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9ezJ9IGQ9XCJNMTIgNnY2bTAgMHY2bTAtNmg2bS02IDBINlwiIC8+XG4gICAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICAgICAg2KXYr9ix2KfYrFxuICAgICAgICAgICAgICA8L2J1dHRvbj5cblxuICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlRGVsZXRlSW1hZ2UoaW1hZ2UuaWQpfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0xIHB4LTMgcHktMiBiZy1yZWQtNjAwIHRleHQtd2hpdGUgdGV4dC1zbSByb3VuZGVkLWxnIGhvdmVyOmJnLXJlZC03MDAgdHJhbnNpdGlvbi1jb2xvcnMgc2hhZG93LWxnXCJcbiAgICAgICAgICAgICAgICB0aXRsZT1cItit2LDZgSDYp9mE2LXZiNix2KlcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTQgaC00XCIgZmlsbD1cIm5vbmVcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgICAgICAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9ezJ9IGQ9XCJNMTkgN2wtLjg2NyAxMi4xNDJBMiAyIDAgMDExNi4xMzggMjFINy44NjJhMiAyIDAgMDEtMS45OTUtMS44NThMNSA3bTUgNHY2bTQtNnY2bTEtMTBWNGExIDEgMCAwMC0xLTFoLTRhMSAxIDAgMDAtMSAxdjNNNCA3aDE2XCIgLz5cbiAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgICDYrdiw2YFcbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qINiy2LEg2KXYr9ix2KfYrCDYs9ix2YrYuSAqL31cbiAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaW5zZXJ0SW1hZ2VSZWZlcmVuY2UoaW1hZ2UsIGluZGV4KX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYWJzb2x1dGUgYm90dG9tLTIgcmlnaHQtMiB3LTggaC04IGJnLXB1cnBsZS02MDAgdGV4dC13aGl0ZSByb3VuZGVkLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgb3BhY2l0eS0wIGdyb3VwLWhvdmVyOm9wYWNpdHktMTAwIHRyYW5zaXRpb24tb3BhY2l0eSBzaGFkb3ctbGcgaG92ZXI6YmctcHVycGxlLTcwMFwiXG4gICAgICAgICAgICAgIHRpdGxlPXtg2KXYr9ix2KfYrCBb2LXZiNix2Kk6JHtpbmRleCArIDF9XWB9XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy00IGgtNFwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn0gZD1cIk0xMiA2djZtMCAwdjZtMC02aDZtLTYgMEg2XCIgLz5cbiAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICA8L2J1dHRvbj5cblxuICAgICAgICAgICAgey8qINmF2LHYrNi5INin2YTYtdmI2LHYqSAqL31cbiAgICAgICAgICAgIHtzaG93SW1hZ2VSZWZlcmVuY2VzICYmIChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBib3R0b20tMCBsZWZ0LTAgcmlnaHQtMCBiZy1wdXJwbGUtNjAwLzkwIHRleHQtd2hpdGUgdGV4dC14cyBwLTIgdGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICB7Z2VuZXJhdGVJbWFnZVJlZmVyZW5jZShpbmRleCwgaW1hZ2UpfVxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICkpfVxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiDYsdiz2KfZhNipINmB2KfYsdi62KkgKi99XG4gICAgICB7aW1hZ2VzLmxlbmd0aCA9PT0gMCAmJiAoXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgcHktMTIgdGV4dC1ncmF5LTQwMFwiPlxuICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwibXgtYXV0byBoLTEyIHctMTIgbWItNFwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPXsyfSBkPVwiTTQgMTZsNC41ODYtNC41ODZhMiAyIDAgMDEyLjgyOCAwTDE2IDE2bS0yLTJsMS41ODYtMS41ODZhMiAyIDAgMDEyLjgyOCAwTDIwIDE0bS02LTZoLjAxTTYgMjBoMTJhMiAyIDAgMDAyLTJWNmEyIDIgMCAwMC0yLTJINmEyIDIgMCAwMC0yIDJ2MTJhMiAyIDAgMDAyIDJ6XCIgLz5cbiAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtbWVkaXVtIG1iLTJcIj7ZhNinINiq2YjYrNivINi12YjYsSDZhdix2YHZiNi52Kk8L3A+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbVwiPtin2LbYuti3INi52YTZiSBcItix2YHYuSDYtdmI2LFcIiDZhNil2LbYp9mB2Kkg2LXZiNixINmE2YTZhdmC2KfZhDwvcD5cbiAgICAgICAgPC9kaXY+XG4gICAgICApfVxuXG4gICAgICB7Lyog2LTYsditINmG2LjYp9mFINin2YTZhdix2KfYrNi5ICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ibHVlLTkwMC8yMCBib3JkZXIgYm9yZGVyLWJsdWUtNTAwLzMwIHJvdW5kZWQtbGcgcC00XCI+XG4gICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWJsdWUtNDAwIGZvbnQtc2VtaWJvbGQgbWItMlwiPtmD2YrZgdmK2Kkg2KfYs9iq2K7Yr9in2YUg2YXYsdin2KzYuSDYp9mE2LXZiNixOjwvaDM+XG4gICAgICAgIDx1bCBjbGFzc05hbWU9XCJ0ZXh0LWJsdWUtMzAwIHRleHQtc20gc3BhY2UteS0xXCI+XG4gICAgICAgICAgPGxpPuKAoiDYp9i22LrYtyDYudmE2YkgXCLYpdiv2LHYp9isINmF2LHYrNi5XCIg2YTYpdi22KfZgdipINmF2LHYrNi5INin2YTYtdmI2LHYqSDZgdmKINin2YTZhti1PC9saT5cbiAgICAgICAgICA8bGk+4oCiINin2LPYqtiu2K/ZhSDYp9mE2YXYsdin2KzYuSDZhdir2YQgW9i12YjYsdipOjFdINmB2Yog2KPZiiDZhdmD2KfZhiDZgdmKINin2YTZhti1PC9saT5cbiAgICAgICAgICA8bGk+4oCiINiz2YrYqtmFINi52LHYtiDYp9mE2LXZiNix2Kkg2KfZhNmF2YLYp9io2YTYqSDZgdmKINin2YTZhdmD2KfZhiDYp9mE2YXYrdiv2K88L2xpPlxuICAgICAgICAgIDxsaT7igKIg2YrZhdmD2YYg2KXYudin2K/YqSDYqtix2KrZitioINin2YTYtdmI2LEg2KjYp9mE2LPYrdioINmI2KfZhNil2YHZhNin2Ko8L2xpPlxuICAgICAgICA8L3VsPlxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gICk7XG59XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VSZWYiLCJ1c2VDYWxsYmFjayIsInN1cGFiYXNlIiwidG9hc3QiLCJjcmVhdGVVbmlxdWVGaWxlTmFtZSIsIkltYWdlIiwiQWR2YW5jZWRJbWFnZU1hbmFnZXIiLCJhcnRpY2xlSWQiLCJpbWFnZXMiLCJvbkltYWdlc0NoYW5nZSIsIm9uSW1hZ2VJbnNlcnQiLCJtYXhJbWFnZXMiLCJhbGxvd2VkVHlwZXMiLCJjb250ZW50Iiwib25Db250ZW50Q2hhbmdlIiwiaXNVcGxvYWRpbmciLCJzZXRJc1VwbG9hZGluZyIsInVwbG9hZFByb2dyZXNzIiwic2V0VXBsb2FkUHJvZ3Jlc3MiLCJkcmFnZ2VkSW1hZ2VJbmRleCIsInNldERyYWdnZWRJbWFnZUluZGV4IiwiZHJhZ2dlZE92ZXJFZGl0b3IiLCJzZXREcmFnZ2VkT3ZlckVkaXRvciIsInNlbGVjdGVkSW1hZ2VzIiwic2V0U2VsZWN0ZWRJbWFnZXMiLCJTZXQiLCJzaG93SW1hZ2VSZWZlcmVuY2VzIiwic2V0U2hvd0ltYWdlUmVmZXJlbmNlcyIsImZpbGVJbnB1dFJlZiIsImdlbmVyYXRlSW1hZ2VSZWZlcmVuY2UiLCJpbmRleCIsImltYWdlRGF0YSIsImluc2VydEltYWdlUmVmZXJlbmNlIiwicmVmZXJlbmNlIiwic3VjY2VzcyIsImhhbmRsZUZpbGVVcGxvYWQiLCJmaWxlcyIsImxlbmd0aCIsImVycm9yIiwibmV3SW1hZ2VzIiwiaSIsImZpbGUiLCJpbmNsdWRlcyIsInR5cGUiLCJmaWxlTmFtZSIsIm5hbWUiLCJmaWxlUGF0aCIsInByZXYiLCJkYXRhIiwidXBsb2FkRGF0YSIsInVwbG9hZEVycm9yIiwic3RvcmFnZSIsImZyb20iLCJ1cGxvYWQiLCJjYWNoZUNvbnRyb2wiLCJ1cHNlcnQiLCJjb25zb2xlIiwibWVzc2FnZSIsInVybERhdGEiLCJnZXRQdWJsaWNVcmwiLCJpZCIsImNyeXB0byIsInJhbmRvbVVVSUQiLCJpbWFnZV91cmwiLCJwdWJsaWNVcmwiLCJpbWFnZV9wYXRoIiwiYWx0X3RleHQiLCJzcGxpdCIsImNhcHRpb24iLCJmaWxlX3NpemUiLCJzaXplIiwibWltZV90eXBlIiwiZGlzcGxheV9vcmRlciIsImltZyIsIndpbmRvdyIsIm9ubG9hZCIsIndpZHRoIiwiaGVpZ2h0Iiwic3JjIiwicHVzaCIsImRiRXJyb3IiLCJpbnNlcnQiLCJhcnRpY2xlX2lkIiwidXBkYXRlZEltYWdlcyIsImhhbmRsZURlbGV0ZUltYWdlIiwiaW1hZ2VJZCIsImltYWdlVG9EZWxldGUiLCJmaW5kIiwic3RvcmFnZUVycm9yIiwicmVtb3ZlIiwiZGVsZXRlIiwiZXEiLCJmaWx0ZXIiLCJoYW5kbGVSZW9yZGVySW1hZ2VzIiwiZnJvbUluZGV4IiwidG9JbmRleCIsInJlb3JkZXJlZEltYWdlcyIsIm1vdmVkSW1hZ2UiLCJzcGxpY2UiLCJtYXAiLCJoYW5kbGVEcmFnU3RhcnQiLCJlIiwiZGF0YVRyYW5zZmVyIiwiZWZmZWN0QWxsb3dlZCIsInNldERhdGEiLCJ0b1N0cmluZyIsImhhbmRsZURyYWdPdmVyIiwicHJldmVudERlZmF1bHQiLCJkcm9wRWZmZWN0IiwiaGFuZGxlRHJvcCIsImRyb3BJbmRleCIsInVwZGF0ZUltYWdlQ2FwdGlvbiIsInVwZGF0ZSIsImRpdiIsImNsYXNzTmFtZSIsImJ1dHRvbiIsIm9uQ2xpY2siLCJjdXJyZW50IiwiY2xpY2siLCJkaXNhYmxlZCIsInNwYW4iLCJmb3JFYWNoIiwiaW5wdXQiLCJyZWYiLCJtdWx0aXBsZSIsImFjY2VwdCIsImpvaW4iLCJvbkNoYW5nZSIsInRhcmdldCIsImltYWdlIiwiZHJhZ2dhYmxlIiwib25EcmFnU3RhcnQiLCJvbkRyYWdPdmVyIiwib25Ecm9wIiwiY2hlY2tlZCIsImhhcyIsIm5ld1NlbGVjdGVkIiwiYWRkIiwiYWx0IiwiZmlsbCIsInNpemVzIiwidmFsdWUiLCJwbGFjZWhvbGRlciIsInRvRml4ZWQiLCJ0aXRsZSIsInN2ZyIsInN0cm9rZSIsInZpZXdCb3giLCJwYXRoIiwic3Ryb2tlTGluZWNhcCIsInN0cm9rZUxpbmVqb2luIiwic3Ryb2tlV2lkdGgiLCJkIiwicCIsImgzIiwidWwiLCJsaSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/AdvancedImageManager.tsx\n"));

/***/ })

});