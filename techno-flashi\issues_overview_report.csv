﻿"Issue Name","Issue Type","Issue Priority","URLs","% of Total","Description","How To Fix","Help URL"
"Security: Missing Secure Referrer-Policy Header","Warning","Low","47","23.380","URLs missing 'no-referrer-when-downgrade', 'strict-origin-when-cross-origin', 'no-referrer' or 'strict-origin' policies in the Referrer-Policy header. When using HTTPS, it's important that the URLs do not leak in non-HTTPS requests. This can expose users to 'man in the middle' attacks, as anyone on the network can view them.","Consider setting a referrer policy of strict-origin-when-cross-origin. It retains much of the referrer's usefulness, while mitigating the risk of leaking data cross-origins.",""
"H2: Multiple","Warning","Low","67","62.620","Pages which have multiple <h2>s. This is not an issue as HTML standards allow multiple <h2>'s when used in a logical hierarchical heading structure. However, this filter can help you quickly scan to review if they are used appropriately.","Ensure <h2>s are used in a logical hierarchical heading structure, and update where appropriate utilising the full heading rank between (h3 - h6) for additional headings.",""
"H2: Missing","Warning","Low","2","1.870","Pages which have a missing <h2>, the content is empty or has a whitespace. The <h2> heading is often used to describe sections or topics within a document. They act as signposts for the user, and can help search engines understand the page.","Consider using logical and descriptive <h2>s on important pages that help the user and search engines better understand the page.",""
"Content: Low Content Pages","Opportunity","Medium","6","5.610","Pages with a word count that is below the default 200 words. The word count is based upon the content area settings used in the analysis which can be configured via 'Config > Content > Area'. There isn't a minimum word count for pages in reality, but the search engines do require descriptive text to understand the purpose of a page. This filter should only be used as a rough guide to help identify pages that might be improved by adding more descriptive content in the context of the website and page's purpose. Some websites, such as ecommerce, will naturally have lower word counts, which can be acceptable if a products details can be communicated efficiently.","Consider including additional descriptive content to help the user and search engines better understand the page.",""
"Security: Missing X-Content-Type-Options Header","Warning","Low","47","23.380","URLs that are missing the 'X-Content-Type-Options' response header with a 'nosniff' value. In the absence of a MIME type, browsers may 'sniff' to guess the content type to interpret it correctly for users. However, this can be exploited by attackers who can try and load malicious code, such as JavaScript via an image they have compromised.","To minimise security issues, the X-Content-Type-Options response header should be supplied and set to 'nosniff'. This instructs browsers to rely only on the Content-Type header and block anything that does not match accurately. This also means the content-type set needs to be accurate.",""
"H2: Non-Sequential","Warning","Low","6","5.610","Pages with an <h2> that is not the second heading level after the <h1> on the page. Heading elements should be in a logical sequentially-descending order. The purpose of heading elements is to convey the structure of the page and they should be in logical order from <h1> to <h6>, which helps navigating the page and users that rely on assistive technologies.","Ensure the <h2> is the second heading on the page. Headings should be in a logical sequential order from <h1> to <h6>. Review and update page heading levels so they are descending in order, for example the heading element following an <h1> should be an <h2>, rather than an <h3>.",""
"Security: Unsafe Cross-Origin Links","Warning","Low","1","0.500","URLs that link to external websites using the target=""_blank"" attribute (to open in a new tab), without using rel=""noopener"" (or rel=""noreferrer"") at the same time. Using target=""_blank"" alone leaves those pages exposed to both security and performance issues for some legacy browsers, which are estimated to be below 5% of market share. Setting target=""_blank"" on <a> elements implicitly provides the same rel behavior as setting rel=""noopener"" which does not set window.opener for most modern browsers, such as Chrome, Safari, Firefox and Edge.  The external links that contain the target=""_blank"" attribute by itself can be viewed in the 'outlinks' tab and 'target' column. They can be exported alongside the pages they are linked from via 'Bulk Export > Security > Unsafe Cross-Origin Links'.","Consider the benefits of including the rel=""noopener"" link attribute on any links that contain the target=""_blank"" attribute to avoid security and performance issues for the users of legacy browsers that may visit the website.",""
"URL: Parameters","Warning","Low","10","4.980","URLs that include parameters such as '?' or '&'. This isn't an issue for Google or other search engines to crawl unless at significant scale, but it's recommended to limit the number of parameters in a URL which can be complicated for users, and can be a sign of low value-add URLs.","Where possible use a static URL structure without parameters for key indexable URLs. However, changing URLs is a big decision, and often it's not worth changing them for SEO purposes alone. If URLs are changed, then appropriate 301 redirects must be implemented.",""
"Canonicals: Non-Indexable Canonical","Issue","High","14","13.080","Pages with a canonical URL that is non-indexable. This will include canonicals which are blocked by robots.txt, no response, redirect (3XX), client error (4XX), server error (5XX), are 'noindex' or 'canonicalised' themselves. This means the search engines are being instructed to consolidate indexing and link signals to a non-indexable page, which often leads to them ignoring the canonical, but may also lead to unpredictability in indexing and ranking. Export pages, their canonicals and status codes via 'Reports > Canonicals > Non-Indexable Canonicals'.","Ensure canonical URLs are to accurate indexable pages to avoid them being ignored by search engines, and any potential indexing or ranking unpredictability.",""
"Response Codes: Internal No Response","Issue","High","1","0.380","Internal URLs with no response returned from the server. Usually due to a malformed URL, connection timeout, connection error, or connection refused. View URLs that link to no responses using the lower 'inlinks' tab and export them in bulk via 'Bulk Export > Response Codes > Internal > No Response inlinks'.","Malformed URLs should be updated to the correct location and other connection issues can often be resolved by using different user-agents ('Config > User-Agent'), adjusting the crawl speed ('Config > Speed') or disabling firewalls & proxies.",""
"Page Titles: Duplicate","Opportunity","Medium","3","2.800","Pages which have duplicate page titles. It's really important to have distinct and unique page titles for every page. If every page has the same page title, then it can make it more challenging for users and the search engines to understand one page from another.","Update duplicate page titles as necessary, so each page contains a unique and descriptive title for users and search engines. If these are duplicate pages, then fix the duplicated pages by linking to a single version, and redirect or use canonicals where appropriate.",""
"Meta Description: Below 400 Pixels","Opportunity","Low","1","0.930","Pages which have meta descriptions much shorter than Google's estimated pixel length limit. This isn't necessarily an issue, but it does indicate there might be room to communicate benefits, USPs or call to actions.","Consider updating the meta description to take advantage of the space left to include additional benefits, USPs or call to actions to improve click through rates (CTR).",""
"Response Codes: External Client Error (4xx)","Warning","Low","4","1.520","External URLs with a client-side error. This indicates a problem occurred with the URL request and can include responses such as 400 bad request, 403 Forbidden, 404 Page Not Found, 410 Removed, 429 Too Many Requests and more. A 404 'Page Not Found' is the most common, and often referred to as a broken link. View URLs that link to errors using the lower 'inlinks' tab and export them in bulk via 'Bulk Export > Response Codes > External > Client Error (4xx) inlinks'.","All links on a website should ideally resolve to 200 'OK' URLs. Errors such as 404 broken links should be updated so users are taken to the correct URL, or removed. A 403 forbidden error occurs when a web server denies access to the SEO Spider's request and can often be resolved by switching the user-agent to Chrome via 'Config > User-Agent'. If they can be viewed in a browser, then it's often not an issue.",""
"Page Titles: Below 30 Characters","Opportunity","Medium","1","0.930","Pages which have page titles under the configured limit. This isn't necessarily an issue, but it does indicate there might be room to target additional keywords or communicate your USPs.","Consider updating the page title to take advantage of the space left to include additional target keywords or USPs.",""
"Page Titles: Over 60 Characters","Opportunity","Medium","71","66.360","Pages which have page titles that exceed the configured limit. Characters over this limit might be truncated in Google's search results and carry less weight in scoring.","Write concise page titles to ensure important words are not truncated in the search results, not visible to users and potentially weighted less in scoring.",""
"Canonicals: Missing","Warning","Medium","5","4.670","Pages that have no canonical URL present either as a link element, or via HTTP header. If a page doesn't indicate a canonical URL, Google will identify what they think is the best version or URL. This can lead to ranking unpredictability when there are multiple versions discovered, and hence generally all URLs should specify a canonical version","Specify a canonical URL for every page to avoid any potential ranking unpredictability if multiple versions of the same page are discovered on different URLs.",""
"Page Titles: Over 561 Pixels","Opportunity","Medium","57","53.270","Pages which have page titles over Google's estimated pixel length limit for titles in search results. Google snippet length is actually based upon pixels limits, rather than a character length. The SEO Spider tries to match the latest pixel truncation points in the SERPs, but it is an approximation and Google adjusts them frequently.","Write concise page titles to ensure important words are not truncated in the search results, not visible to users and potentially weighted less in scoring.",""
"Response Codes: Internal Redirection (3xx)","Warning","Low","5","1.890","Internal URLs which redirect to another URL. These will include server-side redirects, such as 301 or 302 redirects (and more). View URLs that link to redirects using the lower 'inlinks' tab and export them in bulk via 'Bulk Export > Response Codes > Internal > Redirection (3xx) inlinks'.","Ideally all internal links would be to canonical resolving URLs, and avoid linking to URLs that redirect. This reduces latency of redirect hops for users, and enhanced efficiency for search engines.",""
"Meta Description: Duplicate","Opportunity","Low","2","1.870","Pages which have duplicate meta descriptions. It's really important to have distinct and unique meta descriptions that communicate the benefits and purpose of each page. If they are duplicate or irrelevant, then they will be ignored by search engines in their snippets.","Update duplicate meta descriptions as necessary, so important pages contain a unique and descriptive title for users and search engines. If these are duplicate pages, then fix the duplicated pages by linking to a single version, and redirect or use canonicals where appropriate.",""
"URL: Non ASCII Characters","Warning","Low","10","4.980","URLs with characters outside of the ASCII character-set. Standards outline that URLs can only be sent using the ASCII character-set and some users may have difficulty with subtleties of characters outside this range.","URLs should be converted into a valid ASCII format, by encoding links to the URL with safe characters (made up of % followed by two hexadecimal digits). Today browsers and the search engines are largely able to transform URLs accurately.",""
"Directives: Noindex","Warning","High","12","6.150","URLs containing a 'noindex' directive in either a robots meta tag or X-Robots-Tag in the HTTP header. This instructs the search engines not to index the page. The page will still be crawled (to see the directive), but it will then be dropped from the index.","URLs with a 'noindex' should be reviewed carefully to ensure they are correct and shouldn't be indexed. If these pages should be indexed, then the 'noindex' directive should be removed.",""
"Meta Description: Below 70 Characters","Opportunity","Low","2","1.870","Pages which have meta descriptions below the configured limit. This isn't strictly an issue, but an opportunity. There is additional room to communicate benefits, USPs or call to actions.","Consider updating the meta description to take advantage of the space left to include additional benefits, USPs or call to actions to improve click through rates (CTR).",""
"URL: Underscores","Opportunity","Low","1","0.500","URLs with underscores, which are not always seen as word separators by search engines.","Ideally hyphens should be used as word separators, rather than underscores. However, changing URLs is a big decision, and often it's not worth changing them for SEO purposes alone. If URLs are changed, then appropriate 301 redirects must be implemented.",""
"H1: Over 70 Characters","Opportunity","Low","1","0.930","Pages which have <h1>s over the configured length. There is no hard limit for characters in an <h1>, however they should be clear and concise for users and long headings might be less helpful","Write concise <h1>s for users, including target keywords where natural for users - without keyword stuffing.",""
"Security: HTTP URLs","Issue","High","1","0.500","HTTP URLs that are encountered in the crawl. All websites should be secure over HTTPS today on the web. Not only is it important for security, but it's now expected by users. Chrome and other browsers display a 'Not Secure' message against any URLs that are HTTP, or have mixed content issues (where they load insecure resources on them). To view how these URLs were discovered, view their 'inlinks' in the lower window tab. You can also export any pages that link to HTTP URLs via 'Bulk Export > Security > HTTP URLs Inlinks'.","All URLs should be to secure HTTPS pages. Pages should be served over HTTPS, any internal links should be updated to HTTPS versions and HTTP URLs should 301 redirect to HTTPS versions. HTTP URLs identified in this filter that are redirecting to HTTPS versions already should be updated to link to the correct HTTPS versions directly.",""
"Security: Missing X-Frame-Options Header","Warning","Low","47","23.380","URLs missing an X-Frame-Options response header with a 'DENY' or 'SAMEORIGIN' value. This instructs the browser not to render a page within a frame, iframe, embed or object. This helps avoid 'clickjacking' attacks, where your content is displayed on another web page that is controlled by an attacker.","To minimise security issues, the X-Frame-Options response header should be supplied with a 'DENY' or 'SAMEORIGIN' value.",""
"URL: Contains Space","Issue","Low","8","3.980","URLs that contain a space. These are considered unsafe and could cause the link to be broken when sharing the URL. Hyphens should be used as word separators instead of spaces.","Ideally hyphens should be used as word separators, rather than spaces. However, changing URLs is a big decision. If URLs are changed, then appropriate 301 redirects must be implemented.",""
"H2: Over 70 Characters","Opportunity","Low","2","1.870","Pages which have <h2>s over the configured limit. There is no hard limit for characters in an <h2>, however they should be clear and concise for users and long headings might be less helpful","Write concise <h2>s for users, including target keywords where natural for users - without keyword stuffing.",""
"Images: Missing Size Attributes","Opportunity","Low","7","12.070","Image elements without dimensions (width and height size attributes) specified in the HTML. This can cause large layout shifts as the page loads and be frustrating experience for users. It is one of the major reasons that contributes to a high Cumulative Layout Shift (CLS).","Define all image sizes as their native size using width and height attributes in the HTML. This allows the browser to calculate enough space for each image before it loads, avoiding large layout shifts.",""
"H1: Duplicate","Opportunity","Low","3","2.800","Pages which have duplicate <h1>s. It's important to have distinct, unique and useful main headings. If every page has the same <h1>, then it can make it more challenging for users and the search engines to understand one page from another.","Update duplicate <h1>s as necessary, so important pages contain a unique and descriptive <h1> for users and search engines.  If these are duplicate pages, then fix the duplicated pages by linking to a single version, and redirect or use canonicals where appropriate.",""
"H2: Duplicate","Opportunity","Low","65","60.750","Pages which have duplicate <h2>s. It's important to have distinct, unique and useful pages. If every page has the same <h2>, then it can make it more challenging for users and the search engines to understand one page from another.","Update duplicate <h2>s as necessary, so important pages contain a unique and descriptive <h2> for users and search engines.  If these are duplicate pages, then fix the duplicated pages by linking to a single version, and redirect or use canonicals where appropriate.",""
"Meta Description: Over 155 Characters","Opportunity","Low","1","0.930","Pages which have meta descriptions over the configured limit. Characters over this limit might be truncated in Google's search results.","Write concise meta descriptions to ensure important words are not truncated in the search results, and not visible to users.",""
"Canonicals: Canonicalised","Warning","High","14","13.080","Pages that have a canonical to a different URL. The URL is 'canonicalised' to another location. This means the search engines are being instructed to not index the page, and the indexing and linking properties should be consolidated to the URL in the canonical.","These URLs should be reviewed carefully to ensure the indexing and link signals are being consolidated to the correct URL. In a perfect world, a website wouldn't need to canonicalise any URLs as only canonical versions would be linked to internally on a website, but often they are required due to various circumstances outside of control, and to prevent duplicate content. Update internal links to canonical versions of URLs where possible.",""
