"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/SimpleHeroSection.tsx":
/*!**********************************************!*\
  !*** ./src/components/SimpleHeroSection.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SimpleHeroSection: () => (/* binding */ SimpleHeroSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ SimpleHeroSection auto */ \nvar _s = $RefreshSig$();\n\n\nfunction SimpleHeroSection() {\n    _s();\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const animationRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    const mouseRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        x: 0,\n        y: 0\n    });\n    const particlesRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SimpleHeroSection.useEffect\": ()=>{\n            const canvas = canvasRef.current;\n            if (!canvas) return;\n            const ctx = canvas.getContext('2d');\n            if (!ctx) return;\n            // إعداد الكانفاس\n            const resizeCanvas = {\n                \"SimpleHeroSection.useEffect.resizeCanvas\": ()=>{\n                    canvas.width = window.innerWidth;\n                    canvas.height = window.innerHeight;\n                }\n            }[\"SimpleHeroSection.useEffect.resizeCanvas\"];\n            resizeCanvas();\n            window.addEventListener('resize', resizeCanvas);\n            // إنشاء الجسيمات الذكية المتطورة\n            const createParticles = {\n                \"SimpleHeroSection.useEffect.createParticles\": ()=>{\n                    const particles = [];\n                    const colors = [\n                        '#8b5cf6',\n                        '#a855f7',\n                        '#c084fc',\n                        '#d8b4fe',\n                        '#e879f9',\n                        '#f0abfc',\n                        '#fbbf24',\n                        '#f59e0b',\n                        '#06b6d4',\n                        '#0891b2',\n                        '#10b981',\n                        '#059669'\n                    ];\n                    for(let i = 0; i < 150; i++){\n                        particles.push({\n                            x: Math.random() * canvas.width,\n                            y: Math.random() * canvas.height,\n                            vx: (Math.random() - 0.5) * 1.5,\n                            vy: (Math.random() - 0.5) * 1.5,\n                            size: Math.random() * 6 + 1,\n                            color: colors[Math.floor(Math.random() * colors.length)],\n                            opacity: Math.random() * 0.6 + 0.2,\n                            pulse: Math.random() * Math.PI * 2,\n                            pulseSpeed: 0.01 + Math.random() * 0.03,\n                            rotationSpeed: (Math.random() - 0.5) * 0.03,\n                            magnetism: Math.random() * 0.5 + 0.5,\n                            trail: [],\n                            energy: Math.random() * 100 + 50 // طاقة الجسيمة\n                        });\n                    }\n                    particlesRef.current = particles;\n                }\n            }[\"SimpleHeroSection.useEffect.createParticles\"];\n            createParticles();\n            // معالج حركة الماوس\n            const handleMouseMove = {\n                \"SimpleHeroSection.useEffect.handleMouseMove\": (e)=>{\n                    mouseRef.current = {\n                        x: e.clientX / window.innerWidth * 2 - 1,\n                        y: -(e.clientY / window.innerHeight) * 2 + 1\n                    };\n                }\n            }[\"SimpleHeroSection.useEffect.handleMouseMove\"];\n            window.addEventListener('mousemove', handleMouseMove);\n            // دالة رسم السداسي\n            const drawHexagon = {\n                \"SimpleHeroSection.useEffect.drawHexagon\": (ctx, x, y, size)=>{\n                    ctx.beginPath();\n                    for(let i = 0; i < 6; i++){\n                        const angle = i * Math.PI / 3;\n                        const px = x + size * Math.cos(angle);\n                        const py = y + size * Math.sin(angle);\n                        if (i === 0) {\n                            ctx.moveTo(px, py);\n                        } else {\n                            ctx.lineTo(px, py);\n                        }\n                    }\n                    ctx.closePath();\n                    ctx.stroke();\n                }\n            }[\"SimpleHeroSection.useEffect.drawHexagon\"];\n            // حلقة الرسم المتطورة مع تأثيرات مبهرة\n            const animate = {\n                \"SimpleHeroSection.useEffect.animate\": ()=>{\n                    // مسح تدريجي بدلاً من المسح الكامل\n                    ctx.fillStyle = 'rgba(255, 255, 255, 0.05)';\n                    ctx.fillRect(0, 0, canvas.width, canvas.height);\n                    const time = Date.now() * 0.001;\n                    const mouseX = mouseRef.current.x * canvas.width * 0.5 + canvas.width * 0.5;\n                    const mouseY = mouseRef.current.y * canvas.height * 0.5 + canvas.height * 0.5;\n                    // رسم موجات ديناميكية في الخلفية\n                    const waveCount = 5;\n                    for(let i = 0; i < waveCount; i++){\n                        ctx.beginPath();\n                        ctx.strokeStyle = \"rgba(168, 85, 247, \".concat(Math.max(0.01, 0.02 + Math.sin(time + i) * 0.01), \")\");\n                        ctx.lineWidth = 2;\n                        for(let x = 0; x <= canvas.width; x += 10){\n                            const y = canvas.height * 0.5 + Math.sin(x * 0.01 + time + i) * 50 * (i + 1) + Math.sin(x * 0.005 + time * 0.5 + i) * 30;\n                            if (x === 0) {\n                                ctx.moveTo(x, y);\n                            } else {\n                                ctx.lineTo(x, y);\n                            }\n                        }\n                        ctx.stroke();\n                    }\n                    // رسم شبكة سداسية متحركة\n                    const hexSize = 60;\n                    const hexRows = Math.ceil(canvas.height / (hexSize * 0.75)) + 2;\n                    const hexCols = Math.ceil(canvas.width / (hexSize * Math.sqrt(3))) + 2;\n                    ctx.strokeStyle = \"rgba(139, 92, 246, \".concat(Math.max(0.01, 0.04 + Math.sin(time * 0.5) * 0.02), \")\");\n                    ctx.lineWidth = 1;\n                    for(let row = 0; row < hexRows; row++){\n                        for(let col = 0; col < hexCols; col++){\n                            const x = col * hexSize * Math.sqrt(3) + row % 2 * hexSize * Math.sqrt(3) * 0.5;\n                            const y = row * hexSize * 0.75;\n                            const distanceToMouse = Math.sqrt((x - mouseX) ** 2 + (y - mouseY) ** 2);\n                            const influence = Math.max(0, 1 - distanceToMouse / 200);\n                            if (influence > 0.1) {\n                                drawHexagon(ctx, x + Math.sin(time + row + col) * 5, y + Math.cos(time + row + col) * 5, hexSize * 0.3);\n                            }\n                        }\n                    }\n                    // رسم دوائر متوهجة ناعمة\n                    const glowCircles = 5;\n                    for(let i = 0; i < glowCircles; i++){\n                        const x = canvas.width * (0.1 + i * 0.2);\n                        const y = canvas.height * (0.2 + Math.sin(time * 0.3 + i) * 0.3);\n                        const radius = Math.max(10, 150 + Math.sin(time * 0.4 + i) * 50);\n                        const gradient = ctx.createRadialGradient(x, y, 0, x, y, radius);\n                        const colors = [\n                            \"rgba(168, 85, 247, \".concat(Math.max(0.01, 0.05 + Math.sin(time + i) * 0.02), \")\"),\n                            \"rgba(236, 72, 153, \".concat(Math.max(0.01, 0.03 + Math.sin(time + i + 1) * 0.015), \")\"),\n                            \"rgba(251, 191, 36, \".concat(Math.max(0.01, 0.02 + Math.sin(time + i + 2) * 0.01), \")\")\n                        ];\n                        gradient.addColorStop(0, colors[i % colors.length]);\n                        gradient.addColorStop(0.7, colors[(i + 1) % colors.length].replace(/[\\d.]+\\)/, '0.01)'));\n                        gradient.addColorStop(1, 'rgba(255, 255, 255, 0)');\n                        ctx.fillStyle = gradient;\n                        ctx.beginPath();\n                        ctx.arc(x, y, radius, 0, Math.PI * 2);\n                        ctx.fill();\n                    }\n                    // رسم أشكال هندسية متحركة\n                    const shapes = 3;\n                    for(let i = 0; i < shapes; i++){\n                        const x = canvas.width * (0.3 + i * 0.2);\n                        const y = canvas.height * (0.4 + Math.cos(time * 0.2 + i) * 0.2);\n                        const size = Math.max(10, 30 + Math.sin(time + i) * 10);\n                        const rotation = time * 0.5 + i;\n                        ctx.save();\n                        ctx.translate(x, y);\n                        ctx.rotate(rotation);\n                        const gradient = ctx.createLinearGradient(-size, -size, size, size);\n                        gradient.addColorStop(0, \"rgba(168, 85, 247, \".concat(Math.max(0.01, 0.1 + Math.sin(time + i) * 0.05), \")\"));\n                        gradient.addColorStop(1, \"rgba(236, 72, 153, \".concat(Math.max(0.01, 0.05 + Math.cos(time + i) * 0.03), \")\"));\n                        ctx.fillStyle = gradient;\n                        ctx.beginPath();\n                        ctx.roundRect(-size / 2, -size / 2, size, size, 8);\n                        ctx.fill();\n                        ctx.restore();\n                    }\n                    // تحديث ورسم الجسيمات الذكية المتطورة\n                    particlesRef.current.forEach({\n                        \"SimpleHeroSection.useEffect.animate\": (particle, index)=>{\n                            // التأكد من صحة القيم\n                            particle.size = Math.max(1, particle.size || 2);\n                            particle.energy = Math.max(50, Math.min(100, particle.energy || 50));\n                            particle.opacity = Math.max(0.1, Math.min(1, particle.opacity || 0.5));\n                            // حفظ الموضع السابق للمسار\n                            particle.trail.push({\n                                x: particle.x,\n                                y: particle.y\n                            });\n                            if (particle.trail.length > 5) {\n                                particle.trail.shift();\n                            }\n                            // تحديث الموضع مع فيزياء متقدمة\n                            particle.x += particle.vx;\n                            particle.y += particle.vy;\n                            particle.pulse += particle.pulseSpeed;\n                            // تأثير الماوس المغناطيسي المتقدم\n                            const mouseInfluence = 120;\n                            const dx = mouseX - particle.x;\n                            const dy = mouseY - particle.y;\n                            const distance = Math.sqrt(dx * dx + dy * dy);\n                            if (distance < mouseInfluence) {\n                                const force = (mouseInfluence - distance) / mouseInfluence * particle.magnetism;\n                                const angle = Math.atan2(dy, dx);\n                                particle.vx += Math.cos(angle) * force * 0.003;\n                                particle.vy += Math.sin(angle) * force * 0.003;\n                                // تأثير الطاقة\n                                particle.energy = Math.min(100, particle.energy + force * 2);\n                            } else {\n                                // تقليل الطاقة تدريجياً\n                                particle.energy = Math.max(50, particle.energy - 0.5);\n                            }\n                            // تطبيق الاحتكاك\n                            particle.vx *= 0.99;\n                            particle.vy *= 0.99;\n                            // حدود الشاشة مع ارتداد ديناميكي\n                            if (particle.x < 0 || particle.x > canvas.width) {\n                                particle.vx *= -0.7;\n                                particle.x = Math.max(0, Math.min(canvas.width, particle.x));\n                            }\n                            if (particle.y < 0 || particle.y > canvas.height) {\n                                particle.vy *= -0.7;\n                                particle.y = Math.max(0, Math.min(canvas.height, particle.y));\n                            }\n                            // رسم مسار الجسيمة\n                            if (particle.trail.length > 1) {\n                                ctx.beginPath();\n                                ctx.strokeStyle = particle.color.replace(')', ', 0.2)').replace('rgb', 'rgba');\n                                ctx.lineWidth = 1;\n                                for(let i = 0; i < particle.trail.length - 1; i++){\n                                    const alpha = i / particle.trail.length;\n                                    ctx.globalAlpha = alpha * 0.3;\n                                    if (i === 0) {\n                                        ctx.moveTo(particle.trail[i].x, particle.trail[i].y);\n                                    } else {\n                                        ctx.lineTo(particle.trail[i].x, particle.trail[i].y);\n                                    }\n                                }\n                                ctx.stroke();\n                            }\n                            // رسم الجسيمة مع تأثيرات متقدمة\n                            const energyFactor = particle.energy / 100;\n                            const pulseSize = Math.max(1, particle.size + Math.sin(particle.pulse) * 2 * energyFactor);\n                            const pulseOpacity = Math.max(0.1, particle.opacity + Math.sin(particle.pulse) * 0.3 * energyFactor);\n                            // رسم الهالة الخارجية\n                            const outerRadius = Math.max(1, pulseSize * 4);\n                            const outerGradient = ctx.createRadialGradient(particle.x, particle.y, 0, particle.x, particle.y, outerRadius);\n                            outerGradient.addColorStop(0, particle.color.replace(')', ', 0.4)').replace('rgb', 'rgba'));\n                            outerGradient.addColorStop(0.5, particle.color.replace(')', ', 0.2)').replace('rgb', 'rgba'));\n                            outerGradient.addColorStop(1, particle.color.replace(')', ', 0)').replace('rgb', 'rgba'));\n                            ctx.fillStyle = outerGradient;\n                            ctx.globalAlpha = pulseOpacity * 0.6 * energyFactor;\n                            ctx.beginPath();\n                            ctx.arc(particle.x, particle.y, outerRadius, 0, Math.PI * 2);\n                            ctx.fill();\n                            // رسم الهالة الداخلية\n                            const innerRadius = Math.max(1, pulseSize * 2);\n                            const innerGradient = ctx.createRadialGradient(particle.x, particle.y, 0, particle.x, particle.y, innerRadius);\n                            innerGradient.addColorStop(0, particle.color.replace(')', ', 0.8)').replace('rgb', 'rgba'));\n                            innerGradient.addColorStop(1, particle.color.replace(')', ', 0.2)').replace('rgb', 'rgba'));\n                            ctx.fillStyle = innerGradient;\n                            ctx.globalAlpha = pulseOpacity * 0.8;\n                            ctx.beginPath();\n                            ctx.arc(particle.x, particle.y, innerRadius, 0, Math.PI * 2);\n                            ctx.fill();\n                            // رسم الجسيمة الأساسية\n                            ctx.beginPath();\n                            ctx.arc(particle.x, particle.y, Math.max(0.5, pulseSize), 0, Math.PI * 2);\n                            ctx.fillStyle = particle.color;\n                            ctx.globalAlpha = Math.max(0.1, Math.min(1, pulseOpacity));\n                            ctx.fill();\n                            // رسم خطوط الاتصال الذكية المتطورة\n                            particlesRef.current.forEach({\n                                \"SimpleHeroSection.useEffect.animate\": (otherParticle, otherIndex)=>{\n                                    if (index !== otherIndex && index < otherIndex) {\n                                        const dx = particle.x - otherParticle.x;\n                                        const dy = particle.y - otherParticle.y;\n                                        const distance = Math.sqrt(dx * dx + dy * dy);\n                                        if (distance < 150) {\n                                            const opacity = Math.max(0, 0.2 * (1 - distance / 150));\n                                            const energyBonus = (particle.energy + otherParticle.energy) / 200;\n                                            const finalOpacity = Math.max(0.01, Math.min(1, opacity * (1 + energyBonus)));\n                                            // خط متدرج ديناميكي\n                                            const gradient = ctx.createLinearGradient(particle.x, particle.y, otherParticle.x, otherParticle.y);\n                                            const color1 = particle.color.replace(')', ', ' + finalOpacity + ')').replace('rgb', 'rgba');\n                                            const color2 = otherParticle.color.replace(')', ', ' + finalOpacity + ')').replace('rgb', 'rgba');\n                                            const midColor = \"rgba(192, 132, 252, \".concat(finalOpacity * 1.2, \")\");\n                                            gradient.addColorStop(0, color1);\n                                            gradient.addColorStop(0.5, midColor);\n                                            gradient.addColorStop(1, color2);\n                                            // رسم خط متموج\n                                            ctx.beginPath();\n                                            ctx.strokeStyle = gradient;\n                                            ctx.lineWidth = 1 + finalOpacity * 3;\n                                            const steps = 10;\n                                            for(let i = 0; i <= steps; i++){\n                                                const t = i / steps;\n                                                const x = particle.x + (otherParticle.x - particle.x) * t;\n                                                const y = particle.y + (otherParticle.y - particle.y) * t;\n                                                // إضافة تموج ناعم\n                                                const waveOffset = Math.sin(t * Math.PI * 2 + time) * 5 * finalOpacity;\n                                                const perpX = -(otherParticle.y - particle.y) / distance;\n                                                const perpY = (otherParticle.x - particle.x) / distance;\n                                                const finalX = x + perpX * waveOffset;\n                                                const finalY = y + perpY * waveOffset;\n                                                if (i === 0) {\n                                                    ctx.moveTo(finalX, finalY);\n                                                } else {\n                                                    ctx.lineTo(finalX, finalY);\n                                                }\n                                            }\n                                            ctx.stroke();\n                                            // إضافة نقاط ضوئية على الخط\n                                            if (finalOpacity > 0.1) {\n                                                const midX = (particle.x + otherParticle.x) / 2;\n                                                const midY = (particle.y + otherParticle.y) / 2;\n                                                ctx.beginPath();\n                                                ctx.arc(midX, midY, Math.max(0.5, 2 * finalOpacity), 0, Math.PI * 2);\n                                                ctx.fillStyle = \"rgba(255, 255, 255, \".concat(Math.max(0.1, finalOpacity * 0.8), \")\");\n                                                ctx.fill();\n                                            }\n                                        }\n                                    }\n                                }\n                            }[\"SimpleHeroSection.useEffect.animate\"]);\n                        }\n                    }[\"SimpleHeroSection.useEffect.animate\"]);\n                    ctx.globalAlpha = 1;\n                    animationRef.current = requestAnimationFrame(animate);\n                }\n            }[\"SimpleHeroSection.useEffect.animate\"];\n            animate();\n            // التنظيف\n            return ({\n                \"SimpleHeroSection.useEffect\": ()=>{\n                    if (animationRef.current) {\n                        cancelAnimationFrame(animationRef.current);\n                    }\n                    window.removeEventListener('resize', resizeCanvas);\n                    window.removeEventListener('mousemove', handleMouseMove);\n                }\n            })[\"SimpleHeroSection.useEffect\"];\n        }\n    }[\"SimpleHeroSection.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-white via-purple-50 to-pink-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                ref: canvasRef,\n                className: \"absolute inset-0 w-full h-full\",\n                style: {\n                    zIndex: 1\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                lineNumber: 397,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-t from-white/30 via-transparent to-purple-50/20\",\n                style: {\n                    zIndex: 2\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                lineNumber: 404,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 text-center px-4 max-w-6xl mx-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-5xl md:text-7xl font-bold text-gray-900 mb-6 leading-tight\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"bg-gradient-to-r from-purple-600 via-pink-600 to-purple-600 bg-clip-text text-transparent\",\n                                    children: \"TechnoFlash\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                    lineNumber: 410,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 409,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl md:text-2xl text-gray-700 mb-8 max-w-3xl mx-auto leading-relaxed\",\n                                children: \"منصتك الشاملة لأحدث التقنيات وأدوات الذكاء الاصطناعي\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 414,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                        lineNumber: 408,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row gap-4 justify-center items-center mb-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/articles\",\n                                className: \"group bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-4 rounded-xl font-semibold text-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"استكشف المقالات\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 425,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5 group-hover:translate-x-1 transition-transform\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M13 7l5 5m0 0l-5 5m5-5H6\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                            lineNumber: 427,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 426,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 421,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/ai-tools\",\n                                className: \"group border-2 border-purple-400 text-purple-400 px-8 py-4 rounded-xl font-semibold text-lg hover:bg-purple-400 hover:text-white transition-all duration-300 flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"أدوات الذكاء الاصطناعي\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 435,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5 group-hover:rotate-12 transition-transform\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M13 10V3L4 14h7v7l9-11h-7z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                            lineNumber: 437,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 436,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 431,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                        lineNumber: 420,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-2\",\n                                        children: \"100+\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 445,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-600\",\n                                        children: \"مقال تقني\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 446,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 444,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-2\",\n                                        children: \"50+\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 449,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-600\",\n                                        children: \"أداة ذكاء اصطناعي\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 450,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 448,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-2\",\n                                        children: \"1000+\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 453,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-600\",\n                                        children: \"قارئ نشط\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 454,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 452,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                        lineNumber: 443,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                lineNumber: 407,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-8 left-1/2 transform -translate-x-1/2 z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-bounce\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-8 h-8 border-2 border-gray-400/60 rounded-full flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-4 h-4 text-gray-600/80\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M19 14l-7 7m0 0l-7-7m7 7V3\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 464,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                            lineNumber: 463,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                        lineNumber: 462,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                    lineNumber: 461,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                lineNumber: 460,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n        lineNumber: 395,\n        columnNumber: 5\n    }, this);\n}\n_s(SimpleHeroSection, \"/8IKySTmGZxFymijsBTHUGtuaQs=\");\n_c = SimpleHeroSection;\nvar _c;\n$RefreshReg$(_c, \"SimpleHeroSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/SimpleHeroSection.tsx\n"));

/***/ })

});