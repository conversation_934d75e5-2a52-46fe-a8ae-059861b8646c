"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/articles/edit/[id]/page",{

/***/ "(app-pages-browser)/./src/components/MarkdownPreview.tsx":
/*!********************************************!*\
  !*** ./src/components/MarkdownPreview.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MarkdownPreview)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _ArticleImageGallery__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ArticleImageGallery */ \"(app-pages-browser)/./src/components/ArticleImageGallery.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n// مكون فيديو YouTube\nfunction YouTubeVideo(param) {\n    let { videoId } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative w-full my-6 rounded-lg overflow-hidden bg-gray-900\",\n        style: {\n            aspectRatio: '16/9',\n            minHeight: '315px'\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"iframe\", {\n            src: \"https://www.youtube.com/embed/\".concat(videoId, \"?rel=0&modestbranding=1\"),\n            className: \"absolute inset-0 w-full h-full border-0\",\n            allow: \"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share\",\n            allowFullScreen: true,\n            title: \"فيديو يوتيوب\",\n            loading: \"lazy\",\n            width: \"560\",\n            height: \"315\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\MarkdownPreview.tsx\",\n            lineNumber: 28,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\MarkdownPreview.tsx\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, this);\n}\n_c = YouTubeVideo;\n// دالة استخراج معرف فيديو YouTube\nconst extractYouTubeId = (url)=>{\n    if (!url || typeof url !== 'string') {\n        return null;\n    }\n    // تنظيف الرابط\n    const cleanUrl = url.trim();\n    // أنماط مختلفة لروابط YouTube\n    const patterns = [\n        // https://www.youtube.com/watch?v=VIDEO_ID\n        /(?:https?:\\/\\/)?(?:www\\.)?youtube\\.com\\/watch\\?v=([a-zA-Z0-9_-]{11})/,\n        // https://youtu.be/VIDEO_ID\n        /(?:https?:\\/\\/)?youtu\\.be\\/([a-zA-Z0-9_-]{11})/,\n        // https://www.youtube.com/embed/VIDEO_ID\n        /(?:https?:\\/\\/)?(?:www\\.)?youtube\\.com\\/embed\\/([a-zA-Z0-9_-]{11})/,\n        // مجرد معرف الفيديو\n        /^([a-zA-Z0-9_-]{11})$/\n    ];\n    for (const pattern of patterns){\n        const match = cleanUrl.match(pattern);\n        if (match && match[1]) {\n            console.log('Pattern matched:', pattern, 'Video ID:', match[1]);\n            return match[1];\n        }\n    }\n    console.log('No pattern matched for URL:', cleanUrl);\n    return null;\n};\nfunction MarkdownPreview(param) {\n    let { content, className = '', articleImages = [] } = param;\n    _s();\n    // تحليل المحتوى وإرجاع عناصر React\n    const parseMarkdownToElements = (text)=>{\n        if (!text) return [];\n        const elements = [];\n        const lines = text.split('\\n');\n        let currentParagraph = '';\n        let key = 0;\n        let autoImageIndex = 0; // فهرس الصورة التلقائية\n        // معالجة مراجع الصور [صورة:رقم]\n        const processImageReferences = (content)=>{\n            // تقسيم المحتوى بناءً على مراجع الصور\n            const parts = content.split(/(\\[صورة:\\d+\\])/g);\n            const processedParts = [];\n            parts.forEach((part, index)=>{\n                const imageMatch = part.match(/\\[صورة:(\\d+)\\]/);\n                if (imageMatch) {\n                    const imageNumber = parseInt(imageMatch[1]) - 1; // تحويل إلى فهرس (0-based)\n                    const image = articleImages === null || articleImages === void 0 ? void 0 : articleImages[imageNumber];\n                    if (image) {\n                        processedParts.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"my-8 w-full\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative w-full max-w-4xl mx-auto rounded-lg overflow-hidden shadow-lg bg-gray-900 border border-gray-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative w-full\",\n                                        style: {\n                                            aspectRatio: '16/9',\n                                            minHeight: '300px',\n                                            maxHeight: '500px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                src: image.image_url,\n                                                alt: image.alt_text || image.caption || \"صورة \".concat(imageNumber + 1),\n                                                fill: true,\n                                                sizes: \"(max-width: 768px) 100vw, (max-width: 1200px) 80vw, 70vw\",\n                                                className: \"object-cover\",\n                                                loading: \"lazy\",\n                                                quality: 85\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\MarkdownPreview.tsx\",\n                                                lineNumber: 104,\n                                                columnNumber: 21\n                                            }, this),\n                                            image.caption && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-white text-sm text-center font-medium\",\n                                                    children: image.caption\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\MarkdownPreview.tsx\",\n                                                    lineNumber: 115,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\MarkdownPreview.tsx\",\n                                                lineNumber: 114,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\MarkdownPreview.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute top-2 left-2 bg-purple-600 text-white text-xs px-2 py-1 rounded shadow-lg\",\n                                        children: [\n                                            \"صورة \",\n                                            imageNumber + 1\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\MarkdownPreview.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute top-2 right-2 bg-blue-600 text-white text-xs px-2 py-1 rounded shadow-lg\",\n                                        children: [\n                                            \"[صورة:\",\n                                            imageNumber + 1,\n                                            \"]\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\MarkdownPreview.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\MarkdownPreview.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 17\n                            }, this)\n                        }, \"ref-image-\".concat(key++), false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\MarkdownPreview.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 15\n                        }, this));\n                    } else {\n                        // إذا لم توجد الصورة، اعرض رسالة خطأ مفصلة\n                        processedParts.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"my-4 p-4 bg-red-900/20 border border-red-500/30 rounded-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center text-red-400\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5 mr-2\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\MarkdownPreview.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\MarkdownPreview.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-medium\",\n                                                children: [\n                                                    \"الصورة رقم \",\n                                                    imageNumber + 1,\n                                                    \" غير موجودة\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\MarkdownPreview.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-red-300 mt-1\",\n                                                children: \"تأكد من رفع الصورة أو تحديث رقم المرجع\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\MarkdownPreview.tsx\",\n                                                lineNumber: 142,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\MarkdownPreview.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\MarkdownPreview.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 17\n                            }, this)\n                        }, \"missing-image-\".concat(key++), false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\MarkdownPreview.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 15\n                        }, this));\n                    }\n                } else if (part.trim()) {\n                    // معالجة النص العادي مع الحفاظ على التنسيق\n                    const processedText = processInlineMarkdown(part);\n                    if (processedText) {\n                        processedParts.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"inline\",\n                            children: processedText\n                        }, \"text-\".concat(index), false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\MarkdownPreview.tsx\",\n                            lineNumber: 155,\n                            columnNumber: 15\n                        }, this));\n                    }\n                }\n            });\n            return processedParts;\n        };\n        const flushParagraph = ()=>{\n            if (currentParagraph.trim()) {\n                // تحقق من وجود مراجع صور في الفقرة\n                if (currentParagraph.includes('[صورة:')) {\n                    // معالجة مراجع الصور\n                    const processedContent = processImageReferences(currentParagraph.trim());\n                    elements.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4\",\n                        children: processedContent\n                    }, key++, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\MarkdownPreview.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 13\n                    }, this));\n                } else {\n                    // معالجة النص العادي\n                    const processedText = processInlineMarkdown(currentParagraph.trim());\n                    elements.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mb-4 text-dark-text-secondary leading-relaxed\",\n                        children: processedText\n                    }, key++, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\MarkdownPreview.tsx\",\n                        lineNumber: 181,\n                        columnNumber: 13\n                    }, this));\n                // تم إيقاف الإضافة التلقائية للصور - يتم عرض الصور فقط عبر المراجع [صورة:رقم]\n                // أو الروابط المباشرة التي يضعها المحرر يدوياً\n                }\n                currentParagraph = '';\n            }\n        };\n        for(let i = 0; i < lines.length; i++){\n            const line = lines[i];\n            // فيديو YouTube\n            const youtubeMatch = line.match(/\\[youtube\\]([^[]+)\\[\\/youtube\\]/);\n            if (youtubeMatch) {\n                flushParagraph();\n                const url = youtubeMatch[1].trim();\n                const videoId = extractYouTubeId(url);\n                console.log('YouTube URL found:', url);\n                console.log('Extracted Video ID:', videoId);\n                if (videoId) {\n                    elements.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(YouTubeVideo, {\n                        videoId: videoId\n                    }, key++, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\MarkdownPreview.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 25\n                    }, this));\n                } else {\n                    elements.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-red-900/20 border border-red-500/30 rounded-lg p-4 my-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-red-400\",\n                                children: [\n                                    \"رابط يوتيوب غير صحيح: \",\n                                    url\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\MarkdownPreview.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-red-300 text-sm mt-2\",\n                                children: \"تأكد من أن الرابط بالشكل الصحيح مثل: https://www.youtube.com/watch?v=VIDEO_ID\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\MarkdownPreview.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, key++, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\MarkdownPreview.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 13\n                    }, this));\n                }\n                continue;\n            }\n            // معرض الصور\n            const galleryMatch = line.match(/\\[gallery\\]([^[]*)\\[\\/gallery\\]/);\n            if (galleryMatch) {\n                flushParagraph();\n                const params = galleryMatch[1].trim();\n                const [layout = 'grid', columns = '3', spacing = 'normal'] = params.split(',').map((p)=>p.trim());\n                if (articleImages && articleImages.length > 0) {\n                    elements.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ArticleImageGallery__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        images: articleImages,\n                        layout: layout,\n                        columns: parseInt(columns) || 3,\n                        spacing: spacing,\n                        className: \"my-6\"\n                    }, key++, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\MarkdownPreview.tsx\",\n                        lineNumber: 229,\n                        columnNumber: 13\n                    }, this));\n                } else {\n                    elements.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-800/50 border border-gray-600 rounded-lg p-4 my-4 text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-400\",\n                            children: \"لا توجد صور متاحة لعرضها في المعرض\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\MarkdownPreview.tsx\",\n                            lineNumber: 241,\n                            columnNumber: 15\n                        }, this)\n                    }, key++, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\MarkdownPreview.tsx\",\n                        lineNumber: 240,\n                        columnNumber: 13\n                    }, this));\n                }\n                continue;\n            }\n            // العناوين\n            if (line.startsWith('### ')) {\n                flushParagraph();\n                elements.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-semibold text-white mb-3 mt-6\",\n                    children: line.substring(4)\n                }, key++, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\MarkdownPreview.tsx\",\n                    lineNumber: 252,\n                    columnNumber: 11\n                }, this));\n                continue;\n            }\n            if (line.startsWith('## ')) {\n                flushParagraph();\n                elements.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-xl font-semibold text-white mb-4 mt-8\",\n                    children: line.substring(3)\n                }, key++, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\MarkdownPreview.tsx\",\n                    lineNumber: 262,\n                    columnNumber: 11\n                }, this));\n                continue;\n            }\n            if (line.startsWith('# ')) {\n                flushParagraph();\n                elements.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-2xl font-bold text-white mb-6 mt-10\",\n                    children: line.substring(2)\n                }, key++, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\MarkdownPreview.tsx\",\n                    lineNumber: 272,\n                    columnNumber: 11\n                }, this));\n                continue;\n            }\n            // كتل الكود\n            if (line.startsWith('```')) {\n                flushParagraph();\n                const codeLines = [];\n                i++; // تخطي السطر الأول\n                while(i < lines.length && !lines[i].startsWith('```')){\n                    codeLines.push(lines[i]);\n                    i++;\n                }\n                elements.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                    className: \"bg-gray-900 border border-gray-700 rounded-lg p-4 my-4 overflow-x-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                        className: \"text-gray-300 font-mono text-sm\",\n                        children: codeLines.join('\\n')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\MarkdownPreview.tsx\",\n                        lineNumber: 290,\n                        columnNumber: 13\n                    }, this)\n                }, key++, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\MarkdownPreview.tsx\",\n                    lineNumber: 289,\n                    columnNumber: 11\n                }, this));\n                continue;\n            }\n            // القوائم\n            if (line.startsWith('- ') || line.startsWith('* ')) {\n                flushParagraph();\n                const listItems = [];\n                while(i < lines.length && (lines[i].startsWith('- ') || lines[i].startsWith('* '))){\n                    listItems.push(lines[i].substring(2));\n                    i++;\n                }\n                i--; // العودة خطوة واحدة\n                elements.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                    className: \"list-disc list-inside space-y-2 my-4 text-dark-text-secondary\",\n                    children: listItems.map((item, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            className: \"ml-6 mb-2\",\n                            children: processInlineMarkdown(item)\n                        }, idx, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\MarkdownPreview.tsx\",\n                            lineNumber: 310,\n                            columnNumber: 15\n                        }, this))\n                }, key++, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\MarkdownPreview.tsx\",\n                    lineNumber: 308,\n                    columnNumber: 11\n                }, this));\n                continue;\n            }\n            // خط فارغ\n            if (line.trim() === '') {\n                flushParagraph();\n                continue;\n            }\n            // إضافة إلى الفقرة الحالية\n            if (currentParagraph) {\n                currentParagraph += '\\n' + line;\n            } else {\n                currentParagraph = line;\n            }\n        }\n        flushParagraph();\n        return elements;\n    };\n    // معالجة التنسيق داخل النص\n    const processInlineMarkdown = (text)=>{\n        const parts = [];\n        let remaining = text;\n        let key = 0;\n        // معالجة النص العريض\n        remaining = remaining.replace(/\\*\\*(.*?)\\*\\*/g, (match, content)=>{\n            const placeholder = \"__BOLD_\".concat(key++, \"__\");\n            parts.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                className: \"font-semibold text-white\",\n                children: content\n            }, placeholder, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\MarkdownPreview.tsx\",\n                lineNumber: 346,\n                columnNumber: 18\n            }, this));\n            return placeholder;\n        });\n        // معالجة النص المائل\n        remaining = remaining.replace(/\\*(.*?)\\*/g, (match, content)=>{\n            const placeholder = \"__ITALIC_\".concat(key++, \"__\");\n            parts.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                className: \"italic\",\n                children: content\n            }, placeholder, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\MarkdownPreview.tsx\",\n                lineNumber: 353,\n                columnNumber: 18\n            }, this));\n            return placeholder;\n        });\n        // معالجة الكود المضمن\n        remaining = remaining.replace(/`([^`]+)`/g, (match, content)=>{\n            const placeholder = \"__CODE_\".concat(key++, \"__\");\n            parts.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                className: \"bg-gray-800 text-primary px-2 py-1 rounded text-sm font-mono\",\n                children: content\n            }, placeholder, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\MarkdownPreview.tsx\",\n                lineNumber: 361,\n                columnNumber: 9\n            }, this));\n            return placeholder;\n        });\n        // معالجة الروابط\n        remaining = remaining.replace(/\\[([^\\]]+)\\]\\(([^)]+)\\)/g, (match, text, url)=>{\n            const placeholder = \"__LINK_\".concat(key++, \"__\");\n            parts.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                href: url,\n                className: \"text-primary hover:text-primary/80 underline\",\n                target: \"_blank\",\n                rel: \"noopener noreferrer\",\n                children: text\n            }, placeholder, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\MarkdownPreview.tsx\",\n                lineNumber: 372,\n                columnNumber: 9\n            }, this));\n            return placeholder;\n        });\n        // تجميع النتيجة النهائية\n        const result = [];\n        const tokens = remaining.split(/(__[A-Z]+_\\d+__)/);\n        tokens.forEach((token, index)=>{\n            if (token.startsWith('__') && token.endsWith('__')) {\n                const part = parts.find((p)=>/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().isValidElement(p) && p.key === token);\n                if (part) {\n                    result.push(part);\n                }\n            } else if (token) {\n                result.push(token);\n            }\n        });\n        return result.length === 1 ? result[0] : result;\n    };\n    const elements = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"MarkdownPreview.useMemo[elements]\": ()=>parseMarkdownToElements(content)\n    }[\"MarkdownPreview.useMemo[elements]\"], [\n        content\n    ]);\n    if (!content.trim()) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-gray-500 italic p-4 \".concat(className),\n            children: \"لا يوجد محتوى للمعاينة...\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\MarkdownPreview.tsx\",\n            lineNumber: 401,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"prose max-w-none \".concat(className),\n        children: elements\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\MarkdownPreview.tsx\",\n        lineNumber: 408,\n        columnNumber: 5\n    }, this);\n}\n_s(MarkdownPreview, \"1hxn3cRDPRQolY+B1BzOcYKpznQ=\");\n_c1 = MarkdownPreview;\nvar _c, _c1;\n$RefreshReg$(_c, \"YouTubeVideo\");\n$RefreshReg$(_c1, \"MarkdownPreview\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/MarkdownPreview.tsx\n"));

/***/ })

});