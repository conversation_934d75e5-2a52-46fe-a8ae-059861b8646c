'use client';

import { useEffect, useState, useRef } from 'react';
import { getAds } from '@/lib/supabase-ads';

interface FlashAdRendererProps {
  position: 'header' | 'sidebar' | 'footer' | 'in-content' | 'popup' | 'floating' | 'sticky';
  currentPage: string;
  className?: string;
  maxAds?: number;
}

interface FlashAd {
  id: string;
  name: string;
  html_content: string;
  click_url: string;
  position: string;
  target_pages: string[];
  enabled: boolean;
  priority: number;
  network: string;
  animation_type?: string;
  animation_duration?: number;
}

export function FlashAdRenderer({ position, currentPage, className = '', maxAds = 1 }: FlashAdRendererProps) {
  const [ads, setAds] = useState<FlashAd[]>([]);
  const [isLoaded, setIsLoaded] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentAdIndex, setCurrentAdIndex] = useState(0);
  const containerRef = useRef<HTMLDivElement>(null);

  // Load Flash ads from Supabase
  useEffect(() => {
    const loadFlashAds = async () => {
      try {
        setIsLoaded(false);
        setError(null);
        
        // Get ads from the 'ads' table with network 'flash'
        const fetchedAds = await getAds(position, true);
        
        // Filter for Flash network and target pages
        const flashAds = fetchedAds.filter(ad => {
          // Check if it's a Flash ad
          if (ad.network !== 'flash') return false;
          
          // Check page targeting
          if (ad.target_pages && ad.target_pages.length > 0) {
            const targetPages = Array.isArray(ad.target_pages) ? ad.target_pages : [ad.target_pages];
            
            // Check if current page matches target pages
            const pageMatches = targetPages.some(targetPage => {
              if (targetPage === '*') return true;
              if (targetPage === 'articles' && currentPage.includes('/articles')) return true;
              if (targetPage === 'ai-tools' && currentPage.includes('/ai-tools')) return true;
              if (targetPage === currentPage) return true;
              return false;
            });
            
            if (!pageMatches) return false;
          }
          
          return true;
        });
        
        console.log(`🎯 Flash ads loaded for ${position} on ${currentPage}:`, flashAds.length);
        setAds(flashAds.slice(0, maxAds));
        setIsLoaded(true);
        
      } catch (err) {
        console.error('Error loading Flash ads:', err);
        setError(err instanceof Error ? err.message : 'Failed to load ads');
        setIsLoaded(true);
      }
    };

    loadFlashAds();
  }, [position, currentPage, maxAds]);

  // Rotate ads if multiple ads available
  useEffect(() => {
    if (ads.length <= 1) return;

    const interval = setInterval(() => {
      setCurrentAdIndex((prev) => (prev + 1) % ads.length);
    }, 10000); // Change ad every 10 seconds

    return () => clearInterval(interval);
  }, [ads.length]);

  // Render ad content
  const renderAdContent = (ad: FlashAd) => {
    if (!ad.html_content) return null;

    return (
      <div
        key={ad.id}
        className={`flash-ad-container ${ad.animation_type || 'fadeIn'}`}
        style={{
          animationDuration: `${ad.animation_duration || 1000}ms`,
        }}
        dangerouslySetInnerHTML={{ __html: ad.html_content }}
      />
    );
  };

  // Don't render if no ads or still loading
  if (!isLoaded || ads.length === 0) {
    return null;
  }

  // Don't render if there's an error
  if (error) {
    console.warn(`Flash ad error for ${position}:`, error);
    return null;
  }

  const currentAd = ads[currentAdIndex];

  return (
    <div 
      ref={containerRef}
      className={`flash-ad-wrapper ${className}`}
      data-position={position}
      data-page={currentPage}
    >
      {renderAdContent(currentAd)}
      
      {/* Ad indicator for multiple ads */}
      {ads.length > 1 && (
        <div className="flex justify-center mt-2 space-x-1">
          {ads.map((_, index) => (
            <button
              key={index}
              className={`w-2 h-2 rounded-full transition-colors ${
                index === currentAdIndex ? 'bg-gray-600' : 'bg-gray-300'
              }`}
              onClick={() => setCurrentAdIndex(index)}
              aria-label={`عرض الإعلان ${index + 1}`}
            />
          ))}
        </div>
      )}
      
      <style jsx>{`
        .flash-ad-container {
          opacity: 0;
          animation-fill-mode: forwards;
        }
        
        .fadeIn {
          animation-name: fadeIn;
        }
        
        .slideIn {
          animation-name: slideIn;
        }
        
        .pulse {
          animation-name: pulse;
        }
        
        @keyframes fadeIn {
          from { opacity: 0; transform: translateY(20px); }
          to { opacity: 1; transform: translateY(0); }
        }
        
        @keyframes slideIn {
          from { opacity: 0; transform: translateX(20px); }
          to { opacity: 1; transform: translateX(0); }
        }
        
        @keyframes pulse {
          0%, 100% { transform: scale(1); }
          50% { transform: scale(1.05); }
        }
        
        .flash-ad-wrapper {
          margin: 1rem auto;
          text-align: center;
        }
        
        .flash-ad-wrapper[data-position="sidebar"] {
          max-width: 300px;
        }
        
        .flash-ad-wrapper[data-position="in-content"] {
          max-width: 400px;
        }
        
        .flash-ad-wrapper[data-position="header"],
        .flash-ad-wrapper[data-position="footer"] {
          max-width: 100%;
        }
      `}</style>
    </div>
  );
}

// Specific position components
export function FlashHeaderAd(props: Omit<FlashAdRendererProps, 'position'>) {
  return <FlashAdRenderer {...props} position="header" />;
}

export function FlashSidebarAd(props: Omit<FlashAdRendererProps, 'position'>) {
  return <FlashAdRenderer {...props} position="sidebar" />;
}

export function FlashFooterAd(props: Omit<FlashAdRendererProps, 'position'>) {
  return <FlashAdRenderer {...props} position="footer" />;
}

export function FlashInContentAd(props: Omit<FlashAdRendererProps, 'position'>) {
  return <FlashAdRenderer {...props} position="in-content" />;
}

export function FlashPopupAd(props: Omit<FlashAdRendererProps, 'position'>) {
  return <FlashAdRenderer {...props} position="popup" />;
}

export function FlashFloatingAd(props: Omit<FlashAdRendererProps, 'position'>) {
  return <FlashAdRenderer {...props} position="floating" />;
}

export function FlashStickyAd(props: Omit<FlashAdRendererProps, 'position'>) {
  return <FlashAdRenderer {...props} position="sticky" />;
}
