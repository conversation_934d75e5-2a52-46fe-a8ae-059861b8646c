أميري ١٫٠٠٢ (١٥-٠٥-٢٠٢٥)
------------------------

إصدارة تصليح أخطاء.

* تحسين تموضع السكون وعلامة الجزم.
* إزالة ضبط المسافات بين الأرقام المجدولة.
* إصلاح فئة علامة ringcomb في الخط العريض.

أميري ١٫٠٠١ (١٩-١١-٢٠٢٤)
------------------------

إصدارة ١٫٠٠٠ كان يفترض أن تكون النهائية، لكن ظهرت بها بعض المشاكل التي تستدعي إصدارة جديدة تصلح هذه الأخطاء.

* إصلاح مشكلة موضع علامات التشكيل في بعض إصدارات أدوبي إن ديزاين.
* إصلاح إدراج كشيدتين قبل العين في كلمات مثل «يبلِّغ».
* إستخدام خاصية `rlig` بدلا من `calt` لأن كل الاستبدالات في الخط الأميري ضرورية ويجب ألا يسمح بعدم تفعيلهم.

أميري ١٫٠٠٠ (٠٣-١٢-٢٠٢٢)
------------------------

**هذه هى الإصدارة النهائية**

* بعض التصويبات.

أميري ٠٫٩٠٠ (٠٥-١٠-٢٠٢٢)
------------------------
هذه الإصدارة تمهيد للإصدارة النهائية.
* إزالة الحل الالتفافي لتعطيل المحاذاة بالكشيدة، برنامج ليبرأوفيس أصبح يتعامل مع
  الكشيدة بشكل أفضل.
* مصادر الخط تستخدم برنامج جليفز وفونتفورج لم يعد مطلوبا.
* ترجمة أسماء الخصائص الأسلوبية.
* بعض التصويبات.

أميري ٠٫١١٧ (٠١-٠٧-٢٠٢٢)
------------------------
إصلاح تلون بعض الحروف بالخطأ في القرآن الملون.

أميري ٠٫١١٦ (٢٥-٠٦-٢٠٢٢)
------------------------
إصلاح مشكلة تسبب عدم تلون النقاط في خط القرآن الملون.

أميري ٠٫١١٥ (٢٣-٠٦-٢٠٢٢)
------------------------
* تحسينات مختلفة لملاقاة معايير الجودة في خطوط جوجل.
* تحسين تموضع الهمزة الصغيرة تحت الألف المقصورة.
* دعم الخط العلوي في كل الخطوط وليس خطوط القرآن فقط.
* إضافة المحارف اللاتينية الأساسية للخطوط القرآنية.

أميري ٠٫١١٤ (٠٨-٠٣-٢٠٢٢)
------------------------
* استخدام مربع حدود الخط لقيم OS/2.usWinAscent و OS/2.usWinDescent.
* تحسين أرقام الكسور
* تحسين تموضع الفتحة فوق الضمة (https://github.com/alif-type/amiri/issues/200).
* إضافة علامة نهاية الخطاب (U+061D).
* إضافة علامتي الجنيه والقرش (U+0890–0891).
* ملفات التوثيق صارة بصيغة HTML.
* جعل البدائل العربية من المحارف المشتركة هي المبدئية، وجعل الأشكال اللاتينية بدائل عنها.
* جعل المزيد من المحارف المشتركة غير مائلة في الخطوط المائلة.
* جعل شكل الكاف ذات الحلقة التحتية (U+06AB) مستمدا من الكاف الفارسية لا العربية (https://github.com/aliftype/amiri/issues/196).
* دعم علامات التشكيل فوق ألف الوصل (https://github.com/aliftype/amiri/issues/186).
* إصلاح تداخل النقاط في كلمة ”الحجى“ (https://github.com/aliftype/amiri/issues/192).
* إصلاح الحروف التي تستخدم الهمزة العالية (https://github.com/aliftype/amiri/issues/173).
* إصلاح تداخل علامات التشكيل في ”يِحِ يِحٍ يٍحِ يٍحٍ“ (https://github.com/aliftype/amiri/issues/162).
* إصلاح تداخل علامات التشكيل في ”لَله“ (https://github.com/aliftype/amiri/issues/184).
* إصلاح البسملة في الخط القرآني الملون.
* إضافة دعم الفاصلة والفاصلة المنقوطة المعكوستين (U+2E41 و U+204F).
* إضافة دعم المحارف U+08BB–08BD.
* إصلاح موضع النقطة التحتية في حرف الفاء بنقطق فوقية وتحتية (U+06A3).
* دعم وضع الألف الخنجرية فوق المسافة الضيقة غير القاطعة (https://github.com/aliftype/amiri/issues/217).
* إصلاح انعكاس شكل علامتي يمين إلى يسار ويسار إلى يمين.
* إصلاح موضع الفتحة فوق الهمزة الصغيرة (https://github.com/aliftype/amiri/issues/227).

أميري ٠٫١١٣ (٠٢-٠٦-٢٠٢٠)
------------------------
* إضافة U+08BA، و U+08B6، و U+08B7، و U+08B8، و U+08B9.
* إصلاح المشاكل:
  - https://github.com/aliftype/amiri/issues/172
  - https://github.com/aliftype/amiri/issues/187
* بناء الخطوط لم يعد يتطلب فونتفورج.

أميري ٠٫١١٢ (٢٩-٠٩-٢٠١٩)
------------------------
* إصلاح علامة النقطة التحتية لتكون مستديرة لا مربعة.
* إضافة ملف Amiri.fontspec ليستحدم مع حزمة fontspec للاتخ (#139).
* إضافة فاصلة وفاصلة منقوطة بديلة للسندية والجاوية، ويمكنتفعيلها أيضا بخاصية
  “ss08” (#145).
* إصلاح تموضع بعض علامات تشكيل الروهينجا (#155).
* جعل المسافة الضيقة غير الفاصلة مساوية في العرض للمسافة الرفيعة (#177).
* إصلاح الحروف القرآنية الصغيرة لتوافق معايير يونيكود الحديثة.

أميري ٠٫١١١ (٣٠-١٢-٢٠١٧)
------------------------
* تحسين اقتران الكافات التي أضيفت في الإصدارة السابقة.
* إصلاح خطأ يجعل جرف الهمزة العالية (U+0674) يقترن بالهرف السابق عليه:
  https://github.com/aliftype/amiri/issues/138.
* إزالة الأسماء العربية الداخلية للخط، لتفادي ازدواجية اللغة في اسم الخط في بعض
  التطبيقات عندما يكون النظام باللغة العربية، مثل ”Amiri عادي“.
* السماح بأن تسبق أو تلحق همزة وسط الكلمة بالكشيدة:
  https://github.com/aliftype/amiri/issues/137
* تلوين علامة الهمزة المنفصلة في الخط القرآني الملون:
  https://github.com/aliftype/amiri/issues/136
* تغيير شكل الضمة في حرفي U+06C7 و U+0677:
  https://github.com/aliftype/amiri/issues/123
* تغييرات شغيرة أخرى، و تغييرات على نظام بناء الخط من المصدر.

أميري ٠٫١١٠ (٢٠-١٢-٢٠١٧)
-----------------------
* محارف جديدة:
    - علامة سموت (U+0604)
    - بديل للراء الرياضياتية (U+1EE13)
    - الأرقام الكشميرية.
    - بديل للكاف المتبوعة بهاء وسطية.

* تحسينات:
    - إصلاح عرض خط أميري قرآن الملون على وندوز.
    - تحسين تموضع الهمزة المكسورة في خط أميري قرآن.
    - جعل علامة نصف القطر تشبه ”نق“ و ليس ”ق“ فقط.
    - جعل عرض علامة النقطة الموسطة (U+00B7) مثل عرض المسافة.
    - إبقاء علامة التعجب غير مائلة في الخط المائل.
    - جعل تركيب لفظ الجلالة يعمل أفضل على بعض البرمجيات المعيبة.
    - جعل كلمات ”فالله/بالله/تالله/والله“ تأخذ تركيب لفظ الجلالة.
    - تحسين حرف الهمزة العالية (U+0674).
    - تحسين علامة U+0600 لتأتي من كلمة ”نمرة“ بالأردية.
    - تحسين تموضع النون الصغيرة فوق الكشيدة.
    - السماح بالتشكيل فوق النون و الياء الصغيرتين.
    - تحسين الكشيدة المقوسة تحسينًا كبيرًا.
    - إبطال تركيبا ”لبمـ“ و ”ببحـ“ في خط القرآن.

* خصائص جديدة:
    - خاصية `ss07` لتعطيل الكشيدة المقوسة.
    - بعض التحسينات على التوثيق.

أميري ٠٫١٠٩ (٢٠-١١-٢٠١٦)
----------------------
* محارف جديدة:
    - دعم لعدم الوضع التلقائي للشدة و الألف الصغيرة (◌ّٰ ) فوق لفظ الجلالة مع
      خاصية `ss06` لله.
    - نسخة عربية من علامة &.
    - علامة ”𞻰“ الرياضياتية (U+1EEF0).
    - علامة ”𞻱“ الرياضياتية (U+1EEF1).
    - إضافة التنويعات الناقصة لعلامة U+06C2.

* تحسينات:
    - إصلاح اختفاء بعض علامات التشكيل في ميكروسوفت أوفيس.
    - وضع أسماء للخصائص الاختيارية في الخط.
    - الكثير من التحسينات على خط القرآن الملون، مع ألوان جديدة مساهمة من منذر طه.
    - تحسينات و إعادة تصميم للكثير من رموز الرياضيات العربية.
    - https://github.com/khaledhosny/quran-data/issues/1
    - https://github.com/khaledhosny/quran-data/issues/4
    - https://github.com/aliftype/amiri/issues/90
    - https://github.com/aliftype/amiri/issues/106

أميري ٠٫١٠٨ (٢١-٠٩-٢٠١٥)
------------------------
* محارف جديدة:
    - تصميم جديد للهاء في ـهي.
    - تنويعات من الأرقام للاستخدام في الكسور الاعتيادية، عبر خاصيتي `numr`
      و `dnom`.
    - علامات التشكيل الجديد من قسم الحروف العربية الممتد-أ، في المدى
      U+08E4–08FE.
    - إعادة تصميم أشكال U+06C1 لتتميز عن U+06BE.
    - رمز ”جل جلاله“، U+FDFB.

* تحسينات:
    - الفتحتين أو الضمتين أو الكسرتين المتتاليتين لن تستبدل بالتنوين المتراكب،
      إذ الصواب U+08F0، أو U+08F1، أو U+08F2.
    - الكثير من تحسينات التآلف.
    - حل مشكلة مع ماك أو إس عشرة كانت تمنع ظهور لفظ الجلالة بشكل سليم.
    - إزالة النقاط من كل أشكال U+06BA، اتباعًا لمعيار يونيكود.
    - الأشكال الأولية و الوسطية من الحروف التالية لم تكن موجودة: U+063E،
      و U+063F، و U+077A، و U+077B.
    - نقطا أشكال U+06BD الأولي والوسطي يجب أن تُقلب.

* الخطوط الجديدة:
    - نسخة ملونة من خط القرآن يلون فيها التشكيل و العلامات القرآنية. لا تظهر
      الألون حاليًا إلا على متصفحات فيرفكس (كل المنصات) و إنترنت إكسبلورر\إدج
      (وندوز 8٫1 فما فوق).
    - أضيف إلى خطوط الوب WOFF 2.0.

أميري ٠٫١٠٧ (٣٠-١٢-٢٠١٣)
------------------------
* محارف جديدة:
    - الحروف الرياضياتية العربية من قسم الرموز الرياضياتية العربية في يونيكود
      (U+1EE00–U+1EEFF).
    - دعم اختياري لوضع الكسرة أسفل الشدة، مع خاصية `ss05`.
    - إضافة الأرقام متغيرة العرض الناقصة من الخط المائل.

* تحسينات:
    - التراجع عن خفض علامات التشكيل فوق الحروف العريضة، إذ جعلت علامات التشكيل غريبة
      بالنسبة لباقي الحروف.
    - إصلاح موضع التشكيل فوق قاف ”قح“.
    - إلغاء تركيبة ”تمخـ“ إن كانت مشكاة كلها لأنها ضيقة لتستوعب التشكيل.
    - إصلاح موضع السكون فوق الشدة.
    - توسيع الفراغ على يمين ”ثر“ و ”ثن“ و أخواتها لكيلا تتعارض مع المحارف السابقة
      عليها.
    - إبعاد المسافة بين راء و ياء ”مرين“ و ما جارها.
    - التأليف بين النقطة و علامة التنصيص الغالقة.

أميري ٠٫١٠٦ (٢٨-٠٥-٢٠١٣)
------------------------
* محارف جديدة:
    - شكل جديد لحرف گاف يتماشى مع المتعارف عليه، الشكل القديم يمكن تفعيله عبر
      خاصية `ss04`.
    - إعادة رسم الأرقام الفارسية.
    - شكل جديد للضمة المقلوبة، الشكل القديم مفعل تلقائي عند اختيار اللغة الأردية.
    - المزيد من الأشكال المناسبة للحروف المتبوعة بالياء الراجعة النهائية.

* تحسينات:
    - زيادة المسافة بين الألف و الثاء في مثل ”أثر“ و ”أثن“.
    - تحسن بعض الحروف الثخينة.
    - إصلاح موضع حلقة U+0620 في عدة مواضع.
    - خفض علامات التشكيل فوق الحروف العريضة.
    - إعادة كتابة كود العلامات الضامة للحروف ليكون أسرع.
    - تقصير الألف النهائية المسبوقة بالتطويل قليلا.
    - علامات اقتباس أكبر.
    - زيادة زاوية ميل الخط المائل.
    - استخدام أرقام متوسطة الحجم بدلا من الصغيرة مع علامة الرقم و الصفحة.
    - الكثير من التحسينات الصغيرة الأخرى.

* الترخيص:
    - إزالة شرط تغيير اسم الخط في حال تعديله؛ النسخ المعدلة يمكنها استخدام نفس الاسم.

* مسودة لدليل الاستخدام (بالعربية فقط حاليًا).


أميري ٠٫١٠٥ (٣١-١٢-٢٠١٢)
------------------------
* خط قرآني جديد:

  تحتوي هذه الإصدارة على خط ”أميري قرآن“ منفصل لتنضيد النصوص القرآنية، وهو فرع
  من خط ”أميري عادي“ مع بعض الإعدادات والخصائص المبدئية المُطوعة لتناسب تنضيد
  النص القرآني لكن لا تناسب النصوص العادية. مثلا:
    - يغطي فقط المحارف المستخدمة في النصوص القرآنية.
    - ارتفاع السطر أكبر ليستوعب علامات الوقف.
    - الهمزة المكسورة على نبرة أو واو تذهب تحت كرسيها.
    - يدعم محرف السطر العلوي (U+0305) الذي يمكن استخدامه لرسم سطر السجدة في حال
      عدم توفر دعم مناسب للتسطير العلوي.
    - الشكل الخاص للفظ الجلالة مفعل في كل الأحوال بغض النظر عن الحروف أو علامات
      التشكيل المحيطة به، كما لا تُدرج شدة فوقه تلقائيا.
    - بعض التراكيب التي تسبب مشاكل مع التشكيل الكامل مُعطّلة.

* الكثير من التحسينات على الأبعاد، وتموضع التشكيل، والتآلف، وأشكال الحروف. راجع
  سجل مستودع التطوير للاطلاع عليها كلها.

أميري ٠٫١٠٤ (١٩-٠٧-٢٠١٢)
------------------------
* محارف جديدة:
    - شرطة مائلة خاصة لتناسب الأرقام العربية.
    - شكل جديد أكثر تناسقا لعلامة @ العربية.
    - إضافة أرقام متناسبة العرض (خاصية `pnum`).

* تحسينات:
    - توسيع المسافة حول الواو الصغيرة المنخفضة.
    - إصلاح سنة السين عند تفعيل خاصية `ss02`.
    - إصلاح الراء ذات علامة ٨ فوقها.
    - تحسين الكاف المتوسطة في ـكما.
    - جعل العلامات الضامة للأرقام تعمل مع فيرفكس (وغيره من التطبيقات المبنية على
      حرف‌باز).
    - علامة العدد العربية (U+0600) تقبل الآن رقما رابعا، كما أصبحت أوسع قليلا لتفادي
      التلامس مع الأرقام الواسعة.
    - إصلاح ارتباط علامة المد اللازم مع الضمة المقلوبة.
    - إزالة ارتباط Th.
    - إصلاحات متنوعة أخرى.

* التآلف.
    - تأليف اللام ألف النهائية مع الكاف.

أميري ٠٫١٠٣ (٣١-٠٥-٢٠١٢)
------------------------
* هذه إصدارة صغيرة لإصلاح بعض العلات:
    - إصلاح التآلف السيئ بين الأرقام مع علامة نهاية الآية وغيرها من العلامات الضامة.
    - إضافة رموز ظاهرة لمحارف التحكم في ثنائية الاتجاه.
    - تأليف المزيد من الكافات عبر الفاصلة المجازية.
    - إصلاح معالجة ميلان المحارف المشتركة إلى اليمين أو إلى اليسار في الخط المائل
      ليصبح أكثر منطقية.

أميري ٠٫١٠٢ (٢٢-٠٥-٢٠١٢)
------------------------
* محارف جديدة:
    - أكبر تغيير في هذه الإصدارة هو إضافة دعم نظام الكتابة اللاتيني مبنيًا على خط
      Crimson يغطي خرائط محارف لاتيني-0 إلى 9 بالإضافة إلى الحروف المستعملة في
      أنظمة رومنة العربية الشائعة (باستثناء الأبجدية الصوتية الصوتية) و العديد من
      علامات الترقيم الشائعة الأخرى.
    - باء مع ٧ صغيرة سفلية (U+08A0).
    - علامة البسملة (U+FDFD).

* تحسينات:
    - شكل جديد للهاء المفتوحة المتبوعة بالياء النهائية.
    - شكل جديد للفاء المتوسطة المتبوعة بالياء النهائية.
    - شكل جديد للهاء المعقودة المتبوعة بالياء الراجعة.
    - شكل جديد محسن ومفتوح لهمزة الوصل.
    - شدة وسكون قرآني وعادي أكبر وأوضح.
    - علامة صلى الله عليه وسلم أكبر وأوضح.
    - تحسين تموضع نقاط عائلة الباء البادئة.
    - تحسين تموضع نقاط الباء المسبوقة بكاف ومتبوعة بألف أو لام.
    - تحسين تموضع الألف الخنجرية على الحروف العادية.
    - ألف مدة نهائية أوسع لتفادي تعارض المدة مع الحرف التالي.
    - تحسين شكل الكاف المتبوعة بميم ولام، أو ألف، على الشاشة في الأحجام الصغيرة.
    - تحسين الكاف المتبوعة بلام ثم ميم نهائية.
    - تفادي التصاق الباء البادئة أو المتوسطة بنقاط الحرف التالي.
    - تحسين موضع اتصال الصاد المتوسطة والنهائية.

* التآلف:
    - جدول تآلف أحسن أداء يستخدم التموضع السياقي.
    - تقليل تآلف الدال أو الراء مع الكاف لتفادي التصاق النقاط.
    - زيادة تباعد الراء عن الياء البادئة.
    - جعل التآلف عبر الفاصلة المجازية يعمل في محرك ميكروسوفت.

* إصلاح العلات: #1347860، #3471042، #3475146، #3509875

* متفرقات:
    - تكرار خاصية `locl` في `ccmp` للتحايل على المحركات التي لا تدعم الأولى.
    - ملفات EOT أصغر مضغوطة بتقنية MTX.

أميري ٠٫١٠١ (٢٧-١٢-٢٠١١)
------------------------
* الخطوط الجديدة:
    - أضيف في هذه الإصدارة خط سميك، وإن كان بحاجة لبعض التحسين ليصل إلى مستوى الخط
      العادي، لكن حالته لا بأس بها.
    - خط عريض مائل.

* محارف جديدة:
    - علامة صلى الله عليه وسلم (U+FDFD).
    - الأقواس المحلات (U+FD3E, U+FD3F).
    - العلامات التعليمية العربية (U+FBB2-U+FBC1).
    - غالب محارف قسمي أشكال العرض أ و ب.

* تحسينات:
    - إصلاح تحرك نقاط الياء في بعض برامج أبل.
    - تفعيل الشكل المحلي من النقطة وأقواس الاقتباس مع اللغتين الأردية والسندية.
    - إصلاح عدم تفعيل جداول التموضع مع اللغتين الأردية والسندية.
    - إصلاح خطأ شكل الباء إذا أتبعت بسين ثم هاء متوسطتين كما في ”بسهل“.
    - إصلاح خطأ تموضع بعض النقاط.
    - إصلاح خطأ تموضع الهمزة فوق الهاء المتطرفة.
    - توسيع الألف النهائية لتصبح أقل حدة ولألا تلتصق بتشكيل ما جاورها.
    - إصلاح تلامس تشكيل اللام المتوسطة مع نقطة الياء النهائية كما في ”هلِي“.
    - دعم الأرقام الغربية مع العلامات الضامة.
    - استخدام أرقام أكبر مع علامة السنة ليزيد وضوحها.
    - منع تكرار الباء العالية إذا وقعت بين سينين كما في ”سببس“.

أميري ٠٫١٠٠ (٠٤-١٢-٢٠١١)، بيتا جاما دلتا
----------------------------------------
* تمثل هذه الإصدارة محطة هامة في تطوير الخط إذ يغطى الآن كل المحارف العربية في
  الإصدار السادس من يونيكود، كما نضُج الخط بشكل كبير وصار من الممكن الاعتماد
  عليه في أغلب الأغراض الطباعية.

* الخطوط الجديدة:
    - أضيفت نسخة مائلة تميل إلى اليسار وليس إلى اليمين، بحيث تماشي اتجاه الكتابة
      العربية.

* محارف جديدة:
    - العلامات الضامة (U+0600-0603).
    - فاصلة التاريخ، أردو (U+060D).
    - علامة بيت الشعر، أردو (U+060E).
    - علامات التبجيل (U+0610-0614).
    - هاء دو چشمی، أردو (U+06BE and U+06FF).
    - ياء بڑی، أردو (U+06D2 and U+06D3).
    - أربع أحجام للكشيدة.

* تحسينات:
    - إصلاح مشكلة الكشيدة في إنديزاين.
    - إمالة رقم أربعة الأردي ليصبح مقبولا أكثر.
    - إصلاح اختفاء نقطة الخاء المتوسطة إذا سبقتها كاف أو لام، كما في ”كخا“.
    - تقليل ارتفاع اللام البادئة المتبوعة بحاء وميم متوسطتين -كما في ”لحمد“-
      لتماثل أطوال اللامات الأخرى.
    - عين فنجانية وحاء مغلقة إذا تبعت العين أو الحاء بكاف.
    - خفض الواو الصغيرة بعد الهاء.
    - توسيع الواو الصغيرة والألف النهائية إذا وضع عليها مدة.
    - توسيع جوانب الحروف المنقوطة ولمهموزة لئلا تلامس ما جاورها.
    - المزيد من التنويعات العريضة من الحروف لتفادي تلامس علامات الشكل عند التشكيل
      الكامل.
    - راء ثعبانية إذا سبقت الراء بعين بالراء، كما في ”غر“.
    - شكل جديد للألف النهائية التي تسبقها كشيدة، كما في ”عمـان“.
    - شكل جديد للياء النهائية المسبوقة بهاء مفتوحة،كما في ”نهى“.
    - تغيير شكل الكاف النهائية والمتوسطة إلى شكل جديد أفضل لا يلامس مع ما
      جاورهما من الحروف.
    - تغيير شكل الواو النهائية إلى شكل أقرب للتصميم الأصلي.
    - جعل خفض نقاط الباء المسبوقة بواو أو راء اختياري، ونقلها إلى المجموعة
      التجميلية 01.
    - جعل الشكل الخاص للميم المتوسطة المتبوعة بألف اختياري، ونقلها إلى المجموعة
      التجميلية 02.
    - عرض كل الأرقام موحد الآن.
    - أخذت الأرقام المغربية وعلامات الترقيم المشتركة من خط Crimson Text.
    - تغيير المسافات بين السطور قليلا بحيث تناسب الآن النصوص العادية.
    - المزيد من التحسينات المتنوعة هنا وهناك.

أميري ٠٫٠١٦ (٢٢-٠٩-٢٠١١)، بداية النهاية
---------------------------------------
* تحتوي هذه الإصدارة على دعم كامل للقرآن الكريم، وهي محطة هامة أخرى في تطوير
* هذا الخط. لكن يبقى الكثير من العمل لتحسين التفاعل بين الحروف وخاصة تموضع
* علامات الشكل في النصوص تامة التشكيل مثل القرآن.

* محارف جديدة:
    - كل علامات الضبط القرآني في يونيكود 6٫0.
    - باقي علامات التشكيل العربية في يونيكود 6٫0.
    - علامة الجذر (U+221A) مع تنويعة معكوسة للاستخدام العربي، بالإضافة إلى الجذور
      العربية الأخرى (U+0606, U+0607).
    - علامة نصف القطر (U+0608).
    - رمز العملة الأفغانية (U+060B).
    - علامة ”مصرع“ الأردية (U+060F).
    - تنويعة من الميم الأولية إذا تبعت بهاء متوسطة كما في ”مها“.

* التآلف:
  - انخفض عدد أزواج التآلف من ٤١١٢٤٠ إلى ٥٥٨٥٠ مع الحصول على نفس النتيجة.

* تحسينات:
    - تحسينات عامة على علامات الترقيم، وضبط لتباعد الأقواس مع تسميك الأقواس
      المجعدة لتتماشى أكثر مع باقي الخط.
    - أصبح عرض المسافة ٦٠٠ وحدة.
    - أصلح إدراج الكشائد في أماكن خطأ عند مساواة الفقرات.
    - أضيفت تنويعات عريضة من بعض الحروف لتفادي تلامس علامات الشكل عند التشكيل
      الكامل.
    - ضبط موضع السطر السفلي ليكون أخفض من أغلب الحروف النازلة عن السطر.
    - إصلاحات عديدة على تموضع علامات التشكيل.
    - فصلت الحلقة أسفل الياء الكشميرية عن جسم الحرف لتتبع أسلوب الكتابة الكشميري.

أميري ٠٫٠١٥ (١٤-٠٧-٢٠١١)، العنقاء
---------------------------------
* تمثل هذه الإصدارة محطة هامة في تطوير الخط إذ أعيد برمجة بالكامل للوصول إلى
  أكبر قدر من التوافقية مع البرمجيات المختلفة التي تدعم أوبن تيب.

* تحسينات:
    - المزيد من توحيد الحروف المتشابهة.
    - إصلاح العيد من تحذيرات فونت فورج.
    - ضبط العديد من مواضع النقاط.
    - ضبط أفضل لتموضع الكثير من علامات التشكيل.
    - مزيد من التصليحات لتركيب لفظ الجلالة.
    - تحسين شكل الباء المبتدئة عند اتصالها بالألف النهائية.

* المزيد من أزواج التآلف.

* اسم الخط يظهر الآن بالإنجليزية في قوائم الخطوط.


أميري ٠٫٠١٤ (٠٥-٠٦-٢٠١١)، اكسر ساقا
-----------------------------------
* إصدارة فرعية أخرى لإصلاح علتين:
    - اختفاء لفظ الجلالة في برمجات وندوز وأوفس ميكروسوفت.
    - عدم تموضع النقاط تحت الياء المتطرفة.


أميري ٠٫٠١٣ (٢٧-٠٤-٢٠١١)، أسرع!
-------------------------------
* إصدارة لإصلاح تموضع علامات التشكيل فوق الحروف المتآلفة في وندوز.

أميري ٠٫٠١٢ (٢٦-٠٤-٢٠١١)، ليلة طويلة
--------------------------------------
* محارف جديدة:
    - إضافة تنويعات الأرقام الأردية والسندية (خاصية `locl`).
    - إضافة خاصية أرقام الجداول (`tnum`).
    - إضافة الأرقام المغربية (الأوروبية) وبعض علامات الترقيم من خط Linux Libertine.
    - إضافة تنويعة عربية تجريبية من علامة @.
    - إضافة الثلاث نقاط التي تستخدم بدل نقطو نهاية الجملة في بعض اللغات الإفريقية
      (U+061E).
    - إضافة النقطة المُوسّطة (U+00B7).
    - أعيد برمجة تركيب لفظ الجلالة مع إضافة دعم ”فلله“؛ برمجة الخط تحاول قدر
      الإمكان تفادي استخدام تلك التنويعة في الألفاظ التي تشبه لفظ الجلالة.

* تحسينات:
    - تنظيف شامل للخط وحذف لكثير من المحارف المتشابهة بحيث أصبح الخط أكثر
      انسجاما وتناغما.
    - نتج عن هذا التنظيف دعم تراكيب أكثر من ذي قبل مع أن حجم الخط أصبح أصغر وليس
      أكبر.
    - صُغّرت علامات التشكيل بنسبة ٨٠٪ لتفادي تلامسها.
    - الأرقام العربية أكثر اتساقا الآن.
    - العديد من الإصلاحات المتنوعة في التشكيل ومواضع النقاط.
    - دعم لتطبيقات أكثر من ذي قبل

* المزيد من أزواج التآلف.

* إصلاح العلات: 3234138، 3110760، 3087332، 3073139، 3211187، 3211239 و 3078741

أميري ٠٫٠١١ (٣١-٠٣-٢٠١١)، نفخ العجلة
------------------------------------
* محارف جديدة:
    - `"#'*,-/;[\]{|}¦`
    - علامات الاقتباس المحدبة المفردة والمزدوجة: `‹›«»،` مع بديل عربي هلالي الشكل.
    - علامات الاقتباس المجعدة: `‟„”“‛‚’‘`
    - رمز `⁂`
    - شرطة الكسور: `⁄`
    - علامة النسبة المؤوية العربية: `٪؊؉`
    - الفاصلة العشرية العربية وفاصلة الألوف: `٫٬`
    - النجمة الخماسية: `٭`
    - الشرطات الطباعية: `‒ – — ―`
    - الحروف العربية ذات الشرطة: `ۅ ݛ ݪ`
    - الحروف العربية ذات الأرقام: `ݳ ݴ ݵ ݶ ݷ ݸ ݹ ݼ ݽ`

* إصلاحات متنوعة:
    - تحديث بيانات الخط.
    - زيادة حجم الأرقام بنسبة ١٢٠٪ ورفعها لأعلى قليلا.
    - تحسينات متنوعة على علامات الترقيم والرموز الرياضياتية.
    - ضبط التشكيل فوق الهمزة على السطر (ء).
    - توسيط التشكيل تحت الهاء المفردة (ه).

أميري ٠٫٠١٠ (٢١-٠٣-٢٠١١)، الاستفتاء
----------------------------------
* محارف جديدة:
    - لام بثلاث نقاط تحتية U+06B8.

* التآلف:
    - لام ”له“ مع عائلة الراء والواو السابقين لها.
    - وكذلك كاف ”كتب“.

* إصلاحات المحارف:
    - إصلاح موضع علامة التعجب أفقيا مقارنة مع علامة الاستفهام.
    - إزالة همزة زائدة خطأ من كاف بداية ووسط الكلمة U+063B و U+063C.
    - إصلاح سماكة الدال المنفصلة كونها أسمك من باقي الحروف.
    - إصلاح اتصال اللام بالميم في ”لما“.
    - تصغير حجم العين المتوسطة لتتناسب رأس العين المتطرفة.

أميري ٠٫٠٠٩ (٠٦-٠٢-٢٠١١)، الثورة
--------------------------------
* محارف جديدة:
    - دعم أولي للتشكيل، لكن ما زال ينقصه الكثير من الضبط.
    - الأرقام العربية والفارسية.
    - المزيد من علامات الترقيم.
    - المزيد من الحروف العربية الإضافية
    - المزيد من التراكيب وخاصة الأزواج المنتهية بالحاء.

* محاولة لإصلاح علة عرض الخط في أوبن‌أوفِس، يعمل بشكل أفضل بكثير الآن، لكن الحل
  الحقيقي للعلة أُرسِل إلى مطوري ليبرأوفِس لكنه لم يلحق الإصدارة 3٫3٫0.
* مصدر الخط موفر على شكل ملف SFD بدلا من SFDIR الحالي ليسهل فتحه في فونتفورج
  لمن يرغب.
* زيدت المسافة بين الأسطر لتستوعب التشكيل على نحو أفضل.
* الكثير من التحسين والتوسع في أزواج التآلف.
* عشرات من الإصلاحات والتحسينات الصغيرة الأخرى.

* إصلاحات العلل:
    - 3085159 التصاق الكاف مع الحرف الذي يليه
    - 3085165 التصاق همزة الألف مع السابق واللاحق
    - 3085166 التصاق الراء والزاي مع الياء اللاحقة
    - 3085172 التصاق الكاف باللام السابقة المتصلة بالميم
    - 3085174 خطأ في رسم لا إذا سبقتها الكاف المتصلة بالميم
    - 3085175 تلاصق الحاء والجيم والخاء إذا تتابعت
    - 3101634 أزواج تآلف منسية
    - 3101674 العين الفنجانية

أميري ٠٫٠٠٣ (١٠-١٠-٢٠١٠)
------------------------
* تحسين أكثر للتآلف وخاصة معالجة تلامس نقاط الحروف المتآلفة.
* حل تلامس كثير من الحروف وخاصة اللام مع الكاف، والكاف مع المعجمات الفوقية
  بعدها، والباء مع الألف.
* أضيف دعم اللغة الفارسية وستليها باقي اللغات تباعا.

أميري ٠٫٠٠٢ (٢٥-٠٩-٢٠١٠)
------------------------
* تحسين التآلف بين الحروف:

  حُسن تآلف (تقريب المسافات) بين الحروف أكثر، وأصبح يغطي طيفا أوسع من أشكال
  الحروف وخاصة التنويعات السياقية، كما حلت مشكلة تحرك بعض النقاط في وندوز
  بسبب حدوث تعارض بين تموضع النقاط والتأليف.

* تحسين تموضع النقاط:

  حُسن تموضع بعض النقاط، لكن ما تزال بحاجة إلى المزيد من العمل.

* تصغير حجم الملف:

  أصبح حجم ملف الخط غير المضغوط أصغر بحوالي ٢٥٪ عن ذي قبل، وهو ما يفيد في حال
  استخدامه على الوب.

أميري ٠٫٠٠١ (١٩-٠٩-٢٠١٠)
-----------------------
أول إصدارة.

