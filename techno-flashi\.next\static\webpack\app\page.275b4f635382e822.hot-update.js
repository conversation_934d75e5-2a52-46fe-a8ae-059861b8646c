"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/SimpleHeroSection.tsx":
/*!**********************************************!*\
  !*** ./src/components/SimpleHeroSection.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SimpleHeroSection: () => (/* binding */ SimpleHeroSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ SimpleHeroSection auto */ \nvar _s = $RefreshSig$();\n\n\nfunction SimpleHeroSection() {\n    _s();\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const animationRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    const mouseRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        x: 0,\n        y: 0\n    });\n    const particlesRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SimpleHeroSection.useEffect\": ()=>{\n            const canvas = canvasRef.current;\n            if (!canvas) return;\n            const ctx = canvas.getContext('2d');\n            if (!ctx) return;\n            // إعداد الكانفاس\n            const resizeCanvas = {\n                \"SimpleHeroSection.useEffect.resizeCanvas\": ()=>{\n                    canvas.width = window.innerWidth;\n                    canvas.height = window.innerHeight;\n                }\n            }[\"SimpleHeroSection.useEffect.resizeCanvas\"];\n            resizeCanvas();\n            window.addEventListener('resize', resizeCanvas);\n            // إنشاء الجسيمات الذكية المتطورة مع تحسينات جديدة\n            const createParticles = {\n                \"SimpleHeroSection.useEffect.createParticles\": ()=>{\n                    const particles = [];\n                    const colors = [\n                        '#8b5cf6',\n                        '#a855f7',\n                        '#c084fc',\n                        '#d8b4fe',\n                        '#e879f9',\n                        '#f0abfc',\n                        '#fbbf24',\n                        '#f59e0b',\n                        '#06b6d4',\n                        '#0891b2',\n                        '#10b981',\n                        '#059669',\n                        '#f97316',\n                        '#ea580c',\n                        '#dc2626',\n                        '#b91c1c',\n                        '#7c3aed',\n                        '#6d28d9'\n                    ];\n                    for(let i = 0; i < 200; i++){\n                        particles.push({\n                            x: Math.random() * canvas.width,\n                            y: Math.random() * canvas.height,\n                            vx: (Math.random() - 0.5) * 2,\n                            vy: (Math.random() - 0.5) * 2,\n                            size: Math.random() * 8 + 2,\n                            color: colors[Math.floor(Math.random() * colors.length)],\n                            opacity: Math.random() * 0.8 + 0.2,\n                            pulse: Math.random() * Math.PI * 2,\n                            pulseSpeed: 0.008 + Math.random() * 0.04,\n                            rotationSpeed: (Math.random() - 0.5) * 0.05,\n                            magnetism: Math.random() * 0.8 + 0.6,\n                            trail: [],\n                            energy: Math.random() * 100 + 50,\n                            type: Math.floor(Math.random() * 3),\n                            phase: Math.random() * Math.PI * 2,\n                            amplitude: Math.random() * 20 + 10 // سعة الحركة الموجية\n                        });\n                    }\n                    particlesRef.current = particles;\n                }\n            }[\"SimpleHeroSection.useEffect.createParticles\"];\n            createParticles();\n            // معالج حركة الماوس\n            const handleMouseMove = {\n                \"SimpleHeroSection.useEffect.handleMouseMove\": (e)=>{\n                    mouseRef.current = {\n                        x: e.clientX / window.innerWidth * 2 - 1,\n                        y: -(e.clientY / window.innerHeight) * 2 + 1\n                    };\n                }\n            }[\"SimpleHeroSection.useEffect.handleMouseMove\"];\n            window.addEventListener('mousemove', handleMouseMove);\n            // دالة رسم السداسي\n            const drawHexagon = {\n                \"SimpleHeroSection.useEffect.drawHexagon\": (ctx, x, y, size)=>{\n                    ctx.beginPath();\n                    for(let i = 0; i < 6; i++){\n                        const angle = i * Math.PI / 3;\n                        const px = x + size * Math.cos(angle);\n                        const py = y + size * Math.sin(angle);\n                        if (i === 0) {\n                            ctx.moveTo(px, py);\n                        } else {\n                            ctx.lineTo(px, py);\n                        }\n                    }\n                    ctx.closePath();\n                    ctx.stroke();\n                }\n            }[\"SimpleHeroSection.useEffect.drawHexagon\"];\n            // حلقة الرسم المتطورة مع تأثيرات مبهرة\n            const animate = {\n                \"SimpleHeroSection.useEffect.animate\": ()=>{\n                    // مسح تدريجي بدلاً من المسح الكامل\n                    ctx.fillStyle = 'rgba(255, 255, 255, 0.05)';\n                    ctx.fillRect(0, 0, canvas.width, canvas.height);\n                    const time = Date.now() * 0.001;\n                    const mouseX = mouseRef.current.x * canvas.width * 0.5 + canvas.width * 0.5;\n                    const mouseY = mouseRef.current.y * canvas.height * 0.5 + canvas.height * 0.5;\n                    // رسم هالة تتبع الماوس المتوهجة\n                    if (mouseRef.current.x !== 0 || mouseRef.current.y !== 0) {\n                        const mouseGlowSize = 100 + Math.sin(time * 3) * 20;\n                        const mouseGradient = ctx.createRadialGradient(mouseX, mouseY, 0, mouseX, mouseY, mouseGlowSize);\n                        mouseGradient.addColorStop(0, \"rgba(168, 85, 247, \".concat(0.15 + Math.sin(time * 2) * 0.05, \")\"));\n                        mouseGradient.addColorStop(0.3, \"rgba(236, 72, 153, \".concat(0.1 + Math.sin(time * 2.5) * 0.03, \")\"));\n                        mouseGradient.addColorStop(0.6, \"rgba(251, 191, 36, \".concat(0.05 + Math.sin(time * 3) * 0.02, \")\"));\n                        mouseGradient.addColorStop(1, 'rgba(255, 255, 255, 0)');\n                        ctx.fillStyle = mouseGradient;\n                        ctx.beginPath();\n                        ctx.arc(mouseX, mouseY, mouseGlowSize, 0, Math.PI * 2);\n                        ctx.fill();\n                        // رسم دائرة مركزية صغيرة متوهجة\n                        const centerGradient = ctx.createRadialGradient(mouseX, mouseY, 0, mouseX, mouseY, 15);\n                        centerGradient.addColorStop(0, \"rgba(255, 255, 255, \".concat(0.8 + Math.sin(time * 4) * 0.2, \")\"));\n                        centerGradient.addColorStop(1, 'rgba(168, 85, 247, 0)');\n                        ctx.fillStyle = centerGradient;\n                        ctx.beginPath();\n                        ctx.arc(mouseX, mouseY, 15, 0, Math.PI * 2);\n                        ctx.fill();\n                        // رسم موجات تنتشر من موضع الماوس\n                        for(let i = 0; i < 3; i++){\n                            const waveRadius = 50 + (time * 100 + i * 50) % 200;\n                            const waveAlpha = Math.max(0, 0.3 - waveRadius / 200 * 0.3);\n                            if (waveAlpha > 0.01) {\n                                ctx.strokeStyle = \"rgba(168, 85, 247, \".concat(waveAlpha, \")\");\n                                ctx.lineWidth = 2 - waveRadius / 200;\n                                ctx.beginPath();\n                                ctx.arc(mouseX, mouseY, waveRadius, 0, Math.PI * 2);\n                                ctx.stroke();\n                            }\n                        }\n                    }\n                    // رسم موجات ديناميكية في الخلفية\n                    const waveCount = 5;\n                    for(let i = 0; i < waveCount; i++){\n                        ctx.beginPath();\n                        ctx.strokeStyle = \"rgba(168, 85, 247, \".concat(Math.max(0.01, 0.02 + Math.sin(time + i) * 0.01), \")\");\n                        ctx.lineWidth = 2;\n                        for(let x = 0; x <= canvas.width; x += 10){\n                            const y = canvas.height * 0.5 + Math.sin(x * 0.01 + time + i) * 50 * (i + 1) + Math.sin(x * 0.005 + time * 0.5 + i) * 30;\n                            if (x === 0) {\n                                ctx.moveTo(x, y);\n                            } else {\n                                ctx.lineTo(x, y);\n                            }\n                        }\n                        ctx.stroke();\n                    }\n                    // رسم شبكة سداسية متحركة\n                    const hexSize = 60;\n                    const hexRows = Math.ceil(canvas.height / (hexSize * 0.75)) + 2;\n                    const hexCols = Math.ceil(canvas.width / (hexSize * Math.sqrt(3))) + 2;\n                    ctx.strokeStyle = \"rgba(139, 92, 246, \".concat(Math.max(0.01, 0.04 + Math.sin(time * 0.5) * 0.02), \")\");\n                    ctx.lineWidth = 1;\n                    for(let row = 0; row < hexRows; row++){\n                        for(let col = 0; col < hexCols; col++){\n                            const x = col * hexSize * Math.sqrt(3) + row % 2 * hexSize * Math.sqrt(3) * 0.5;\n                            const y = row * hexSize * 0.75;\n                            const distanceToMouse = Math.sqrt((x - mouseX) ** 2 + (y - mouseY) ** 2);\n                            const influence = Math.max(0, 1 - distanceToMouse / 300); // زيادة نطاق التأثير\n                            if (influence > 0.05) {\n                                // تحسين الحركة والحجم حسب قرب الماوس\n                                const moveIntensity = influence * 15;\n                                const sizeMultiplier = 0.2 + influence * 0.8;\n                                const alpha = 0.02 + influence * 0.1;\n                                ctx.strokeStyle = \"rgba(139, 92, 246, \".concat(alpha, \")\");\n                                ctx.lineWidth = 1 + influence * 2;\n                                drawHexagon(ctx, x + Math.sin(time + row + col) * moveIntensity, y + Math.cos(time + row + col) * moveIntensity, hexSize * sizeMultiplier);\n                            }\n                        }\n                    }\n                    // رسم دوائر متوهجة ناعمة\n                    const glowCircles = 5;\n                    for(let i = 0; i < glowCircles; i++){\n                        const x = canvas.width * (0.1 + i * 0.2);\n                        const y = canvas.height * (0.2 + Math.sin(time * 0.3 + i) * 0.3);\n                        const radius = Math.max(10, 150 + Math.sin(time * 0.4 + i) * 50);\n                        const gradient = ctx.createRadialGradient(x, y, 0, x, y, radius);\n                        const colors = [\n                            \"rgba(168, 85, 247, \".concat(Math.max(0.01, 0.05 + Math.sin(time + i) * 0.02), \")\"),\n                            \"rgba(236, 72, 153, \".concat(Math.max(0.01, 0.03 + Math.sin(time + i + 1) * 0.015), \")\"),\n                            \"rgba(251, 191, 36, \".concat(Math.max(0.01, 0.02 + Math.sin(time + i + 2) * 0.01), \")\")\n                        ];\n                        gradient.addColorStop(0, colors[i % colors.length]);\n                        gradient.addColorStop(0.7, colors[(i + 1) % colors.length].replace(/[\\d.]+\\)/, '0.01)'));\n                        gradient.addColorStop(1, 'rgba(255, 255, 255, 0)');\n                        ctx.fillStyle = gradient;\n                        ctx.beginPath();\n                        ctx.arc(x, y, radius, 0, Math.PI * 2);\n                        ctx.fill();\n                    }\n                    // رسم أشكال هندسية متحركة\n                    const shapes = 3;\n                    for(let i = 0; i < shapes; i++){\n                        const x = canvas.width * (0.3 + i * 0.2);\n                        const y = canvas.height * (0.4 + Math.cos(time * 0.2 + i) * 0.2);\n                        const size = Math.max(10, 30 + Math.sin(time + i) * 10);\n                        const rotation = time * 0.5 + i;\n                        ctx.save();\n                        ctx.translate(x, y);\n                        ctx.rotate(rotation);\n                        const gradient = ctx.createLinearGradient(-size, -size, size, size);\n                        gradient.addColorStop(0, \"rgba(168, 85, 247, \".concat(Math.max(0.01, 0.1 + Math.sin(time + i) * 0.05), \")\"));\n                        gradient.addColorStop(1, \"rgba(236, 72, 153, \".concat(Math.max(0.01, 0.05 + Math.cos(time + i) * 0.03), \")\"));\n                        ctx.fillStyle = gradient;\n                        ctx.beginPath();\n                        ctx.roundRect(-size / 2, -size / 2, size, size, 8);\n                        ctx.fill();\n                        ctx.restore();\n                    }\n                    // تحديث ورسم الجسيمات الذكية المتطورة\n                    particlesRef.current.forEach({\n                        \"SimpleHeroSection.useEffect.animate\": (particle, index)=>{\n                            // التأكد من صحة القيم\n                            particle.size = Math.max(1, particle.size || 2);\n                            particle.energy = Math.max(50, Math.min(100, particle.energy || 50));\n                            particle.opacity = Math.max(0.1, Math.min(1, particle.opacity || 0.5));\n                            // حفظ الموضع السابق للمسار\n                            particle.trail.push({\n                                x: particle.x,\n                                y: particle.y\n                            });\n                            if (particle.trail.length > 5) {\n                                particle.trail.shift();\n                            }\n                            // تحديث الموضع مع فيزياء متقدمة\n                            particle.x += particle.vx;\n                            particle.y += particle.vy;\n                            particle.pulse += particle.pulseSpeed;\n                            // تأثير الماوس المغناطيسي المحسن والأكثر تفاعلاً\n                            const mouseInfluence = 200; // زيادة نطاق التأثير\n                            const dx = mouseX - particle.x;\n                            const dy = mouseY - particle.y;\n                            const distance = Math.sqrt(dx * dx + dy * dy);\n                            if (distance < mouseInfluence) {\n                                const force = (mouseInfluence - distance) / mouseInfluence * particle.magnetism;\n                                const angle = Math.atan2(dy, dx);\n                                // زيادة قوة التفاعل\n                                particle.vx += Math.cos(angle) * force * 0.008;\n                                particle.vy += Math.sin(angle) * force * 0.008;\n                                // تأثير الطاقة المحسن\n                                particle.energy = Math.min(100, particle.energy + force * 4);\n                                // إضافة تأثير اهتزاز ناعم\n                                particle.vx += (Math.random() - 0.5) * force * 0.002;\n                                particle.vy += (Math.random() - 0.5) * force * 0.002;\n                            } else {\n                                // تقليل الطاقة تدريجياً\n                                particle.energy = Math.max(50, particle.energy - 0.3);\n                            }\n                            // تطبيق الاحتكاك\n                            particle.vx *= 0.99;\n                            particle.vy *= 0.99;\n                            // حدود الشاشة مع ارتداد ديناميكي\n                            if (particle.x < 0 || particle.x > canvas.width) {\n                                particle.vx *= -0.7;\n                                particle.x = Math.max(0, Math.min(canvas.width, particle.x));\n                            }\n                            if (particle.y < 0 || particle.y > canvas.height) {\n                                particle.vy *= -0.7;\n                                particle.y = Math.max(0, Math.min(canvas.height, particle.y));\n                            }\n                            // رسم مسار الجسيمة\n                            if (particle.trail.length > 1) {\n                                ctx.beginPath();\n                                ctx.strokeStyle = particle.color.replace(')', ', 0.2)').replace('rgb', 'rgba');\n                                ctx.lineWidth = 1;\n                                for(let i = 0; i < particle.trail.length - 1; i++){\n                                    const alpha = i / particle.trail.length;\n                                    ctx.globalAlpha = alpha * 0.3;\n                                    if (i === 0) {\n                                        ctx.moveTo(particle.trail[i].x, particle.trail[i].y);\n                                    } else {\n                                        ctx.lineTo(particle.trail[i].x, particle.trail[i].y);\n                                    }\n                                }\n                                ctx.stroke();\n                            }\n                            // رسم الجسيمة مع تأثيرات متقدمة\n                            const energyFactor = particle.energy / 100;\n                            const pulseSize = Math.max(1, particle.size + Math.sin(particle.pulse) * 2 * energyFactor);\n                            const pulseOpacity = Math.max(0.1, particle.opacity + Math.sin(particle.pulse) * 0.3 * energyFactor);\n                            // رسم الهالة الخارجية\n                            const outerRadius = Math.max(1, pulseSize * 4);\n                            const outerGradient = ctx.createRadialGradient(particle.x, particle.y, 0, particle.x, particle.y, outerRadius);\n                            outerGradient.addColorStop(0, particle.color.replace(')', ', 0.4)').replace('rgb', 'rgba'));\n                            outerGradient.addColorStop(0.5, particle.color.replace(')', ', 0.2)').replace('rgb', 'rgba'));\n                            outerGradient.addColorStop(1, particle.color.replace(')', ', 0)').replace('rgb', 'rgba'));\n                            ctx.fillStyle = outerGradient;\n                            ctx.globalAlpha = pulseOpacity * 0.6 * energyFactor;\n                            ctx.beginPath();\n                            ctx.arc(particle.x, particle.y, outerRadius, 0, Math.PI * 2);\n                            ctx.fill();\n                            // رسم الهالة الداخلية\n                            const innerRadius = Math.max(1, pulseSize * 2);\n                            const innerGradient = ctx.createRadialGradient(particle.x, particle.y, 0, particle.x, particle.y, innerRadius);\n                            innerGradient.addColorStop(0, particle.color.replace(')', ', 0.8)').replace('rgb', 'rgba'));\n                            innerGradient.addColorStop(1, particle.color.replace(')', ', 0.2)').replace('rgb', 'rgba'));\n                            ctx.fillStyle = innerGradient;\n                            ctx.globalAlpha = pulseOpacity * 0.8;\n                            ctx.beginPath();\n                            ctx.arc(particle.x, particle.y, innerRadius, 0, Math.PI * 2);\n                            ctx.fill();\n                            // رسم الجسيمة الأساسية\n                            ctx.beginPath();\n                            ctx.arc(particle.x, particle.y, Math.max(0.5, pulseSize), 0, Math.PI * 2);\n                            ctx.fillStyle = particle.color;\n                            ctx.globalAlpha = Math.max(0.1, Math.min(1, pulseOpacity));\n                            ctx.fill();\n                            // رسم خطوط الاتصال الذكية المتطورة\n                            particlesRef.current.forEach({\n                                \"SimpleHeroSection.useEffect.animate\": (otherParticle, otherIndex)=>{\n                                    if (index !== otherIndex && index < otherIndex) {\n                                        const dx = particle.x - otherParticle.x;\n                                        const dy = particle.y - otherParticle.y;\n                                        const distance = Math.sqrt(dx * dx + dy * dy);\n                                        if (distance < 150) {\n                                            const opacity = Math.max(0, 0.2 * (1 - distance / 150));\n                                            const energyBonus = (particle.energy + otherParticle.energy) / 200;\n                                            const finalOpacity = Math.max(0.01, Math.min(1, opacity * (1 + energyBonus)));\n                                            // خط متدرج ديناميكي\n                                            const gradient = ctx.createLinearGradient(particle.x, particle.y, otherParticle.x, otherParticle.y);\n                                            const color1 = particle.color.replace(')', ', ' + finalOpacity + ')').replace('rgb', 'rgba');\n                                            const color2 = otherParticle.color.replace(')', ', ' + finalOpacity + ')').replace('rgb', 'rgba');\n                                            const midColor = \"rgba(192, 132, 252, \".concat(finalOpacity * 1.2, \")\");\n                                            gradient.addColorStop(0, color1);\n                                            gradient.addColorStop(0.5, midColor);\n                                            gradient.addColorStop(1, color2);\n                                            // رسم خط متموج\n                                            ctx.beginPath();\n                                            ctx.strokeStyle = gradient;\n                                            ctx.lineWidth = 1 + finalOpacity * 3;\n                                            const steps = 10;\n                                            for(let i = 0; i <= steps; i++){\n                                                const t = i / steps;\n                                                const x = particle.x + (otherParticle.x - particle.x) * t;\n                                                const y = particle.y + (otherParticle.y - particle.y) * t;\n                                                // إضافة تموج ناعم\n                                                const waveOffset = Math.sin(t * Math.PI * 2 + time) * 5 * finalOpacity;\n                                                const perpX = -(otherParticle.y - particle.y) / distance;\n                                                const perpY = (otherParticle.x - particle.x) / distance;\n                                                const finalX = x + perpX * waveOffset;\n                                                const finalY = y + perpY * waveOffset;\n                                                if (i === 0) {\n                                                    ctx.moveTo(finalX, finalY);\n                                                } else {\n                                                    ctx.lineTo(finalX, finalY);\n                                                }\n                                            }\n                                            ctx.stroke();\n                                            // إضافة نقاط ضوئية على الخط\n                                            if (finalOpacity > 0.1) {\n                                                const midX = (particle.x + otherParticle.x) / 2;\n                                                const midY = (particle.y + otherParticle.y) / 2;\n                                                ctx.beginPath();\n                                                ctx.arc(midX, midY, Math.max(0.5, 2 * finalOpacity), 0, Math.PI * 2);\n                                                ctx.fillStyle = \"rgba(255, 255, 255, \".concat(Math.max(0.1, finalOpacity * 0.8), \")\");\n                                                ctx.fill();\n                                            }\n                                        }\n                                    }\n                                }\n                            }[\"SimpleHeroSection.useEffect.animate\"]);\n                        }\n                    }[\"SimpleHeroSection.useEffect.animate\"]);\n                    ctx.globalAlpha = 1;\n                    animationRef.current = requestAnimationFrame(animate);\n                }\n            }[\"SimpleHeroSection.useEffect.animate\"];\n            animate();\n            // التنظيف\n            return ({\n                \"SimpleHeroSection.useEffect\": ()=>{\n                    if (animationRef.current) {\n                        cancelAnimationFrame(animationRef.current);\n                    }\n                    window.removeEventListener('resize', resizeCanvas);\n                    window.removeEventListener('mousemove', handleMouseMove);\n                }\n            })[\"SimpleHeroSection.useEffect\"];\n        }\n    }[\"SimpleHeroSection.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-white via-purple-50 to-pink-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                ref: canvasRef,\n                className: \"absolute inset-0 w-full h-full\",\n                style: {\n                    zIndex: 1\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                lineNumber: 466,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-t from-white/30 via-transparent to-purple-50/20\",\n                style: {\n                    zIndex: 2\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                lineNumber: 473,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 text-center px-4 max-w-6xl mx-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-5xl md:text-7xl font-bold text-gray-900 mb-6 leading-tight\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"bg-gradient-to-r from-purple-600 via-pink-600 to-purple-600 bg-clip-text text-transparent\",\n                                    children: \"TechnoFlash\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                    lineNumber: 479,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 478,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl md:text-2xl text-gray-700 mb-8 max-w-3xl mx-auto leading-relaxed\",\n                                children: \"منصتك الشاملة لأحدث التقنيات وأدوات الذكاء الاصطناعي\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 483,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                        lineNumber: 477,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row gap-4 justify-center items-center mb-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/articles\",\n                                className: \"group bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-4 rounded-xl font-semibold text-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"استكشف المقالات\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 494,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5 group-hover:translate-x-1 transition-transform\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M13 7l5 5m0 0l-5 5m5-5H6\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                            lineNumber: 496,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 495,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 490,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/ai-tools\",\n                                className: \"group border-2 border-purple-400 text-purple-400 px-8 py-4 rounded-xl font-semibold text-lg hover:bg-purple-400 hover:text-white transition-all duration-300 flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"أدوات الذكاء الاصطناعي\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 504,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5 group-hover:rotate-12 transition-transform\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M13 10V3L4 14h7v7l9-11h-7z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                            lineNumber: 506,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 505,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 500,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                        lineNumber: 489,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-2\",\n                                        children: \"100+\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 514,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-600\",\n                                        children: \"مقال تقني\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 515,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 513,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-2\",\n                                        children: \"50+\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 518,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-600\",\n                                        children: \"أداة ذكاء اصطناعي\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 519,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 517,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-2\",\n                                        children: \"1000+\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 522,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-600\",\n                                        children: \"قارئ نشط\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 523,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 521,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                        lineNumber: 512,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                lineNumber: 476,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-8 left-1/2 transform -translate-x-1/2 z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-bounce\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-8 h-8 border-2 border-gray-400/60 rounded-full flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-4 h-4 text-gray-600/80\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M19 14l-7 7m0 0l-7-7m7 7V3\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 533,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                            lineNumber: 532,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                        lineNumber: 531,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                    lineNumber: 530,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                lineNumber: 529,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n        lineNumber: 464,\n        columnNumber: 5\n    }, this);\n}\n_s(SimpleHeroSection, \"/8IKySTmGZxFymijsBTHUGtuaQs=\");\n_c = SimpleHeroSection;\nvar _c;\n$RefreshReg$(_c, \"SimpleHeroSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/SimpleHeroSection.tsx\n"));

/***/ })

});