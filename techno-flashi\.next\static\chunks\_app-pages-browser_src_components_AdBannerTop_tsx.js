"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_components_AdBannerTop_tsx"],{

/***/ "(app-pages-browser)/./src/components/AdBannerTop.tsx":
/*!****************************************!*\
  !*** ./src/components/AdBannerTop.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdBannerTop)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction AdBannerTop(param) {\n    let { placement = \"homepage-top\", className = \"\" } = param;\n    _s();\n    const [bannerAd, setBannerAd] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdBannerTop.useEffect\": ()=>{\n            fetchBannerAd();\n        }\n    }[\"AdBannerTop.useEffect\"], [\n        placement\n    ]);\n    const fetchBannerAd = async ()=>{\n        try {\n            const response = await fetch(\"/api/ads?type=banner&placement=\".concat(placement, \"&is_active=true&limit=1\"));\n            if (response.ok) {\n                const data = await response.json();\n                if (data.ads && data.ads.length > 0) {\n                    setBannerAd(data.ads[0]);\n                }\n            } else {\n                console.error('Failed to fetch banner ad:', response.status);\n            }\n        } catch (error) {\n            console.error('Error fetching banner ad:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleAdClick = async ()=>{\n        if (!bannerAd) return;\n        // تسجيل النقرة\n        try {\n            await fetch(\"/api/ads/\".concat(bannerAd.id, \"/click\"), {\n                method: 'POST'\n            });\n        } catch (error) {\n            console.error('Failed to record click:', error);\n        }\n        // فتح الرابط\n        if (bannerAd.link_url) {\n            if (bannerAd.target_blank) {\n                window.open(bannerAd.link_url, '_blank');\n            } else {\n                window.location.href = bannerAd.link_url;\n            }\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full bg-gray-100 animate-pulse \".concat(className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-24 bg-gray-300 rounded-lg\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\AdBannerTop.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\AdBannerTop.tsx\",\n                lineNumber: 66,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\AdBannerTop.tsx\",\n            lineNumber: 65,\n            columnNumber: 7\n        }, this);\n    }\n    if (!bannerAd) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full bg-gradient-to-r from-primary/5 to-primary/10 border-b border-primary/20 \".concat(className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative group cursor-pointer\",\n                    onClick: handleAdClick,\n                    title: bannerAd.description || bannerAd.title,\n                    children: [\n                        bannerAd.image_url ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative overflow-hidden rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        src: bannerAd.image_url,\n                                        alt: bannerAd.title,\n                                        width: bannerAd.width || 728,\n                                        height: bannerAd.height || 90,\n                                        className: \"object-contain max-w-full h-auto\",\n                                        style: {\n                                            filter: 'drop-shadow(0 4px 8px rgba(0,0,0,0.1))'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\AdBannerTop.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-primary/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-lg\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\AdBannerTop.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\AdBannerTop.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\AdBannerTop.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 13\n                        }, this) : /* إعلان نصي */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center p-6 bg-white rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border-2 border-primary/20\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-bold text-gray-900 mb-2\",\n                                    children: bannerAd.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\AdBannerTop.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 15\n                                }, this),\n                                bannerAd.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mb-4\",\n                                    children: bannerAd.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\AdBannerTop.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"inline-flex items-center text-primary font-semibold\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"اضغط للمزيد\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\AdBannerTop.tsx\",\n                                            lineNumber: 116,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-4 h-4 mr-2 group-hover:translate-x-1 transition-transform duration-300\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M15 19l-7-7 7-7\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\AdBannerTop.tsx\",\n                                                lineNumber: 118,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\AdBannerTop.tsx\",\n                                            lineNumber: 117,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\AdBannerTop.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\AdBannerTop.tsx\",\n                            lineNumber: 106,\n                            columnNumber: 13\n                        }, this),\n                        bannerAd.ad_code && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                dangerouslySetInnerHTML: {\n                                    __html: bannerAd.ad_code\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\AdBannerTop.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\AdBannerTop.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"sr-only\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        \"إعلان: \",\n                                        bannerAd.title\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\AdBannerTop.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 13\n                                }, this),\n                                bannerAd.sponsor_name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        \" - برعاية \",\n                                        bannerAd.sponsor_name\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\AdBannerTop.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\AdBannerTop.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\AdBannerTop.tsx\",\n                    lineNumber: 80,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mt-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full\",\n                        children: \"إعلان\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\AdBannerTop.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\AdBannerTop.tsx\",\n                    lineNumber: 143,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\AdBannerTop.tsx\",\n            lineNumber: 79,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\AdBannerTop.tsx\",\n        lineNumber: 78,\n        columnNumber: 5\n    }, this);\n}\n_s(AdBannerTop, \"W6u5rhS4JjoEzvwkFxPZ9AxSkus=\");\n_c = AdBannerTop;\nvar _c;\n$RefreshReg$(_c, \"AdBannerTop\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/AdBannerTop.tsx\n"));

/***/ })

}]);