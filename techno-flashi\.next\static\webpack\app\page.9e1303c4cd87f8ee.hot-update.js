"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/PerformanceOptimizer.tsx":
/*!*************************************************!*\
  !*** ./src/components/PerformanceOptimizer.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PerformanceOptimizer: () => (/* binding */ PerformanceOptimizer),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ PerformanceOptimizer,default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n// Lazy load non-critical components\nconst LazyFeaturedArticlesSection = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.lazy)(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_FeaturedArticlesSection_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ./FeaturedArticlesSection */ \"(app-pages-browser)/./src/components/FeaturedArticlesSection.tsx\")).then((module)=>({\n            default: module.FeaturedArticlesSection\n        })));\n_c = LazyFeaturedArticlesSection;\nconst LazyServicesSection = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.lazy)(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_ServicesSection_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ./ServicesSection */ \"(app-pages-browser)/./src/components/ServicesSection.tsx\")).then((module)=>({\n            default: module.ServicesSection\n        })));\n_c1 = LazyServicesSection;\nconst LazyAdBannerTop = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.lazy)(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_AdBannerTop_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ./AdBannerTop */ \"(app-pages-browser)/./src/components/AdBannerTop.tsx\")).then((module)=>({\n            default: module.default\n        })));\n_c2 = LazyAdBannerTop;\n// Loading skeleton components\nconst ArticlesSkeleton = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"py-16 px-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-8 bg-gray-200 rounded w-64 mx-auto mb-4 animate-pulse\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n                            lineNumber: 23,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-4 bg-gray-200 rounded w-96 mx-auto animate-pulse\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n                            lineNumber: 24,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n                    lineNumber: 22,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                    children: [\n                        ...Array(6)\n                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-xl border border-gray-200 overflow-hidden\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-48 bg-gray-200 animate-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n                                    lineNumber: 29,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-gray-200 rounded w-3/4 mb-2 animate-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n                                            lineNumber: 31,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-gray-200 rounded w-1/2 mb-4 animate-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n                                            lineNumber: 32,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-3 bg-gray-200 rounded w-full mb-2 animate-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n                                            lineNumber: 33,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-3 bg-gray-200 rounded w-2/3 animate-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n                                            lineNumber: 34,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n                                    lineNumber: 30,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, i, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 11\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n            lineNumber: 21,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n        lineNumber: 20,\n        columnNumber: 3\n    }, undefined);\n_c3 = ArticlesSkeleton;\nconst ServicesSkeleton = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"py-16 px-4 bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-8 bg-gray-200 rounded w-48 mx-auto mb-4 animate-pulse\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n                            lineNumber: 47,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-4 bg-gray-200 rounded w-80 mx-auto animate-pulse\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n                    lineNumber: 46,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                    children: [\n                        ...Array(3)\n                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-xl p-8 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-16 h-16 bg-gray-200 rounded-lg mx-auto mb-6 animate-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n                                    lineNumber: 53,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-6 bg-gray-200 rounded w-32 mx-auto mb-4 animate-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 bg-gray-200 rounded w-full mb-2 animate-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 bg-gray-200 rounded w-3/4 mx-auto animate-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, i, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 11\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n                    lineNumber: 50,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n            lineNumber: 45,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n        lineNumber: 44,\n        columnNumber: 3\n    }, undefined);\n_c4 = ServicesSkeleton;\nconst AdSkeleton = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"ad-banner bg-background-secondary border border-light-border rounded-lg\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-20 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-text-description text-sm\",\n                children: \"إعلان\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n                lineNumber: 67,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n            lineNumber: 66,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n        lineNumber: 65,\n        columnNumber: 3\n    }, undefined);\n_c5 = AdSkeleton;\nconst LazyComponent = (param)=>{\n    let { children, fallback, rootMargin = '100px', threshold = 0.1 } = param;\n    _s();\n    const [ref, isIntersecting] = useIntersectionObserver({\n        rootMargin,\n        threshold\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        children: isIntersecting ? children : fallback\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n        lineNumber: 92,\n        columnNumber: 5\n    }, undefined);\n};\n_s(LazyComponent, \"FtwX3XzyHW6LsNpWjTaSblHYEHs=\", false, function() {\n    return [\n        useIntersectionObserver\n    ];\n});\n_c6 = LazyComponent;\nfunction PerformanceOptimizer(param) {\n    let { latestArticles, latestServices = [] } = param;\n    _s1();\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PerformanceOptimizer.useEffect\": ()=>{\n            setIsClient(true);\n        }\n    }[\"PerformanceOptimizer.useEffect\"], []);\n    if (!isClient) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AdSkeleton, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n                    lineNumber: 114,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ArticlesSkeleton, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n                    lineNumber: 115,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ServicesSkeleton, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n                    lineNumber: 116,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LazyComponent, {\n                fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AdSkeleton, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 32\n                }, void 0),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n                    fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AdSkeleton, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 29\n                    }, void 0),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LazyAdBannerTop, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n                    lineNumber: 125,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n                lineNumber: 124,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LazyComponent, {\n                fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ArticlesSkeleton, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n                    lineNumber: 131,\n                    columnNumber: 32\n                }, void 0),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n                    fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ArticlesSkeleton, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 29\n                    }, void 0),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LazyFeaturedArticlesSection, {\n                        articles: latestArticles\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n                lineNumber: 131,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LazyComponent, {\n                fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ServicesSkeleton, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n                    lineNumber: 138,\n                    columnNumber: 32\n                }, void 0),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n                    fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ServicesSkeleton, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 29\n                    }, void 0),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LazyServicesSection, {\n                        services: latestServices\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n                    lineNumber: 139,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n                lineNumber: 138,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s1(PerformanceOptimizer, \"k460N28PNzD7zo1YW47Q9UigQis=\");\n_c7 = PerformanceOptimizer;\n// Hook for intersection observer\nfunction useIntersectionObserver() {\n    let { rootMargin = '0px', threshold = 0.1 } = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    _s2();\n    const [ref, setRef] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isIntersecting, setIsIntersecting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"useIntersectionObserver.useEffect\": ()=>{\n            if (!ref) return;\n            const observer = new IntersectionObserver({\n                \"useIntersectionObserver.useEffect\": (param)=>{\n                    let [entry] = param;\n                    if (entry.isIntersecting) {\n                        setIsIntersecting(true);\n                        observer.disconnect();\n                    }\n                }\n            }[\"useIntersectionObserver.useEffect\"], {\n                rootMargin,\n                threshold\n            });\n            observer.observe(ref);\n            return ({\n                \"useIntersectionObserver.useEffect\": ()=>observer.disconnect()\n            })[\"useIntersectionObserver.useEffect\"];\n        }\n    }[\"useIntersectionObserver.useEffect\"], [\n        ref,\n        rootMargin,\n        threshold\n    ]);\n    return [\n        setRef,\n        isIntersecting\n    ];\n}\n_s2(useIntersectionObserver, \"rO9taFyS6DEQG6eLdFtcL3uw0s8=\");\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PerformanceOptimizer);\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7;\n$RefreshReg$(_c, \"LazyFeaturedArticlesSection\");\n$RefreshReg$(_c1, \"LazyServicesSection\");\n$RefreshReg$(_c2, \"LazyAdBannerTop\");\n$RefreshReg$(_c3, \"ArticlesSkeleton\");\n$RefreshReg$(_c4, \"ServicesSkeleton\");\n$RefreshReg$(_c5, \"AdSkeleton\");\n$RefreshReg$(_c6, \"LazyComponent\");\n$RefreshReg$(_c7, \"PerformanceOptimizer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/PerformanceOptimizer.tsx\n"));

/***/ })

});