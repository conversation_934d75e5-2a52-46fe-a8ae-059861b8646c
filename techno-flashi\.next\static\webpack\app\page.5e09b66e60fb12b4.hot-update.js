"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/SimpleHeroSection.tsx":
/*!**********************************************!*\
  !*** ./src/components/SimpleHeroSection.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SimpleHeroSection: () => (/* binding */ SimpleHeroSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ SimpleHeroSection auto */ \nvar _s = $RefreshSig$();\n\n\nfunction SimpleHeroSection() {\n    _s();\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const animationRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    const mouseRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        x: 0,\n        y: 0\n    });\n    const particlesRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SimpleHeroSection.useEffect\": ()=>{\n            const canvas = canvasRef.current;\n            if (!canvas) return;\n            const ctx = canvas.getContext('2d');\n            if (!ctx) return;\n            // إعداد الكانفاس\n            const resizeCanvas = {\n                \"SimpleHeroSection.useEffect.resizeCanvas\": ()=>{\n                    canvas.width = window.innerWidth;\n                    canvas.height = window.innerHeight;\n                }\n            }[\"SimpleHeroSection.useEffect.resizeCanvas\"];\n            resizeCanvas();\n            window.addEventListener('resize', resizeCanvas);\n            // إنشاء الجسيمات المحسنة للثيم الفاتح\n            const createParticles = {\n                \"SimpleHeroSection.useEffect.createParticles\": ()=>{\n                    const particles = [];\n                    const colors = [\n                        '#8b5cf6',\n                        '#a855f7',\n                        '#c084fc',\n                        '#d8b4fe',\n                        '#e879f9',\n                        '#f0abfc',\n                        '#fbbf24',\n                        '#f59e0b'\n                    ];\n                    for(let i = 0; i < 100; i++){\n                        particles.push({\n                            x: Math.random() * canvas.width,\n                            y: Math.random() * canvas.height,\n                            vx: (Math.random() - 0.5) * 1.2,\n                            vy: (Math.random() - 0.5) * 1.2,\n                            size: Math.random() * 5 + 2,\n                            color: colors[Math.floor(Math.random() * colors.length)],\n                            opacity: Math.random() * 0.4 + 0.1,\n                            pulse: Math.random() * Math.PI * 2,\n                            pulseSpeed: 0.015 + Math.random() * 0.025,\n                            rotationSpeed: (Math.random() - 0.5) * 0.02\n                        });\n                    }\n                    particlesRef.current = particles;\n                }\n            }[\"SimpleHeroSection.useEffect.createParticles\"];\n            createParticles();\n            // معالج حركة الماوس\n            const handleMouseMove = {\n                \"SimpleHeroSection.useEffect.handleMouseMove\": (e)=>{\n                    mouseRef.current = {\n                        x: e.clientX / window.innerWidth * 2 - 1,\n                        y: -(e.clientY / window.innerHeight) * 2 + 1\n                    };\n                }\n            }[\"SimpleHeroSection.useEffect.handleMouseMove\"];\n            window.addEventListener('mousemove', handleMouseMove);\n            // حلقة الرسم المحسنة\n            const animate = {\n                \"SimpleHeroSection.useEffect.animate\": ()=>{\n                    ctx.clearRect(0, 0, canvas.width, canvas.height);\n                    // رسم الشبكة المتحركة\n                    const time = Date.now() * 0.001;\n                    const gridSize = 60;\n                    // شبكة متحركة مع تأثير موجي\n                    ctx.strokeStyle = \"rgba(139, 92, 246, \".concat(0.05 + Math.sin(time) * 0.02, \")\");\n                    ctx.lineWidth = 1;\n                    for(let x = 0; x < canvas.width; x += gridSize){\n                        const offset = Math.sin(time + x * 0.01) * 5;\n                        ctx.beginPath();\n                        ctx.moveTo(x + offset, 0);\n                        ctx.lineTo(x + offset, canvas.height);\n                        ctx.stroke();\n                    }\n                    for(let y = 0; y < canvas.height; y += gridSize){\n                        const offset = Math.cos(time + y * 0.01) * 3;\n                        ctx.beginPath();\n                        ctx.moveTo(0, y + offset);\n                        ctx.lineTo(canvas.width, y + offset);\n                        ctx.stroke();\n                    }\n                    // رسم دوائر متوهجة في الخلفية\n                    const glowCircles = 3;\n                    for(let i = 0; i < glowCircles; i++){\n                        const x = canvas.width * (0.2 + i * 0.3);\n                        const y = canvas.height * (0.3 + Math.sin(time + i) * 0.2);\n                        const radius = 100 + Math.sin(time * 0.5 + i) * 30;\n                        const gradient = ctx.createRadialGradient(x, y, 0, x, y, radius);\n                        gradient.addColorStop(0, \"rgba(168, 85, 247, \".concat(0.1 + Math.sin(time + i) * 0.05, \")\"));\n                        gradient.addColorStop(1, 'rgba(168, 85, 247, 0)');\n                        ctx.fillStyle = gradient;\n                        ctx.beginPath();\n                        ctx.arc(x, y, radius, 0, Math.PI * 2);\n                        ctx.fill();\n                    }\n                    // تحديث ورسم الجسيمات المحسنة\n                    particlesRef.current.forEach({\n                        \"SimpleHeroSection.useEffect.animate\": (particle, index)=>{\n                            // تحديث الموضع\n                            particle.x += particle.vx;\n                            particle.y += particle.vy;\n                            // تحديث التأثير النابض\n                            particle.pulse += particle.pulseSpeed;\n                            // تأثير الماوس المحسن\n                            const mouseInfluence = 80;\n                            const dx = mouseRef.current.x * canvas.width * 0.5 + canvas.width * 0.5 - particle.x;\n                            const dy = mouseRef.current.y * canvas.height * 0.5 + canvas.height * 0.5 - particle.y;\n                            const distance = Math.sqrt(dx * dx + dy * dy);\n                            if (distance < mouseInfluence) {\n                                const force = (mouseInfluence - distance) / mouseInfluence;\n                                particle.vx += dx * force * 0.002;\n                                particle.vy += dy * force * 0.002;\n                            }\n                            // حدود الشاشة مع ارتداد ناعم\n                            if (particle.x < 0 || particle.x > canvas.width) {\n                                particle.vx *= -0.8;\n                                particle.x = Math.max(0, Math.min(canvas.width, particle.x));\n                            }\n                            if (particle.y < 0 || particle.y > canvas.height) {\n                                particle.vy *= -0.8;\n                                particle.y = Math.max(0, Math.min(canvas.height, particle.y));\n                            }\n                            // رسم الجسيمة مع تأثير متوهج\n                            const pulseSize = particle.size + Math.sin(particle.pulse) * 1;\n                            const pulseOpacity = particle.opacity + Math.sin(particle.pulse) * 0.2;\n                            // رسم الهالة\n                            const gradient = ctx.createRadialGradient(particle.x, particle.y, 0, particle.x, particle.y, pulseSize * 3);\n                            gradient.addColorStop(0, particle.color.replace(')', ', 0.3)').replace('rgb', 'rgba'));\n                            gradient.addColorStop(1, particle.color.replace(')', ', 0)').replace('rgb', 'rgba'));\n                            ctx.fillStyle = gradient;\n                            ctx.globalAlpha = pulseOpacity * 0.5;\n                            ctx.beginPath();\n                            ctx.arc(particle.x, particle.y, pulseSize * 3, 0, Math.PI * 2);\n                            ctx.fill();\n                            // رسم الجسيمة الأساسية\n                            ctx.beginPath();\n                            ctx.arc(particle.x, particle.y, pulseSize, 0, Math.PI * 2);\n                            ctx.fillStyle = particle.color;\n                            ctx.globalAlpha = pulseOpacity;\n                            ctx.fill();\n                            // رسم خطوط الاتصال المحسنة\n                            particlesRef.current.forEach({\n                                \"SimpleHeroSection.useEffect.animate\": (otherParticle, otherIndex)=>{\n                                    if (index !== otherIndex && index < otherIndex) {\n                                        const dx = particle.x - otherParticle.x;\n                                        const dy = particle.y - otherParticle.y;\n                                        const distance = Math.sqrt(dx * dx + dy * dy);\n                                        if (distance < 120) {\n                                            const opacity = 0.15 * (1 - distance / 120);\n                                            const gradient = ctx.createLinearGradient(particle.x, particle.y, otherParticle.x, otherParticle.y);\n                                            gradient.addColorStop(0, \"rgba(168, 85, 247, \".concat(opacity, \")\"));\n                                            gradient.addColorStop(0.5, \"rgba(192, 132, 252, \".concat(opacity * 1.5, \")\"));\n                                            gradient.addColorStop(1, \"rgba(168, 85, 247, \".concat(opacity, \")\"));\n                                            ctx.beginPath();\n                                            ctx.moveTo(particle.x, particle.y);\n                                            ctx.lineTo(otherParticle.x, otherParticle.y);\n                                            ctx.strokeStyle = gradient;\n                                            ctx.lineWidth = 1 + opacity * 2;\n                                            ctx.stroke();\n                                        }\n                                    }\n                                }\n                            }[\"SimpleHeroSection.useEffect.animate\"]);\n                        }\n                    }[\"SimpleHeroSection.useEffect.animate\"]);\n                    ctx.globalAlpha = 1;\n                    animationRef.current = requestAnimationFrame(animate);\n                }\n            }[\"SimpleHeroSection.useEffect.animate\"];\n            animate();\n            // التنظيف\n            return ({\n                \"SimpleHeroSection.useEffect\": ()=>{\n                    if (animationRef.current) {\n                        cancelAnimationFrame(animationRef.current);\n                    }\n                    window.removeEventListener('resize', resizeCanvas);\n                    window.removeEventListener('mousemove', handleMouseMove);\n                }\n            })[\"SimpleHeroSection.useEffect\"];\n        }\n    }[\"SimpleHeroSection.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                ref: canvasRef,\n                className: \"absolute inset-0 w-full h-full\",\n                style: {\n                    zIndex: 1\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                lineNumber: 219,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-t from-slate-900/50 via-transparent to-slate-900/30\",\n                style: {\n                    zIndex: 2\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                lineNumber: 226,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 text-center px-4 max-w-6xl mx-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-5xl md:text-7xl font-bold text-white mb-6 leading-tight\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"bg-gradient-to-r from-purple-400 via-pink-400 to-purple-400 bg-clip-text text-transparent\",\n                                    children: \"TechnoFlash\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                    lineNumber: 232,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 231,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl md:text-2xl text-gray-300 mb-8 max-w-3xl mx-auto leading-relaxed\",\n                                children: \"منصتك الشاملة لأحدث التقنيات وأدوات الذكاء الاصطناعي\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                        lineNumber: 230,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row gap-4 justify-center items-center mb-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/articles\",\n                                className: \"group bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-4 rounded-xl font-semibold text-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"استكشف المقالات\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5 group-hover:translate-x-1 transition-transform\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M13 7l5 5m0 0l-5 5m5-5H6\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 248,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/ai-tools\",\n                                className: \"group border-2 border-purple-400 text-purple-400 px-8 py-4 rounded-xl font-semibold text-lg hover:bg-purple-400 hover:text-white transition-all duration-300 flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"أدوات الذكاء الاصطناعي\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5 group-hover:rotate-12 transition-transform\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M13 10V3L4 14h7v7l9-11h-7z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 253,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                        lineNumber: 242,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl md:text-4xl font-bold text-white mb-2\",\n                                        children: \"100+\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400\",\n                                        children: \"مقال تقني\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 266,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl md:text-4xl font-bold text-white mb-2\",\n                                        children: \"50+\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 271,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400\",\n                                        children: \"أداة ذكاء اصطناعي\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 272,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 270,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl md:text-4xl font-bold text-white mb-2\",\n                                        children: \"1000+\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 275,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400\",\n                                        children: \"قارئ نشط\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 276,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 274,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                        lineNumber: 265,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                lineNumber: 229,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-8 left-1/2 transform -translate-x-1/2 z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-bounce\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-8 h-8 border-2 border-white/40 rounded-full flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-4 h-4 text-white/60\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M19 14l-7 7m0 0l-7-7m7 7V3\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 286,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                            lineNumber: 285,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                        lineNumber: 284,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                    lineNumber: 283,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                lineNumber: 282,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n        lineNumber: 217,\n        columnNumber: 5\n    }, this);\n}\n_s(SimpleHeroSection, \"/8IKySTmGZxFymijsBTHUGtuaQs=\");\n_c = SimpleHeroSection;\nvar _c;\n$RefreshReg$(_c, \"SimpleHeroSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/SimpleHeroSection.tsx\n"));

/***/ })

});