"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/SimpleHeroSection.tsx":
/*!**********************************************!*\
  !*** ./src/components/SimpleHeroSection.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SimpleHeroSection: () => (/* binding */ SimpleHeroSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ SimpleHeroSection auto */ \nvar _s = $RefreshSig$();\n\n\nfunction SimpleHeroSection() {\n    _s();\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const animationRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    const mouseRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        x: 0,\n        y: 0\n    });\n    const particlesRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SimpleHeroSection.useEffect\": ()=>{\n            const canvas = canvasRef.current;\n            if (!canvas) return;\n            const ctx = canvas.getContext('2d');\n            if (!ctx) return;\n            // إعداد الكانفاس\n            const resizeCanvas = {\n                \"SimpleHeroSection.useEffect.resizeCanvas\": ()=>{\n                    canvas.width = window.innerWidth;\n                    canvas.height = window.innerHeight;\n                }\n            }[\"SimpleHeroSection.useEffect.resizeCanvas\"];\n            resizeCanvas();\n            window.addEventListener('resize', resizeCanvas);\n            // إنشاء الجسيمات الذكية المتطورة مع تحسينات جديدة\n            const createParticles = {\n                \"SimpleHeroSection.useEffect.createParticles\": ()=>{\n                    const particles = [];\n                    const colors = [\n                        '#8b5cf6',\n                        '#a855f7',\n                        '#c084fc',\n                        '#d8b4fe',\n                        '#e879f9',\n                        '#f0abfc',\n                        '#fbbf24',\n                        '#f59e0b',\n                        '#06b6d4',\n                        '#0891b2',\n                        '#10b981',\n                        '#059669',\n                        '#f97316',\n                        '#ea580c',\n                        '#dc2626',\n                        '#b91c1c',\n                        '#7c3aed',\n                        '#6d28d9'\n                    ];\n                    for(let i = 0; i < 200; i++){\n                        particles.push({\n                            x: Math.random() * canvas.width,\n                            y: Math.random() * canvas.height,\n                            vx: (Math.random() - 0.5) * 2,\n                            vy: (Math.random() - 0.5) * 2,\n                            size: Math.random() * 8 + 2,\n                            color: colors[Math.floor(Math.random() * colors.length)],\n                            opacity: Math.random() * 0.8 + 0.2,\n                            pulse: Math.random() * Math.PI * 2,\n                            pulseSpeed: 0.008 + Math.random() * 0.04,\n                            rotationSpeed: (Math.random() - 0.5) * 0.05,\n                            magnetism: Math.random() * 0.8 + 0.6,\n                            trail: [],\n                            energy: Math.random() * 100 + 50,\n                            type: Math.floor(Math.random() * 3),\n                            phase: Math.random() * Math.PI * 2,\n                            amplitude: Math.random() * 20 + 10 // سعة الحركة الموجية\n                        });\n                    }\n                    particlesRef.current = particles;\n                }\n            }[\"SimpleHeroSection.useEffect.createParticles\"];\n            createParticles();\n            // معالج حركة الماوس\n            const handleMouseMove = {\n                \"SimpleHeroSection.useEffect.handleMouseMove\": (e)=>{\n                    mouseRef.current = {\n                        x: e.clientX / window.innerWidth * 2 - 1,\n                        y: -(e.clientY / window.innerHeight) * 2 + 1\n                    };\n                }\n            }[\"SimpleHeroSection.useEffect.handleMouseMove\"];\n            window.addEventListener('mousemove', handleMouseMove);\n            // دالة رسم السداسي\n            const drawHexagon = {\n                \"SimpleHeroSection.useEffect.drawHexagon\": (ctx, x, y, size)=>{\n                    ctx.beginPath();\n                    for(let i = 0; i < 6; i++){\n                        const angle = i * Math.PI / 3;\n                        const px = x + size * Math.cos(angle);\n                        const py = y + size * Math.sin(angle);\n                        if (i === 0) {\n                            ctx.moveTo(px, py);\n                        } else {\n                            ctx.lineTo(px, py);\n                        }\n                    }\n                    ctx.closePath();\n                    ctx.stroke();\n                }\n            }[\"SimpleHeroSection.useEffect.drawHexagon\"];\n            // حلقة الرسم المتطورة مع تأثيرات مبهرة\n            const animate = {\n                \"SimpleHeroSection.useEffect.animate\": ()=>{\n                    // مسح تدريجي بدلاً من المسح الكامل\n                    ctx.fillStyle = 'rgba(255, 255, 255, 0.05)';\n                    ctx.fillRect(0, 0, canvas.width, canvas.height);\n                    const time = Date.now() * 0.001;\n                    const mouseX = mouseRef.current.x * canvas.width * 0.5 + canvas.width * 0.5;\n                    const mouseY = mouseRef.current.y * canvas.height * 0.5 + canvas.height * 0.5;\n                    // رسم هالة تتبع الماوس المتوهجة المحسنة\n                    if (mouseRef.current.x !== 0 || mouseRef.current.y !== 0) {\n                        // هالة خارجية كبيرة\n                        const outerGlowSize = 150 + Math.sin(time * 2) * 30;\n                        const outerGradient = ctx.createRadialGradient(mouseX, mouseY, 0, mouseX, mouseY, outerGlowSize);\n                        outerGradient.addColorStop(0, \"rgba(168, 85, 247, \".concat(0.2 + Math.sin(time * 1.5) * 0.08, \")\"));\n                        outerGradient.addColorStop(0.2, \"rgba(236, 72, 153, \".concat(0.15 + Math.sin(time * 2) * 0.06, \")\"));\n                        outerGradient.addColorStop(0.4, \"rgba(251, 191, 36, \".concat(0.1 + Math.sin(time * 2.5) * 0.04, \")\"));\n                        outerGradient.addColorStop(0.6, \"rgba(6, 182, 212, \".concat(0.08 + Math.sin(time * 3) * 0.03, \")\"));\n                        outerGradient.addColorStop(1, 'rgba(255, 255, 255, 0)');\n                        ctx.fillStyle = outerGradient;\n                        ctx.beginPath();\n                        ctx.arc(mouseX, mouseY, outerGlowSize, 0, Math.PI * 2);\n                        ctx.fill();\n                        // هالة متوسطة\n                        const midGlowSize = 80 + Math.sin(time * 3) * 15;\n                        const midGradient = ctx.createRadialGradient(mouseX, mouseY, 0, mouseX, mouseY, midGlowSize);\n                        midGradient.addColorStop(0, \"rgba(139, 92, 246, \".concat(0.3 + Math.sin(time * 2.5) * 0.1, \")\"));\n                        midGradient.addColorStop(0.5, \"rgba(168, 85, 247, \".concat(0.2 + Math.sin(time * 3) * 0.08, \")\"));\n                        midGradient.addColorStop(1, 'rgba(168, 85, 247, 0)');\n                        ctx.fillStyle = midGradient;\n                        ctx.beginPath();\n                        ctx.arc(mouseX, mouseY, midGlowSize, 0, Math.PI * 2);\n                        ctx.fill();\n                        // دائرة مركزية نابضة\n                        const centerSize = 20 + Math.sin(time * 4) * 8;\n                        const centerGradient = ctx.createRadialGradient(mouseX, mouseY, 0, mouseX, mouseY, centerSize);\n                        centerGradient.addColorStop(0, \"rgba(255, 255, 255, \".concat(0.9 + Math.sin(time * 5) * 0.1, \")\"));\n                        centerGradient.addColorStop(0.7, \"rgba(168, 85, 247, \".concat(0.6 + Math.sin(time * 4) * 0.2, \")\"));\n                        centerGradient.addColorStop(1, 'rgba(168, 85, 247, 0)');\n                        ctx.fillStyle = centerGradient;\n                        ctx.beginPath();\n                        ctx.arc(mouseX, mouseY, centerSize, 0, Math.PI * 2);\n                        ctx.fill();\n                        // رسم موجات تنتشر من موضع الماوس\n                        for(let i = 0; i < 3; i++){\n                            const waveRadius = 50 + (time * 100 + i * 50) % 200;\n                            const waveAlpha = Math.max(0, 0.3 - waveRadius / 200 * 0.3);\n                            if (waveAlpha > 0.01) {\n                                ctx.strokeStyle = \"rgba(168, 85, 247, \".concat(waveAlpha, \")\");\n                                ctx.lineWidth = 2 - waveRadius / 200;\n                                ctx.beginPath();\n                                ctx.arc(mouseX, mouseY, waveRadius, 0, Math.PI * 2);\n                                ctx.stroke();\n                            }\n                        }\n                    }\n                    // رسم موجات ديناميكية في الخلفية\n                    const waveCount = 5;\n                    for(let i = 0; i < waveCount; i++){\n                        ctx.beginPath();\n                        ctx.strokeStyle = \"rgba(168, 85, 247, \".concat(Math.max(0.01, 0.02 + Math.sin(time + i) * 0.01), \")\");\n                        ctx.lineWidth = 2;\n                        for(let x = 0; x <= canvas.width; x += 10){\n                            const y = canvas.height * 0.5 + Math.sin(x * 0.01 + time + i) * 50 * (i + 1) + Math.sin(x * 0.005 + time * 0.5 + i) * 30;\n                            if (x === 0) {\n                                ctx.moveTo(x, y);\n                            } else {\n                                ctx.lineTo(x, y);\n                            }\n                        }\n                        ctx.stroke();\n                    }\n                    // رسم شبكة سداسية متحركة\n                    const hexSize = 60;\n                    const hexRows = Math.ceil(canvas.height / (hexSize * 0.75)) + 2;\n                    const hexCols = Math.ceil(canvas.width / (hexSize * Math.sqrt(3))) + 2;\n                    ctx.strokeStyle = \"rgba(139, 92, 246, \".concat(Math.max(0.01, 0.04 + Math.sin(time * 0.5) * 0.02), \")\");\n                    ctx.lineWidth = 1;\n                    for(let row = 0; row < hexRows; row++){\n                        for(let col = 0; col < hexCols; col++){\n                            const x = col * hexSize * Math.sqrt(3) + row % 2 * hexSize * Math.sqrt(3) * 0.5;\n                            const y = row * hexSize * 0.75;\n                            const distanceToMouse = Math.sqrt((x - mouseX) ** 2 + (y - mouseY) ** 2);\n                            const influence = Math.max(0, 1 - distanceToMouse / 300); // زيادة نطاق التأثير\n                            if (influence > 0.05) {\n                                // تحسين الحركة والحجم حسب قرب الماوس\n                                const moveIntensity = influence * 15;\n                                const sizeMultiplier = 0.2 + influence * 0.8;\n                                const alpha = 0.02 + influence * 0.1;\n                                ctx.strokeStyle = \"rgba(139, 92, 246, \".concat(alpha, \")\");\n                                ctx.lineWidth = 1 + influence * 2;\n                                drawHexagon(ctx, x + Math.sin(time + row + col) * moveIntensity, y + Math.cos(time + row + col) * moveIntensity, hexSize * sizeMultiplier);\n                            }\n                        }\n                    }\n                    // رسم دوائر متوهجة ناعمة\n                    const glowCircles = 5;\n                    for(let i = 0; i < glowCircles; i++){\n                        const x = canvas.width * (0.1 + i * 0.2);\n                        const y = canvas.height * (0.2 + Math.sin(time * 0.3 + i) * 0.3);\n                        const radius = Math.max(10, 150 + Math.sin(time * 0.4 + i) * 50);\n                        const gradient = ctx.createRadialGradient(x, y, 0, x, y, radius);\n                        const colors = [\n                            \"rgba(168, 85, 247, \".concat(Math.max(0.01, 0.05 + Math.sin(time + i) * 0.02), \")\"),\n                            \"rgba(236, 72, 153, \".concat(Math.max(0.01, 0.03 + Math.sin(time + i + 1) * 0.015), \")\"),\n                            \"rgba(251, 191, 36, \".concat(Math.max(0.01, 0.02 + Math.sin(time + i + 2) * 0.01), \")\")\n                        ];\n                        gradient.addColorStop(0, colors[i % colors.length]);\n                        gradient.addColorStop(0.7, colors[(i + 1) % colors.length].replace(/[\\d.]+\\)/, '0.01)'));\n                        gradient.addColorStop(1, 'rgba(255, 255, 255, 0)');\n                        ctx.fillStyle = gradient;\n                        ctx.beginPath();\n                        ctx.arc(x, y, radius, 0, Math.PI * 2);\n                        ctx.fill();\n                    }\n                    // رسم أشكال هندسية متحركة\n                    const shapes = 3;\n                    for(let i = 0; i < shapes; i++){\n                        const x = canvas.width * (0.3 + i * 0.2);\n                        const y = canvas.height * (0.4 + Math.cos(time * 0.2 + i) * 0.2);\n                        const size = Math.max(10, 30 + Math.sin(time + i) * 10);\n                        const rotation = time * 0.5 + i;\n                        ctx.save();\n                        ctx.translate(x, y);\n                        ctx.rotate(rotation);\n                        const gradient = ctx.createLinearGradient(-size, -size, size, size);\n                        gradient.addColorStop(0, \"rgba(168, 85, 247, \".concat(Math.max(0.01, 0.1 + Math.sin(time + i) * 0.05), \")\"));\n                        gradient.addColorStop(1, \"rgba(236, 72, 153, \".concat(Math.max(0.01, 0.05 + Math.cos(time + i) * 0.03), \")\"));\n                        ctx.fillStyle = gradient;\n                        ctx.beginPath();\n                        ctx.roundRect(-size / 2, -size / 2, size, size, 8);\n                        ctx.fill();\n                        ctx.restore();\n                    }\n                    // تحديث ورسم الجسيمات الذكية المتطورة\n                    particlesRef.current.forEach({\n                        \"SimpleHeroSection.useEffect.animate\": (particle, index)=>{\n                            // التأكد من صحة القيم\n                            particle.size = Math.max(1, particle.size || 2);\n                            particle.energy = Math.max(50, Math.min(100, particle.energy || 50));\n                            particle.opacity = Math.max(0.1, Math.min(1, particle.opacity || 0.5));\n                            // حفظ الموضع السابق للمسار\n                            particle.trail.push({\n                                x: particle.x,\n                                y: particle.y\n                            });\n                            if (particle.trail.length > 5) {\n                                particle.trail.shift();\n                            }\n                            // تحديث الموضع مع فيزياء متقدمة\n                            particle.x += particle.vx;\n                            particle.y += particle.vy;\n                            particle.pulse += particle.pulseSpeed;\n                            // تأثير الماوس المغناطيسي المحسن والأكثر تفاعلاً\n                            const mouseInfluence = 200; // زيادة نطاق التأثير\n                            const dx = mouseX - particle.x;\n                            const dy = mouseY - particle.y;\n                            const distance = Math.sqrt(dx * dx + dy * dy);\n                            if (distance < mouseInfluence) {\n                                const force = (mouseInfluence - distance) / mouseInfluence * particle.magnetism;\n                                const angle = Math.atan2(dy, dx);\n                                // زيادة قوة التفاعل\n                                particle.vx += Math.cos(angle) * force * 0.008;\n                                particle.vy += Math.sin(angle) * force * 0.008;\n                                // تأثير الطاقة المحسن\n                                particle.energy = Math.min(100, particle.energy + force * 4);\n                                // إضافة تأثير اهتزاز ناعم\n                                particle.vx += (Math.random() - 0.5) * force * 0.002;\n                                particle.vy += (Math.random() - 0.5) * force * 0.002;\n                            } else {\n                                // تقليل الطاقة تدريجياً\n                                particle.energy = Math.max(50, particle.energy - 0.3);\n                            }\n                            // تطبيق الاحتكاك\n                            particle.vx *= 0.99;\n                            particle.vy *= 0.99;\n                            // حدود الشاشة مع ارتداد ديناميكي\n                            if (particle.x < 0 || particle.x > canvas.width) {\n                                particle.vx *= -0.7;\n                                particle.x = Math.max(0, Math.min(canvas.width, particle.x));\n                            }\n                            if (particle.y < 0 || particle.y > canvas.height) {\n                                particle.vy *= -0.7;\n                                particle.y = Math.max(0, Math.min(canvas.height, particle.y));\n                            }\n                            // رسم مسار الجسيمة\n                            if (particle.trail.length > 1) {\n                                ctx.beginPath();\n                                ctx.strokeStyle = particle.color.replace(')', ', 0.2)').replace('rgb', 'rgba');\n                                ctx.lineWidth = 1;\n                                for(let i = 0; i < particle.trail.length - 1; i++){\n                                    const alpha = i / particle.trail.length;\n                                    ctx.globalAlpha = alpha * 0.3;\n                                    if (i === 0) {\n                                        ctx.moveTo(particle.trail[i].x, particle.trail[i].y);\n                                    } else {\n                                        ctx.lineTo(particle.trail[i].x, particle.trail[i].y);\n                                    }\n                                }\n                                ctx.stroke();\n                            }\n                            // رسم الجسيمة مع تأثيرات متقدمة\n                            const energyFactor = particle.energy / 100;\n                            const pulseSize = Math.max(1, particle.size + Math.sin(particle.pulse) * 2 * energyFactor);\n                            const pulseOpacity = Math.max(0.1, particle.opacity + Math.sin(particle.pulse) * 0.3 * energyFactor);\n                            // رسم الهالة الخارجية\n                            const outerRadius = Math.max(1, pulseSize * 4);\n                            const outerGradient = ctx.createRadialGradient(particle.x, particle.y, 0, particle.x, particle.y, outerRadius);\n                            outerGradient.addColorStop(0, particle.color.replace(')', ', 0.4)').replace('rgb', 'rgba'));\n                            outerGradient.addColorStop(0.5, particle.color.replace(')', ', 0.2)').replace('rgb', 'rgba'));\n                            outerGradient.addColorStop(1, particle.color.replace(')', ', 0)').replace('rgb', 'rgba'));\n                            ctx.fillStyle = outerGradient;\n                            ctx.globalAlpha = pulseOpacity * 0.6 * energyFactor;\n                            ctx.beginPath();\n                            ctx.arc(particle.x, particle.y, outerRadius, 0, Math.PI * 2);\n                            ctx.fill();\n                            // رسم الهالة الداخلية\n                            const innerRadius = Math.max(1, pulseSize * 2);\n                            const innerGradient = ctx.createRadialGradient(particle.x, particle.y, 0, particle.x, particle.y, innerRadius);\n                            innerGradient.addColorStop(0, particle.color.replace(')', ', 0.8)').replace('rgb', 'rgba'));\n                            innerGradient.addColorStop(1, particle.color.replace(')', ', 0.2)').replace('rgb', 'rgba'));\n                            ctx.fillStyle = innerGradient;\n                            ctx.globalAlpha = pulseOpacity * 0.8;\n                            ctx.beginPath();\n                            ctx.arc(particle.x, particle.y, innerRadius, 0, Math.PI * 2);\n                            ctx.fill();\n                            // رسم الجسيمة الأساسية\n                            ctx.beginPath();\n                            ctx.arc(particle.x, particle.y, Math.max(0.5, pulseSize), 0, Math.PI * 2);\n                            ctx.fillStyle = particle.color;\n                            ctx.globalAlpha = Math.max(0.1, Math.min(1, pulseOpacity));\n                            ctx.fill();\n                            // رسم خطوط الاتصال الذكية المتطورة\n                            particlesRef.current.forEach({\n                                \"SimpleHeroSection.useEffect.animate\": (otherParticle, otherIndex)=>{\n                                    if (index !== otherIndex && index < otherIndex) {\n                                        const dx = particle.x - otherParticle.x;\n                                        const dy = particle.y - otherParticle.y;\n                                        const distance = Math.sqrt(dx * dx + dy * dy);\n                                        if (distance < 150) {\n                                            const opacity = Math.max(0, 0.2 * (1 - distance / 150));\n                                            const energyBonus = (particle.energy + otherParticle.energy) / 200;\n                                            const finalOpacity = Math.max(0.01, Math.min(1, opacity * (1 + energyBonus)));\n                                            // خط متدرج ديناميكي\n                                            const gradient = ctx.createLinearGradient(particle.x, particle.y, otherParticle.x, otherParticle.y);\n                                            const color1 = particle.color.replace(')', ', ' + finalOpacity + ')').replace('rgb', 'rgba');\n                                            const color2 = otherParticle.color.replace(')', ', ' + finalOpacity + ')').replace('rgb', 'rgba');\n                                            const midColor = \"rgba(192, 132, 252, \".concat(finalOpacity * 1.2, \")\");\n                                            gradient.addColorStop(0, color1);\n                                            gradient.addColorStop(0.5, midColor);\n                                            gradient.addColorStop(1, color2);\n                                            // رسم خط متموج\n                                            ctx.beginPath();\n                                            ctx.strokeStyle = gradient;\n                                            ctx.lineWidth = 1 + finalOpacity * 3;\n                                            const steps = 10;\n                                            for(let i = 0; i <= steps; i++){\n                                                const t = i / steps;\n                                                const x = particle.x + (otherParticle.x - particle.x) * t;\n                                                const y = particle.y + (otherParticle.y - particle.y) * t;\n                                                // إضافة تموج ناعم\n                                                const waveOffset = Math.sin(t * Math.PI * 2 + time) * 5 * finalOpacity;\n                                                const perpX = -(otherParticle.y - particle.y) / distance;\n                                                const perpY = (otherParticle.x - particle.x) / distance;\n                                                const finalX = x + perpX * waveOffset;\n                                                const finalY = y + perpY * waveOffset;\n                                                if (i === 0) {\n                                                    ctx.moveTo(finalX, finalY);\n                                                } else {\n                                                    ctx.lineTo(finalX, finalY);\n                                                }\n                                            }\n                                            ctx.stroke();\n                                            // إضافة نقاط ضوئية على الخط\n                                            if (finalOpacity > 0.1) {\n                                                const midX = (particle.x + otherParticle.x) / 2;\n                                                const midY = (particle.y + otherParticle.y) / 2;\n                                                ctx.beginPath();\n                                                ctx.arc(midX, midY, Math.max(0.5, 2 * finalOpacity), 0, Math.PI * 2);\n                                                ctx.fillStyle = \"rgba(255, 255, 255, \".concat(Math.max(0.1, finalOpacity * 0.8), \")\");\n                                                ctx.fill();\n                                            }\n                                        }\n                                    }\n                                }\n                            }[\"SimpleHeroSection.useEffect.animate\"]);\n                        }\n                    }[\"SimpleHeroSection.useEffect.animate\"]);\n                    ctx.globalAlpha = 1;\n                    animationRef.current = requestAnimationFrame(animate);\n                }\n            }[\"SimpleHeroSection.useEffect.animate\"];\n            animate();\n            // التنظيف\n            return ({\n                \"SimpleHeroSection.useEffect\": ()=>{\n                    if (animationRef.current) {\n                        cancelAnimationFrame(animationRef.current);\n                    }\n                    window.removeEventListener('resize', resizeCanvas);\n                    window.removeEventListener('mousemove', handleMouseMove);\n                }\n            })[\"SimpleHeroSection.useEffect\"];\n        }\n    }[\"SimpleHeroSection.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-white via-purple-50 to-pink-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                ref: canvasRef,\n                className: \"absolute inset-0 w-full h-full\",\n                style: {\n                    zIndex: 1\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                lineNumber: 486,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-t from-white/30 via-transparent to-purple-50/20\",\n                style: {\n                    zIndex: 2\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                lineNumber: 493,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 text-center px-4 max-w-6xl mx-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-5xl md:text-7xl font-bold text-gray-900 mb-6 leading-tight\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"bg-gradient-to-r from-purple-600 via-pink-600 to-purple-600 bg-clip-text text-transparent\",\n                                    children: \"TechnoFlash\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                    lineNumber: 499,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 498,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl md:text-2xl text-gray-700 mb-8 max-w-3xl mx-auto leading-relaxed\",\n                                children: \"منصتك الشاملة لأحدث التقنيات وأدوات الذكاء الاصطناعي\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 503,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                        lineNumber: 497,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row gap-4 justify-center items-center mb-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/articles\",\n                                className: \"group bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-4 rounded-xl font-semibold text-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"استكشف المقالات\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 514,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5 group-hover:translate-x-1 transition-transform\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M13 7l5 5m0 0l-5 5m5-5H6\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                            lineNumber: 516,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 515,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 510,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/ai-tools\",\n                                className: \"group border-2 border-purple-400 text-purple-400 px-8 py-4 rounded-xl font-semibold text-lg hover:bg-purple-400 hover:text-white transition-all duration-300 flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"أدوات الذكاء الاصطناعي\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 524,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5 group-hover:rotate-12 transition-transform\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M13 10V3L4 14h7v7l9-11h-7z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                            lineNumber: 526,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 525,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 520,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                        lineNumber: 509,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-2\",\n                                        children: \"100+\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 534,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-600\",\n                                        children: \"مقال تقني\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 535,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 533,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-2\",\n                                        children: \"50+\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 538,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-600\",\n                                        children: \"أداة ذكاء اصطناعي\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 539,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 537,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-2\",\n                                        children: \"1000+\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 542,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-600\",\n                                        children: \"قارئ نشط\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 543,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 541,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                        lineNumber: 532,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                lineNumber: 496,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-8 left-1/2 transform -translate-x-1/2 z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-bounce\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-8 h-8 border-2 border-gray-400/60 rounded-full flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-4 h-4 text-gray-600/80\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M19 14l-7 7m0 0l-7-7m7 7V3\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 553,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                            lineNumber: 552,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                        lineNumber: 551,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                    lineNumber: 550,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                lineNumber: 549,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n        lineNumber: 484,\n        columnNumber: 5\n    }, this);\n}\n_s(SimpleHeroSection, \"/8IKySTmGZxFymijsBTHUGtuaQs=\");\n_c = SimpleHeroSection;\nvar _c;\n$RefreshReg$(_c, \"SimpleHeroSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/SimpleHeroSection.tsx\n"));

/***/ })

});