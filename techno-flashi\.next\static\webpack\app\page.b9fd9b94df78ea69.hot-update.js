"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/SimpleHeroSection.tsx":
/*!**********************************************!*\
  !*** ./src/components/SimpleHeroSection.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SimpleHeroSection: () => (/* binding */ SimpleHeroSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ SimpleHeroSection auto */ \nvar _s = $RefreshSig$();\n\n\nfunction SimpleHeroSection() {\n    _s();\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const animationRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    const mouseRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        x: 0,\n        y: 0\n    });\n    const particlesRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SimpleHeroSection.useEffect\": ()=>{\n            const canvas = canvasRef.current;\n            if (!canvas) return;\n            const ctx = canvas.getContext('2d');\n            if (!ctx) return;\n            // إعداد الكانفاس\n            const resizeCanvas = {\n                \"SimpleHeroSection.useEffect.resizeCanvas\": ()=>{\n                    canvas.width = window.innerWidth;\n                    canvas.height = window.innerHeight;\n                }\n            }[\"SimpleHeroSection.useEffect.resizeCanvas\"];\n            resizeCanvas();\n            window.addEventListener('resize', resizeCanvas);\n            // إنشاء الجسيمات المحسنة\n            const createParticles = {\n                \"SimpleHeroSection.useEffect.createParticles\": ()=>{\n                    const particles = [];\n                    const colors = [\n                        '#8b5cf6',\n                        '#a855f7',\n                        '#c084fc',\n                        '#d8b4fe',\n                        '#e879f9',\n                        '#f0abfc'\n                    ];\n                    for(let i = 0; i < 80; i++){\n                        particles.push({\n                            x: Math.random() * canvas.width,\n                            y: Math.random() * canvas.height,\n                            vx: (Math.random() - 0.5) * 0.8,\n                            vy: (Math.random() - 0.5) * 0.8,\n                            size: Math.random() * 4 + 1,\n                            color: colors[Math.floor(Math.random() * colors.length)],\n                            opacity: Math.random() * 0.6 + 0.2,\n                            pulse: Math.random() * Math.PI * 2,\n                            pulseSpeed: 0.02 + Math.random() * 0.03\n                        });\n                    }\n                    particlesRef.current = particles;\n                }\n            }[\"SimpleHeroSection.useEffect.createParticles\"];\n            createParticles();\n            // معالج حركة الماوس\n            const handleMouseMove = {\n                \"SimpleHeroSection.useEffect.handleMouseMove\": (e)=>{\n                    mouseRef.current = {\n                        x: e.clientX / window.innerWidth * 2 - 1,\n                        y: -(e.clientY / window.innerHeight) * 2 + 1\n                    };\n                }\n            }[\"SimpleHeroSection.useEffect.handleMouseMove\"];\n            window.addEventListener('mousemove', handleMouseMove);\n            // حلقة الرسم\n            const animate = {\n                \"SimpleHeroSection.useEffect.animate\": ()=>{\n                    ctx.clearRect(0, 0, canvas.width, canvas.height);\n                    // رسم الشبكة\n                    ctx.strokeStyle = 'rgba(139, 92, 246, 0.1)';\n                    ctx.lineWidth = 1;\n                    const gridSize = 50;\n                    for(let x = 0; x < canvas.width; x += gridSize){\n                        ctx.beginPath();\n                        ctx.moveTo(x, 0);\n                        ctx.lineTo(x, canvas.height);\n                        ctx.stroke();\n                    }\n                    for(let y = 0; y < canvas.height; y += gridSize){\n                        ctx.beginPath();\n                        ctx.moveTo(0, y);\n                        ctx.lineTo(canvas.width, y);\n                        ctx.stroke();\n                    }\n                    // تحديث ورسم الجسيمات\n                    particlesRef.current.forEach({\n                        \"SimpleHeroSection.useEffect.animate\": (particle, index)=>{\n                            // تحديث الموضع\n                            particle.x += particle.vx;\n                            particle.y += particle.vy;\n                            // تأثير الماوس\n                            const mouseInfluence = 50;\n                            const dx = mouseRef.current.x * canvas.width * 0.5 + canvas.width * 0.5 - particle.x;\n                            const dy = mouseRef.current.y * canvas.height * 0.5 + canvas.height * 0.5 - particle.y;\n                            const distance = Math.sqrt(dx * dx + dy * dy);\n                            if (distance < mouseInfluence) {\n                                const force = (mouseInfluence - distance) / mouseInfluence;\n                                particle.vx += dx * force * 0.001;\n                                particle.vy += dy * force * 0.001;\n                            }\n                            // حدود الشاشة\n                            if (particle.x < 0 || particle.x > canvas.width) particle.vx *= -1;\n                            if (particle.y < 0 || particle.y > canvas.height) particle.vy *= -1;\n                            // رسم الجسيمة\n                            ctx.beginPath();\n                            ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);\n                            ctx.fillStyle = particle.color;\n                            ctx.globalAlpha = particle.opacity;\n                            ctx.fill();\n                            // رسم خطوط الاتصال\n                            particlesRef.current.forEach({\n                                \"SimpleHeroSection.useEffect.animate\": (otherParticle, otherIndex)=>{\n                                    if (index !== otherIndex) {\n                                        const dx = particle.x - otherParticle.x;\n                                        const dy = particle.y - otherParticle.y;\n                                        const distance = Math.sqrt(dx * dx + dy * dy);\n                                        if (distance < 100) {\n                                            ctx.beginPath();\n                                            ctx.moveTo(particle.x, particle.y);\n                                            ctx.lineTo(otherParticle.x, otherParticle.y);\n                                            ctx.strokeStyle = \"rgba(139, 92, 246, \".concat(0.1 * (1 - distance / 100), \")\");\n                                            ctx.lineWidth = 1;\n                                            ctx.stroke();\n                                        }\n                                    }\n                                }\n                            }[\"SimpleHeroSection.useEffect.animate\"]);\n                        }\n                    }[\"SimpleHeroSection.useEffect.animate\"]);\n                    ctx.globalAlpha = 1;\n                    animationRef.current = requestAnimationFrame(animate);\n                }\n            }[\"SimpleHeroSection.useEffect.animate\"];\n            animate();\n            // التنظيف\n            return ({\n                \"SimpleHeroSection.useEffect\": ()=>{\n                    if (animationRef.current) {\n                        cancelAnimationFrame(animationRef.current);\n                    }\n                    window.removeEventListener('resize', resizeCanvas);\n                    window.removeEventListener('mousemove', handleMouseMove);\n                }\n            })[\"SimpleHeroSection.useEffect\"];\n        }\n    }[\"SimpleHeroSection.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                ref: canvasRef,\n                className: \"absolute inset-0 w-full h-full\",\n                style: {\n                    zIndex: 1\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                lineNumber: 160,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-t from-slate-900/50 via-transparent to-slate-900/30\",\n                style: {\n                    zIndex: 2\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                lineNumber: 167,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 text-center px-4 max-w-6xl mx-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-5xl md:text-7xl font-bold text-white mb-6 leading-tight\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"bg-gradient-to-r from-purple-400 via-pink-400 to-purple-400 bg-clip-text text-transparent\",\n                                    children: \"TechnoFlash\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl md:text-2xl text-gray-300 mb-8 max-w-3xl mx-auto leading-relaxed\",\n                                children: \"منصتك الشاملة لأحدث التقنيات وأدوات الذكاء الاصطناعي\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row gap-4 justify-center items-center mb-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/articles\",\n                                className: \"group bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-4 rounded-xl font-semibold text-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"استكشف المقالات\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5 group-hover:translate-x-1 transition-transform\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M13 7l5 5m0 0l-5 5m5-5H6\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/ai-tools\",\n                                className: \"group border-2 border-purple-400 text-purple-400 px-8 py-4 rounded-xl font-semibold text-lg hover:bg-purple-400 hover:text-white transition-all duration-300 flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"أدوات الذكاء الاصطناعي\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 198,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5 group-hover:rotate-12 transition-transform\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M13 10V3L4 14h7v7l9-11h-7z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                            lineNumber: 200,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 199,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                        lineNumber: 183,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl md:text-4xl font-bold text-white mb-2\",\n                                        children: \"100+\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400\",\n                                        children: \"مقال تقني\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl md:text-4xl font-bold text-white mb-2\",\n                                        children: \"50+\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 212,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400\",\n                                        children: \"أداة ذكاء اصطناعي\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl md:text-4xl font-bold text-white mb-2\",\n                                        children: \"1000+\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400\",\n                                        children: \"قارئ نشط\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                lineNumber: 170,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-8 left-1/2 transform -translate-x-1/2 z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-bounce\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-8 h-8 border-2 border-white/40 rounded-full flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-4 h-4 text-white/60\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M19 14l-7 7m0 0l-7-7m7 7V3\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                        lineNumber: 225,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                    lineNumber: 224,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                lineNumber: 223,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n        lineNumber: 158,\n        columnNumber: 5\n    }, this);\n}\n_s(SimpleHeroSection, \"/8IKySTmGZxFymijsBTHUGtuaQs=\");\n_c = SimpleHeroSection;\nvar _c;\n$RefreshReg$(_c, \"SimpleHeroSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/SimpleHeroSection.tsx\n"));

/***/ })

});