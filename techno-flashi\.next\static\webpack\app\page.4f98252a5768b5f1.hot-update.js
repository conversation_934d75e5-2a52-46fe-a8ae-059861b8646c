"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/SimpleHeroSection.tsx":
/*!**********************************************!*\
  !*** ./src/components/SimpleHeroSection.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SimpleHeroSection: () => (/* binding */ SimpleHeroSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ SimpleHeroSection auto */ \nvar _s = $RefreshSig$();\n\n\nfunction SimpleHeroSection() {\n    _s();\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const animationRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    const mouseRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        x: 0,\n        y: 0\n    });\n    const particlesRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SimpleHeroSection.useEffect\": ()=>{\n            const canvas = canvasRef.current;\n            if (!canvas) return;\n            const ctx = canvas.getContext('2d');\n            if (!ctx) return;\n            // إعداد الكانفاس\n            const resizeCanvas = {\n                \"SimpleHeroSection.useEffect.resizeCanvas\": ()=>{\n                    canvas.width = window.innerWidth;\n                    canvas.height = window.innerHeight;\n                }\n            }[\"SimpleHeroSection.useEffect.resizeCanvas\"];\n            resizeCanvas();\n            window.addEventListener('resize', resizeCanvas);\n            // إنشاء الجسيمات الذكية المتطورة\n            const createParticles = {\n                \"SimpleHeroSection.useEffect.createParticles\": ()=>{\n                    const particles = [];\n                    const colors = [\n                        '#8b5cf6',\n                        '#a855f7',\n                        '#c084fc',\n                        '#d8b4fe',\n                        '#e879f9',\n                        '#f0abfc',\n                        '#fbbf24',\n                        '#f59e0b',\n                        '#06b6d4',\n                        '#0891b2',\n                        '#10b981',\n                        '#059669'\n                    ];\n                    for(let i = 0; i < 150; i++){\n                        particles.push({\n                            x: Math.random() * canvas.width,\n                            y: Math.random() * canvas.height,\n                            vx: (Math.random() - 0.5) * 1.5,\n                            vy: (Math.random() - 0.5) * 1.5,\n                            size: Math.random() * 6 + 1,\n                            color: colors[Math.floor(Math.random() * colors.length)],\n                            opacity: Math.random() * 0.6 + 0.2,\n                            pulse: Math.random() * Math.PI * 2,\n                            pulseSpeed: 0.01 + Math.random() * 0.03,\n                            rotationSpeed: (Math.random() - 0.5) * 0.03,\n                            magnetism: Math.random() * 0.5 + 0.5,\n                            trail: [],\n                            energy: Math.random() * 100 + 50 // طاقة الجسيمة\n                        });\n                    }\n                    particlesRef.current = particles;\n                }\n            }[\"SimpleHeroSection.useEffect.createParticles\"];\n            createParticles();\n            // معالج حركة الماوس\n            const handleMouseMove = {\n                \"SimpleHeroSection.useEffect.handleMouseMove\": (e)=>{\n                    mouseRef.current = {\n                        x: e.clientX / window.innerWidth * 2 - 1,\n                        y: -(e.clientY / window.innerHeight) * 2 + 1\n                    };\n                }\n            }[\"SimpleHeroSection.useEffect.handleMouseMove\"];\n            window.addEventListener('mousemove', handleMouseMove);\n            // دالة رسم السداسي\n            const drawHexagon = {\n                \"SimpleHeroSection.useEffect.drawHexagon\": (ctx, x, y, size)=>{\n                    ctx.beginPath();\n                    for(let i = 0; i < 6; i++){\n                        const angle = i * Math.PI / 3;\n                        const px = x + size * Math.cos(angle);\n                        const py = y + size * Math.sin(angle);\n                        if (i === 0) {\n                            ctx.moveTo(px, py);\n                        } else {\n                            ctx.lineTo(px, py);\n                        }\n                    }\n                    ctx.closePath();\n                    ctx.stroke();\n                }\n            }[\"SimpleHeroSection.useEffect.drawHexagon\"];\n            // حلقة الرسم المتطورة مع تأثيرات مبهرة\n            const animate = {\n                \"SimpleHeroSection.useEffect.animate\": ()=>{\n                    // مسح تدريجي بدلاً من المسح الكامل\n                    ctx.fillStyle = 'rgba(255, 255, 255, 0.05)';\n                    ctx.fillRect(0, 0, canvas.width, canvas.height);\n                    const time = Date.now() * 0.001;\n                    const mouseX = mouseRef.current.x * canvas.width * 0.5 + canvas.width * 0.5;\n                    const mouseY = mouseRef.current.y * canvas.height * 0.5 + canvas.height * 0.5;\n                    // رسم موجات ديناميكية في الخلفية\n                    const waveCount = 5;\n                    for(let i = 0; i < waveCount; i++){\n                        ctx.beginPath();\n                        ctx.strokeStyle = \"rgba(168, 85, 247, \".concat(0.02 + Math.sin(time + i) * 0.01, \")\");\n                        ctx.lineWidth = 2;\n                        for(let x = 0; x <= canvas.width; x += 10){\n                            const y = canvas.height * 0.5 + Math.sin(x * 0.01 + time + i) * 50 * (i + 1) + Math.sin(x * 0.005 + time * 0.5 + i) * 30;\n                            if (x === 0) {\n                                ctx.moveTo(x, y);\n                            } else {\n                                ctx.lineTo(x, y);\n                            }\n                        }\n                        ctx.stroke();\n                    }\n                    // رسم شبكة سداسية متحركة\n                    const hexSize = 60;\n                    const hexRows = Math.ceil(canvas.height / (hexSize * 0.75)) + 2;\n                    const hexCols = Math.ceil(canvas.width / (hexSize * Math.sqrt(3))) + 2;\n                    ctx.strokeStyle = \"rgba(139, 92, 246, \".concat(0.04 + Math.sin(time * 0.5) * 0.02, \")\");\n                    ctx.lineWidth = 1;\n                    for(let row = 0; row < hexRows; row++){\n                        for(let col = 0; col < hexCols; col++){\n                            const x = col * hexSize * Math.sqrt(3) + row % 2 * hexSize * Math.sqrt(3) * 0.5;\n                            const y = row * hexSize * 0.75;\n                            const distanceToMouse = Math.sqrt((x - mouseX) ** 2 + (y - mouseY) ** 2);\n                            const influence = Math.max(0, 1 - distanceToMouse / 200);\n                            if (influence > 0.1) {\n                                drawHexagon(ctx, x + Math.sin(time + row + col) * 5, y + Math.cos(time + row + col) * 5, hexSize * 0.3);\n                            }\n                        }\n                    }\n                    // رسم دوائر متوهجة ناعمة\n                    const glowCircles = 5;\n                    for(let i = 0; i < glowCircles; i++){\n                        const x = canvas.width * (0.1 + i * 0.2);\n                        const y = canvas.height * (0.2 + Math.sin(time * 0.3 + i) * 0.3);\n                        const radius = 150 + Math.sin(time * 0.4 + i) * 50;\n                        const gradient = ctx.createRadialGradient(x, y, 0, x, y, radius);\n                        const colors = [\n                            \"rgba(168, 85, 247, \".concat(0.05 + Math.sin(time + i) * 0.02, \")\"),\n                            \"rgba(236, 72, 153, \".concat(0.03 + Math.sin(time + i + 1) * 0.015, \")\"),\n                            \"rgba(251, 191, 36, \".concat(0.02 + Math.sin(time + i + 2) * 0.01, \")\")\n                        ];\n                        gradient.addColorStop(0, colors[i % colors.length]);\n                        gradient.addColorStop(0.7, colors[(i + 1) % colors.length].replace(/[\\d.]+\\)/, '0.01)'));\n                        gradient.addColorStop(1, 'rgba(255, 255, 255, 0)');\n                        ctx.fillStyle = gradient;\n                        ctx.beginPath();\n                        ctx.arc(x, y, radius, 0, Math.PI * 2);\n                        ctx.fill();\n                    }\n                    // رسم أشكال هندسية متحركة\n                    const shapes = 3;\n                    for(let i = 0; i < shapes; i++){\n                        const x = canvas.width * (0.3 + i * 0.2);\n                        const y = canvas.height * (0.4 + Math.cos(time * 0.2 + i) * 0.2);\n                        const size = 30 + Math.sin(time + i) * 10;\n                        const rotation = time * 0.5 + i;\n                        ctx.save();\n                        ctx.translate(x, y);\n                        ctx.rotate(rotation);\n                        const gradient = ctx.createLinearGradient(-size, -size, size, size);\n                        gradient.addColorStop(0, \"rgba(168, 85, 247, \".concat(0.1 + Math.sin(time + i) * 0.05, \")\"));\n                        gradient.addColorStop(1, \"rgba(236, 72, 153, \".concat(0.05 + Math.cos(time + i) * 0.03, \")\"));\n                        ctx.fillStyle = gradient;\n                        ctx.beginPath();\n                        ctx.roundRect(-size / 2, -size / 2, size, size, 8);\n                        ctx.fill();\n                        ctx.restore();\n                    }\n                    // تحديث ورسم الجسيمات المحسنة\n                    particlesRef.current.forEach({\n                        \"SimpleHeroSection.useEffect.animate\": (particle, index)=>{\n                            // تحديث الموضع\n                            particle.x += particle.vx;\n                            particle.y += particle.vy;\n                            // تحديث التأثير النابض\n                            particle.pulse += particle.pulseSpeed;\n                            // تأثير الماوس المحسن\n                            const mouseInfluence = 80;\n                            const dx = mouseRef.current.x * canvas.width * 0.5 + canvas.width * 0.5 - particle.x;\n                            const dy = mouseRef.current.y * canvas.height * 0.5 + canvas.height * 0.5 - particle.y;\n                            const distance = Math.sqrt(dx * dx + dy * dy);\n                            if (distance < mouseInfluence) {\n                                const force = (mouseInfluence - distance) / mouseInfluence;\n                                particle.vx += dx * force * 0.002;\n                                particle.vy += dy * force * 0.002;\n                            }\n                            // حدود الشاشة مع ارتداد ناعم\n                            if (particle.x < 0 || particle.x > canvas.width) {\n                                particle.vx *= -0.8;\n                                particle.x = Math.max(0, Math.min(canvas.width, particle.x));\n                            }\n                            if (particle.y < 0 || particle.y > canvas.height) {\n                                particle.vy *= -0.8;\n                                particle.y = Math.max(0, Math.min(canvas.height, particle.y));\n                            }\n                            // رسم الجسيمة مع تأثير متوهج\n                            const pulseSize = particle.size + Math.sin(particle.pulse) * 1;\n                            const pulseOpacity = particle.opacity + Math.sin(particle.pulse) * 0.2;\n                            // رسم الهالة\n                            const gradient = ctx.createRadialGradient(particle.x, particle.y, 0, particle.x, particle.y, pulseSize * 3);\n                            gradient.addColorStop(0, particle.color.replace(')', ', 0.3)').replace('rgb', 'rgba'));\n                            gradient.addColorStop(1, particle.color.replace(')', ', 0)').replace('rgb', 'rgba'));\n                            ctx.fillStyle = gradient;\n                            ctx.globalAlpha = pulseOpacity * 0.5;\n                            ctx.beginPath();\n                            ctx.arc(particle.x, particle.y, pulseSize * 3, 0, Math.PI * 2);\n                            ctx.fill();\n                            // رسم الجسيمة الأساسية\n                            ctx.beginPath();\n                            ctx.arc(particle.x, particle.y, pulseSize, 0, Math.PI * 2);\n                            ctx.fillStyle = particle.color;\n                            ctx.globalAlpha = pulseOpacity;\n                            ctx.fill();\n                            // رسم خطوط الاتصال المحسنة\n                            particlesRef.current.forEach({\n                                \"SimpleHeroSection.useEffect.animate\": (otherParticle, otherIndex)=>{\n                                    if (index !== otherIndex && index < otherIndex) {\n                                        const dx = particle.x - otherParticle.x;\n                                        const dy = particle.y - otherParticle.y;\n                                        const distance = Math.sqrt(dx * dx + dy * dy);\n                                        if (distance < 120) {\n                                            const opacity = 0.15 * (1 - distance / 120);\n                                            const gradient = ctx.createLinearGradient(particle.x, particle.y, otherParticle.x, otherParticle.y);\n                                            gradient.addColorStop(0, \"rgba(168, 85, 247, \".concat(opacity, \")\"));\n                                            gradient.addColorStop(0.5, \"rgba(192, 132, 252, \".concat(opacity * 1.5, \")\"));\n                                            gradient.addColorStop(1, \"rgba(168, 85, 247, \".concat(opacity, \")\"));\n                                            ctx.beginPath();\n                                            ctx.moveTo(particle.x, particle.y);\n                                            ctx.lineTo(otherParticle.x, otherParticle.y);\n                                            ctx.strokeStyle = gradient;\n                                            ctx.lineWidth = 1 + opacity * 2;\n                                            ctx.stroke();\n                                        }\n                                    }\n                                }\n                            }[\"SimpleHeroSection.useEffect.animate\"]);\n                        }\n                    }[\"SimpleHeroSection.useEffect.animate\"]);\n                    ctx.globalAlpha = 1;\n                    animationRef.current = requestAnimationFrame(animate);\n                }\n            }[\"SimpleHeroSection.useEffect.animate\"];\n            animate();\n            // التنظيف\n            return ({\n                \"SimpleHeroSection.useEffect\": ()=>{\n                    if (animationRef.current) {\n                        cancelAnimationFrame(animationRef.current);\n                    }\n                    window.removeEventListener('resize', resizeCanvas);\n                    window.removeEventListener('mousemove', handleMouseMove);\n                }\n            })[\"SimpleHeroSection.useEffect\"];\n        }\n    }[\"SimpleHeroSection.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-white via-purple-50 to-pink-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                ref: canvasRef,\n                className: \"absolute inset-0 w-full h-full\",\n                style: {\n                    zIndex: 1\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                lineNumber: 299,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-t from-white/30 via-transparent to-purple-50/20\",\n                style: {\n                    zIndex: 2\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                lineNumber: 306,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 text-center px-4 max-w-6xl mx-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-5xl md:text-7xl font-bold text-gray-900 mb-6 leading-tight\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"bg-gradient-to-r from-purple-600 via-pink-600 to-purple-600 bg-clip-text text-transparent\",\n                                    children: \"TechnoFlash\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 311,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl md:text-2xl text-gray-700 mb-8 max-w-3xl mx-auto leading-relaxed\",\n                                children: \"منصتك الشاملة لأحدث التقنيات وأدوات الذكاء الاصطناعي\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                        lineNumber: 310,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row gap-4 justify-center items-center mb-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/articles\",\n                                className: \"group bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-4 rounded-xl font-semibold text-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"استكشف المقالات\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5 group-hover:translate-x-1 transition-transform\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M13 7l5 5m0 0l-5 5m5-5H6\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                            lineNumber: 329,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 328,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 323,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/ai-tools\",\n                                className: \"group border-2 border-purple-400 text-purple-400 px-8 py-4 rounded-xl font-semibold text-lg hover:bg-purple-400 hover:text-white transition-all duration-300 flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"أدوات الذكاء الاصطناعي\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 337,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5 group-hover:rotate-12 transition-transform\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M13 10V3L4 14h7v7l9-11h-7z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                            lineNumber: 339,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 338,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 333,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                        lineNumber: 322,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-2\",\n                                        children: \"100+\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 347,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-600\",\n                                        children: \"مقال تقني\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 348,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 346,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-2\",\n                                        children: \"50+\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 351,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-600\",\n                                        children: \"أداة ذكاء اصطناعي\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 352,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 350,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-2\",\n                                        children: \"1000+\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 355,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-600\",\n                                        children: \"قارئ نشط\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 356,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 354,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                        lineNumber: 345,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                lineNumber: 309,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-8 left-1/2 transform -translate-x-1/2 z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-bounce\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-8 h-8 border-2 border-gray-400/60 rounded-full flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-4 h-4 text-gray-600/80\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M19 14l-7 7m0 0l-7-7m7 7V3\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 366,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                            lineNumber: 365,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                        lineNumber: 364,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                    lineNumber: 363,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                lineNumber: 362,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n        lineNumber: 297,\n        columnNumber: 5\n    }, this);\n}\n_s(SimpleHeroSection, \"/8IKySTmGZxFymijsBTHUGtuaQs=\");\n_c = SimpleHeroSection;\nvar _c;\n$RefreshReg$(_c, \"SimpleHeroSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/SimpleHeroSection.tsx\n"));

/***/ })

});