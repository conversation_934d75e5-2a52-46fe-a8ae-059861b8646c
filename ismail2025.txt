عدل علي التصميم الصفحة كشكل مثل الكود واجعل الخطوط محليه كما هي وايضا الموقع يعمل كما هو فقط التصميم <!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TechnoFlash | تكنو فلاش - بوابتك للمستقبل التقني</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Google Fonts: Cairo -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700;900&display=swap" rel="stylesheet">
    
    <!-- Lucide Icons -->
    <script src="https://unpkg.com/lucide@latest"></script>

    <!-- Three.js for 3D background -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>

    <style>
        body {
            font-family: 'Cairo', sans-serif;
            scroll-behavior: smooth;
            background-color: #f8fafc; /* slate-50 */
        }
        
        #hero-canvas {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
        }

        .hero-content {
            position: relative;
            z-index: 2;
        }

        /* Scroll-triggered animations */
        .reveal {
            opacity: 0;
            transform: translateY(50px);
            transition: opacity 0.8s ease-out, transform 0.8s ease-out;
        }
        .reveal.visible {
            opacity: 1;
            transform: translateY(0);
        }

        /* Glassmorphism effect for Newsletter */
        .glass-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        /* Card hover glow effect */
        .card-glow-effect {
            position: relative;
            transition: all 0.3s ease;
        }
        .card-glow-effect:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px -15px rgba(148, 106, 224, 0.3);
        }
    </style>
</head>
<body class="text-slate-800">

    <!-- Header -->
    <header id="header" class="bg-white/80 backdrop-blur-lg sticky top-0 z-50 shadow-sm">
        <nav class="container mx-auto px-6 py-4">
            <div class="flex items-center justify-between">
                <a href="#" class="flex items-center space-x-2 rtl:space-x-reverse">
                    <span class="bg-gradient-to-br from-purple-600 to-pink-500 text-white font-bold text-2xl rounded-md p-2">T</span>
                    <span class="text-2xl font-bold text-slate-900">TechnoFlash</span>
                </a>
                <div class="hidden md:flex items-center space-x-8 rtl:space-x-reverse">
                    <a href="#" class="text-slate-600 hover:text-purple-600 transition-colors font-semibold">🏠 الرئيسية</a>
                    <a href="#latest-articles" class="text-slate-600 hover:text-purple-600 transition-colors">📰 أحدث المقالات</a>
                    <a href="#" class="text-slate-600 hover:text-purple-600 transition-colors">🤖 أدوات الذكاء الاصطناعي</a>
                    <a href="#" class="text-slate-600 hover:text-purple-600 transition-colors">⚙️ الخدمات</a>
                </div>
                <div class="flex items-center space-x-4 rtl:space-x-reverse">
                     <a href="#" class="hidden sm:block bg-gradient-to-r from-purple-600 to-pink-500 text-white px-5 py-2 rounded-lg shadow-md hover:shadow-lg hover:from-purple-700 hover:to-pink-600 transition-all transform hover:scale-105">
                        📞 اتصل بنا
                    </a>
                    <button id="mobile-menu-button" class="md:hidden p-2 rounded-md text-slate-600 hover:bg-slate-100 focus:outline-none">
                        <i data-lucide="menu" class="w-6 h-6"></i>
                    </button>
                </div>
            </div>
            <div id="mobile-menu" class="hidden md:hidden mt-4">
                <a href="#" class="block py-2 px-4 text-sm text-slate-700 hover:bg-purple-50 rounded-md">🏠 الرئيسية</a>
                <a href="#latest-articles" class="block py-2 px-4 text-sm text-slate-700 hover:bg-purple-50 rounded-md">📰 أحدث المقالات</a>
                <a href="#" class="block py-2 px-4 text-sm text-slate-700 hover:bg-purple-50 rounded-md">🤖 أدوات الذكاء الاصطناعي</a>
                <a href="#" class="block py-2 px-4 text-sm text-slate-700 hover:bg-purple-50 rounded-md">⚙️ الخدمات</a>
                <a href="#" class="block mt-2 w-full text-center bg-gradient-to-r from-purple-600 to-pink-500 text-white px-5 py-2 rounded-lg shadow-md hover:shadow-lg transition-all">
                    📞 اتصل بنا
                </a>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <main>
        <!-- Hero Section -->
        <section class="relative pt-24 pb-28 overflow-hidden bg-slate-50">
            <canvas id="hero-canvas"></canvas>
            <div class="hero-content container mx-auto px-6 text-center">
                <div class="inline-block bg-white/50 backdrop-blur-sm text-purple-800 text-sm font-semibold px-4 py-2 rounded-full mb-5">
                    ✨ بوابتك للمستقبل التقني
                </div>
                <h1 class="text-4xl md:text-6xl font-extrabold text-slate-900 leading-tight mb-4">
                    مستقبلك التقني<br>يبدأ من هنا
                </h1>
                <p class="max-w-3xl mx-auto text-lg text-slate-700 mb-8">
                    اكتشف أحدث التقنيات، أدوات الذكاء الاصطناعي المتطورة، ومقالات تقنية متخصصة لتطوير مهاراتك ومواكبة عالم التكنولوجيا المتسارع.
                </p>
                <div class="flex justify-center items-center space-x-4 rtl:space-x-reverse mb-12">
                    <a href="#latest-articles" class="bg-gradient-to-r from-purple-600 to-pink-500 text-white px-8 py-3 rounded-lg shadow-lg hover:shadow-xl hover:from-purple-700 hover:to-pink-600 transition-all transform hover:scale-105 font-semibold">
                        ابدأ الاستكشاف
                    </a>
                    <a href="#" class="bg-white text-slate-700 px-8 py-3 rounded-lg shadow-md hover:bg-slate-100 transition-all font-semibold flex items-center space-x-2 rtl:space-x-reverse">
                        <span>تصفح الخدمات</span>
                        <i data-lucide="arrow-left" class="w-5 h-5 rtl:hidden"></i>
                        <i data-lucide="arrow-right" class="w-5 h-5 ltr:hidden"></i>
                    </a>
                </div>
            </div>
        </section>

        <!-- Latest Articles Section -->
        <section id="latest-articles" class="py-20 bg-slate-100/70 -mt-16">
            <div class="container mx-auto px-6">
                <div class="text-center mb-12 reveal">
                    <h2 class="text-3xl md:text-4xl font-bold text-slate-900 mb-2">أحدث المقالات والأخبار</h2>
                    <p class="text-lg text-slate-600">كن أول من يقرأ تحليلاتنا وأخبارنا التقنية.</p>
                </div>
                <div class="grid lg:grid-cols-2 gap-8 items-start">
                    <!-- Featured Article -->
                    <a href="#" class="block lg:col-span-1 bg-white rounded-xl shadow-lg overflow-hidden group card-glow-effect reveal">
                        <div class="overflow-hidden h-80">
                            <img src="https://placehold.co/800x600/8b5cf6/ffffff?text=AI+Future" alt="صورة المقال المميز" class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500">
                        </div>
                        <div class="p-8">
                            <span class="text-sm bg-purple-100 text-purple-800 font-semibold px-3 py-1 rounded-full">الذكاء الاصطناعي</span>
                            <h3 class="mt-4 text-2xl font-bold text-slate-800 group-hover:text-purple-600 transition-colors">كيف سيغير نموذج GPT-5 عالم البرمجة؟</h3>
                            <p class="mt-3 text-slate-600">نظرة عميقة على الإمكانيات المتوقعة للجيل القادم من نماذج اللغة وتأثيرها المباشر على مستقبل المطورين والشركات التقنية.</p>
                        </div>
                    </a>
                    <!-- Other Articles -->
                    <div class="lg:col-span-1 grid gap-8">
                        <a href="#" class="block bg-white rounded-xl shadow-lg overflow-hidden group card-glow-effect reveal" style="transition-delay: 0.2s;">
                            <div class="flex items-center">
                                <div class="w-1/3 overflow-hidden h-40">
                                    <img src="https://placehold.co/400x400/ec4899/ffffff?text=Security" alt="صورة مقال" class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500">
                                </div>
                                <div class="w-2/3 p-5">
                                    <span class="text-xs bg-pink-100 text-pink-800 font-semibold px-2 py-1 rounded-full">الأمن السيبراني</span>
                                    <h3 class="mt-3 text-lg font-bold text-slate-800 group-hover:text-purple-600 transition-colors">5 خطوات عملية لحماية بياناتك</h3>
                                </div>
                            </div>
                        </a>
                        <a href="#" class="block bg-white rounded-xl shadow-lg overflow-hidden group card-glow-effect reveal" style="transition-delay: 0.4s;">
                            <div class="flex items-center">
                                <div class="w-1/3 overflow-hidden h-40">
                                    <img src="https://placehold.co/400x400/10b981/ffffff?text=Dev" alt="صورة مقال" class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500">
                                </div>
                                <div class="w-2/3 p-5">
                                    <span class="text-xs bg-teal-100 text-teal-800 font-semibold px-2 py-1 rounded-full">تطوير الويب</span>
                                    <h3 class="mt-3 text-lg font-bold text-slate-800 group-hover:text-purple-600 transition-colors">مقارنة بين React و Vue في 2025</h3>
                                </div>
                            </div>
                        </a>
                    </div>
                </div>
                <div class="text-center mt-12 reveal">
                    <a href="#" class="bg-white text-purple-600 px-8 py-3 rounded-lg shadow-md hover:bg-slate-100 transition-all font-semibold border border-purple-200 hover:border-purple-400">
                        تصفح جميع المقالات
                    </a>
                </div>
            </div>
        </section>

        <!-- Features Section -->
        <section class="py-20 bg-white">
            <div class="container mx-auto px-6">
                <div class="text-center mb-12 reveal">
                    <h2 class="text-3xl md:text-4xl font-bold text-slate-900 mb-2">تجربة تقنية متكاملة</h2>
                    <p class="text-lg text-slate-600">كل ما تحتاجه في مكان واحد.</p>
                </div>
                <div class="grid md:grid-cols-3 gap-8">
                    <div class="bg-slate-50 border border-slate-200 rounded-xl p-8 hover:shadow-xl hover:border-purple-200 transition-all duration-300 reveal">
                        <div class="inline-block bg-purple-100 text-purple-600 p-4 rounded-full mb-4"><i data-lucide="file-text" class="w-8 h-8"></i></div>
                        <h3 class="text-xl font-bold mb-2">محتوى تقني متميز</h3>
                        <p class="text-slate-600">مقالات وأدلة شاملة تغطي أحدث التطورات في عالم التكنولوجيا والبرمجة والذكاء الاصطناعي.</p>
                    </div>
                    <div class="bg-slate-50 border border-slate-200 rounded-xl p-8 hover:shadow-xl hover:border-pink-200 transition-all duration-300 reveal" style="transition-delay: 0.2s;">
                        <div class="inline-block bg-pink-100 text-pink-600 p-4 rounded-full mb-4"><i data-lucide="cpu" class="w-8 h-8"></i></div>
                        <h3 class="text-xl font-bold mb-2">أدوات ذكية متطورة</h3>
                        <p class="text-slate-600">مجموعة شاملة من أدوات الذكاء الاصطناعي المتقدمة لتسهيل عملك وزيادة إنتاجيتك.</p>
                    </div>
                    <div class="bg-slate-50 border border-slate-200 rounded-xl p-8 hover:shadow-xl hover:border-teal-200 transition-all duration-300 reveal" style="transition-delay: 0.4s;">
                        <div class="inline-block bg-teal-100 text-teal-600 p-4 rounded-full mb-4"><i data-lucide="settings-2" class="w-8 h-8"></i></div>
                        <h3 class="text-xl font-bold mb-2">خدمات متخصصة</h3>
                        <p class="text-slate-600">حلول تقنية مخصصة وخدمات تطوير واستشارات متخصصة لتحقيق أهدافك التقنية.</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Newsletter CTA -->
        <section class="py-20 bg-slate-100/70">
            <div class="container mx-auto px-6 reveal">
                <div class="bg-gradient-to-br from-slate-900 to-purple-900 text-white rounded-2xl p-8 md:p-12 shadow-2xl shadow-purple-200">
                    <div class="grid md:grid-cols-2 gap-8 items-center">
                        <div>
                            <h2 class="text-3xl font-bold mb-3">انضم إلى النخبة التقنية</h2>
                            <p class="opacity-80 mb-6">احصل على نشرتنا الأسبوعية المليئة بأحدث المقالات، أدوات الذكاء الاصطناعي المبتكرة، ونصائح البرمجة العملية مباشرة في بريدك.</p>
                        </div>
                        <div class="glass-card p-6 rounded-lg">
                            <form class="flex flex-col sm:flex-row gap-3">
                                <input type="email" placeholder="بريدك الإلكتروني" class="w-full px-4 py-3 rounded-md text-slate-800 bg-slate-50 focus:outline-none focus:ring-2 focus:ring-purple-400" required>
                                <button type="submit" class="bg-white text-purple-600 font-bold px-6 py-3 rounded-md hover:bg-slate-100 transition-colors flex-shrink-0">اشترك الآن</button>
                            </form>
                            <p class="text-xs opacity-70 mt-3 text-center sm:text-right">نحترم خصوصيتك. يمكنك إلغاء الاشتراك في أي وقت.</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        
    </main>

    <!-- Footer -->
    <footer class="bg-slate-900 text-white">
        <div class="container mx-auto px-6 py-12">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div class="col-span-1 md:col-span-1">
                    <a href="#" class="flex items-center space-x-2 rtl:space-x-reverse mb-4">
                        <span class="bg-gradient-to-br from-purple-600 to-pink-500 text-white font-bold text-2xl rounded-md p-2">T</span>
                        <span class="text-2xl font-bold">TechnoFlash</span>
                    </a>
                    <p class="text-slate-400">بوابتك للمستقبل التقني.</p>
                </div>
                <div>
                    <h4 class="font-bold text-lg mb-4">روابط سريعة</h4>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-slate-400 hover:text-white transition-colors">الرئيسية</a></li>
                        <li><a href="#latest-articles" class="text-slate-400 hover:text-white transition-colors">المقالات</a></li>
                        <li><a href="#" class="text-slate-400 hover:text-white transition-colors">أدوات الذكاء الاصطناعي</a></li>
                        <li><a href="#" class="text-slate-400 hover:text-white transition-colors">من نحن</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-bold text-lg mb-4">الخدمات</h4>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-slate-400 hover:text-white transition-colors">استشارات تقنية</a></li>
                        <li><a href="#" class="text-slate-400 hover:text-white transition-colors">تطوير المواقع</a></li>
                        <li><a href="#" class="text-slate-400 hover:text-white transition-colors">سياسة الخصوصية</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-bold text-lg mb-4">تابعنا</h4>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-slate-400 hover:text-white transition-colors flex items-center space-x-2 rtl:space-x-reverse"><i data-lucide="youtube" class="w-5 h-5 text-red-500"></i><span>قناة اليوتيوب</span></a></li>
                        <li><a href="#" class="text-slate-400 hover:text-white transition-colors flex items-center space-x-2 rtl:space-x-reverse"><i data-lucide="mail" class="w-5 h-5 text-purple-400"></i><span>البريد الإلكتروني</span></a></li>
                    </ul>
                </div>
            </div>
            <div class="mt-12 pt-8 border-t border-slate-800 text-center text-slate-500 text-sm">
                <p>&copy; 2025 TechnoFlash. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>

    <script>
        // Initialize Lucide Icons
        lucide.createIcons();

        // Mobile Menu Toggle
        const mobileMenuButton = document.getElementById('mobile-menu-button');
        const mobileMenu = document.getElementById('mobile-menu');
        mobileMenuButton.addEventListener('click', () => {
            mobileMenu.classList.toggle('hidden');
        });

        // Scroll-triggered animations
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('visible');
                }
            });
        }, {
            threshold: 0.1
        });

        document.querySelectorAll('.reveal').forEach(el => {
            observer.observe(el);
        });

        // Three.js background animation
        const canvas = document.getElementById('hero-canvas');
        if (canvas) {
            const scene = new THREE.Scene();
            const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
            const renderer = new THREE.WebGLRenderer({ canvas: canvas, alpha: true });
            
            renderer.setSize(window.innerWidth, window.innerHeight);
            renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));

            const particlesGeometry = new THREE.BufferGeometry;
            const count = 5000;

            const positions = new Float32Array(count * 3);
            for(let i = 0; i < count * 3; i++) {
                positions[i] = (Math.random() - 0.5) * 10;
            }
            particlesGeometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
            
            const particlesMaterial = new THREE.PointsMaterial({
                size: 0.02,
                sizeAttenuation: true,
                color: 0x8b5cf6, // purple-500
                transparent: true,
                opacity: 0.7
            });

            const particles = new THREE.Points(particlesGeometry, particlesMaterial);
            scene.add(particles);

            camera.position.z = 5;

            const mouse = new THREE.Vector2();
            window.addEventListener('mousemove', (event) => {
                mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
                mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;
            });

            const clock = new THREE.Clock();
            const animate = () => {
                const elapsedTime = clock.getElapsedTime();
                
                particles.rotation.y = elapsedTime * 0.05;
                particles.rotation.x = mouse.y * 0.1;
                particles.rotation.y += mouse.x * 0.1;

                renderer.render(scene, camera);
                window.requestAnimationFrame(animate);
            };
            animate();

            window.addEventListener('resize', () => {
                camera.aspect = window.innerWidth / window.innerHeight;
                camera.updateProjectionMatrix();
                renderer.setSize(window.innerWidth, window.innerHeight);
                renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
            });
        }

    </script>
</body>
</html>
