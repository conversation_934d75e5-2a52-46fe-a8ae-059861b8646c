/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/ads/route";
exports.ids = ["app/api/ads/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fads%2Froute&page=%2Fapi%2Fads%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fads%2Froute.ts&appDir=C%3A%5CUsers%5Cismail%5CDownloads%5C11111111111111%5Ctechno-flashi%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cismail%5CDownloads%5C11111111111111%5Ctechno-flashi&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fads%2Froute&page=%2Fapi%2Fads%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fads%2Froute.ts&appDir=C%3A%5CUsers%5Cismail%5CDownloads%5C11111111111111%5Ctechno-flashi%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cismail%5CDownloads%5C11111111111111%5Ctechno-flashi&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_ismail_Downloads_11111111111111_techno_flashi_src_app_api_ads_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/ads/route.ts */ \"(rsc)/./src/app/api/ads/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"standalone\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/ads/route\",\n        pathname: \"/api/ads\",\n        filename: \"route\",\n        bundlePath: \"app/api/ads/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\app\\\\api\\\\ads\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_ismail_Downloads_11111111111111_techno_flashi_src_app_api_ads_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fads%2Froute&page=%2Fapi%2Fads%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fads%2Froute.ts&appDir=C%3A%5CUsers%5Cismail%5CDownloads%5C11111111111111%5Ctechno-flashi%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cismail%5CDownloads%5C11111111111111%5Ctechno-flashi&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/ads/route.ts":
/*!**********************************!*\
  !*** ./src/app/api/ads/route.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase */ \"(rsc)/./src/lib/supabase.ts\");\n\n\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        // معاملات الاستعلام\n        const type = searchParams.get('type');\n        const placement = searchParams.get('placement');\n        const status = searchParams.get('status');\n        const isActive = searchParams.get('is_active');\n        const limit = searchParams.get('limit');\n        console.log('Fetching ads with params:', {\n            type,\n            placement,\n            status,\n            isActive,\n            limit\n        });\n        // بناء الاستعلام\n        let query = _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from('advertisements').select('*');\n        // تطبيق الفلاتر\n        if (type) {\n            query = query.eq('type', type);\n        }\n        if (placement) {\n            query = query.eq('position', placement);\n        }\n        // حقل status لا يوجد في الجدول الجديد - تم استبداله بـ is_active\n        // if (status) {\n        //   query = query.eq('status', status);\n        // }\n        if (isActive !== null) {\n            query = query.eq('is_active', isActive === 'true');\n        }\n        // ترتيب حسب الأولوية ثم تاريخ الإنشاء\n        query = query.order('priority', {\n            ascending: true\n        });\n        query = query.order('created_at', {\n            ascending: false\n        });\n        // تطبيق الحد\n        if (limit) {\n            query = query.limit(parseInt(limit));\n        }\n        const { data: ads, error } = await query;\n        if (error) {\n            console.error('Database error:', error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to fetch ads',\n                details: error.message\n            }, {\n                status: 500\n            });\n        }\n        console.log(`Fetched ${ads?.length || 0} ads from database`);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            ads: ads || [],\n            count: ads?.length || 0\n        });\n    } catch (error) {\n        console.error('API error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function POST(request) {\n    try {\n        console.log('POST /api/ads - Creating new ad');\n        const body = await request.json();\n        console.log('Creating new ad:', body);\n        // التحقق من البيانات المطلوبة\n        if (!body.title || !body.position) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Title and position are required'\n            }, {\n                status: 400\n            });\n        }\n        const { data: ad, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from('advertisements').insert([\n            {\n                title: body.title,\n                content: body.content || body.description,\n                image_url: body.image_url,\n                target_url: body.target_url || body.link_url,\n                video_url: body.video_url,\n                custom_css: body.custom_css,\n                custom_js: body.custom_js,\n                position: body.position || body.placement,\n                type: body.type || 'text',\n                priority: body.priority || 1,\n                is_active: body.is_active !== false,\n                start_date: body.start_date || null,\n                end_date: body.end_date || null,\n                max_views: body.max_views || null,\n                max_clicks: body.max_clicks || null\n            }\n        ]).select().single();\n        if (error) {\n            console.error('Database error creating ad:', error);\n            // تحديد نوع الخطأ لإعطاء رسالة أوضح\n            let errorMessage = 'Failed to create ad';\n            if (error.message.includes('permission')) {\n                errorMessage = 'Permission denied. Please check authentication.';\n            } else if (error.message.includes('duplicate')) {\n                errorMessage = 'Ad with this title already exists.';\n            } else if (error.message.includes('validation')) {\n                errorMessage = 'Invalid data provided.';\n            }\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: errorMessage,\n                details: error.message,\n                code: error.code || 'UNKNOWN_ERROR'\n            }, {\n                status: 500\n            });\n        }\n        console.log('Ad created successfully:', ad.id);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            ad\n        }, {\n            status: 201\n        });\n    } catch (error) {\n        console.error('API error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/ads/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fixArabicEncoding: () => (/* binding */ fixArabicEncoding),\n/* harmony export */   fixObjectEncoding: () => (/* binding */ fixObjectEncoding),\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n// هذا الملف يقوم بإعداد وتصدير \"العميل\" الخاص بـ Supabase\n// الذي سنستخدمه للتواصل مع قاعدة البيانات في كل مكان بالموقع\n\n// Supabase Project Configuration - استخدام متغيرات البيئة\nconst supabaseUrl = \"https://zgktrwpladrkhhemhnni.supabase.co\" || 0;\nconst supabaseKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inpna3Ryd3BsYWRya2hoZW1obm5pIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIwMjk0NTIsImV4cCI6MjA2NzYwNTQ1Mn0.uHKisokqk484Vq5QjCbVbcdcabxArrtKUMxjdCihe04\" || 0;\n// التحقق من وجود المتغيرات\nif (!supabaseUrl) {\n    throw new Error('Missing NEXT_PUBLIC_SUPABASE_URL environment variable');\n}\nif (!supabaseKey) {\n    throw new Error('Missing NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable');\n}\n// دالة مساعدة لإصلاح encoding النص العربي\nfunction fixArabicEncoding(text) {\n    if (!text) return text;\n    try {\n        // إذا كان النص يحتوي على Unicode escape sequences\n        if (text.includes('\\\\u')) {\n            return JSON.parse(`\"${text}\"`);\n        }\n        return text;\n    } catch (error) {\n        console.warn('Failed to fix Arabic encoding for:', text);\n        return text;\n    }\n}\n// دالة لإصلاح encoding في كائن كامل\nfunction fixObjectEncoding(obj) {\n    if (!obj) return obj;\n    const fixed = {\n        ...obj\n    };\n    for(const key in fixed){\n        if (typeof fixed[key] === 'string') {\n            fixed[key] = fixArabicEncoding(fixed[key]);\n        } else if (Array.isArray(fixed[key])) {\n            fixed[key] = fixed[key].map((item)=>typeof item === 'string' ? fixArabicEncoding(item) : item);\n        }\n    }\n    return fixed;\n}\n// إنشاء وتصدير العميل مع إعدادات إضافية لدعم النص العربي\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseKey, {\n    db: {\n        schema: 'public'\n    },\n    auth: {\n        autoRefreshToken: true,\n        persistSession: true,\n        detectSessionInUrl: true\n    }\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/isows"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fads%2Froute&page=%2Fapi%2Fads%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fads%2Froute.ts&appDir=C%3A%5CUsers%5Cismail%5CDownloads%5C11111111111111%5Ctechno-flashi%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cismail%5CDownloads%5C11111111111111%5Ctechno-flashi&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();