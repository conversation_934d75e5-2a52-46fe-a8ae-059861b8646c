"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/PerformanceOptimizer.tsx":
/*!*************************************************!*\
  !*** ./src/components/PerformanceOptimizer.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PerformanceOptimizer: () => (/* binding */ PerformanceOptimizer),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ PerformanceOptimizer,default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n// Lazy load non-critical components\nconst LazyFeaturedArticlesSection = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.lazy)(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_FeaturedArticlesSection_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ./FeaturedArticlesSection */ \"(app-pages-browser)/./src/components/FeaturedArticlesSection.tsx\")).then((module)=>({\n            default: module.FeaturedArticlesSection\n        })));\n_c = LazyFeaturedArticlesSection;\nconst LazyServicesSection = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.lazy)(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_ServicesSection_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ./ServicesSection */ \"(app-pages-browser)/./src/components/ServicesSection.tsx\")).then((module)=>({\n            default: module.ServicesSection\n        })));\n_c1 = LazyServicesSection;\nconst LazyAdBannerTop = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.lazy)(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_AdBannerTop_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ./AdBannerTop */ \"(app-pages-browser)/./src/components/AdBannerTop.tsx\")).then((module)=>({\n            default: module.default\n        })));\n_c2 = LazyAdBannerTop;\n// Loading skeleton components\nconst ArticlesSkeleton = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"py-16 px-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-8 bg-gray-200 rounded w-64 mx-auto mb-4 animate-pulse\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n                            lineNumber: 23,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-4 bg-gray-200 rounded w-96 mx-auto animate-pulse\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n                            lineNumber: 24,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n                    lineNumber: 22,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                    children: [\n                        ...Array(6)\n                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-xl border border-gray-200 overflow-hidden\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-48 bg-gray-200 animate-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n                                    lineNumber: 29,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-gray-200 rounded w-3/4 mb-2 animate-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n                                            lineNumber: 31,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-gray-200 rounded w-1/2 mb-4 animate-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n                                            lineNumber: 32,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-3 bg-gray-200 rounded w-full mb-2 animate-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n                                            lineNumber: 33,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-3 bg-gray-200 rounded w-2/3 animate-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n                                            lineNumber: 34,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n                                    lineNumber: 30,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, i, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 11\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n            lineNumber: 21,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n        lineNumber: 20,\n        columnNumber: 3\n    }, undefined);\n_c3 = ArticlesSkeleton;\nconst ServicesSkeleton = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"py-16 px-4 bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-8 bg-gray-200 rounded w-48 mx-auto mb-4 animate-pulse\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n                            lineNumber: 47,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-4 bg-gray-200 rounded w-80 mx-auto animate-pulse\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n                    lineNumber: 46,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                    children: [\n                        ...Array(3)\n                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-xl p-8 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-16 h-16 bg-gray-200 rounded-lg mx-auto mb-6 animate-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n                                    lineNumber: 53,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-6 bg-gray-200 rounded w-32 mx-auto mb-4 animate-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 bg-gray-200 rounded w-full mb-2 animate-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 bg-gray-200 rounded w-3/4 mx-auto animate-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, i, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 11\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n                    lineNumber: 50,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n            lineNumber: 45,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n        lineNumber: 44,\n        columnNumber: 3\n    }, undefined);\n_c4 = ServicesSkeleton;\nconst AdSkeleton = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"ad-banner bg-background-secondary border border-light-border rounded-lg\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-20 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-text-description text-sm\",\n                children: \"إعلان\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n                lineNumber: 67,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n            lineNumber: 66,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n        lineNumber: 65,\n        columnNumber: 3\n    }, undefined);\n_c5 = AdSkeleton;\nconst LazyComponent = (param)=>{\n    let { children, fallback, rootMargin = '100px', threshold = 0.1 } = param;\n    _s();\n    const [ref, isIntersecting] = useIntersectionObserver({\n        rootMargin,\n        threshold\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        children: isIntersecting ? children : fallback\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n        lineNumber: 92,\n        columnNumber: 5\n    }, undefined);\n};\n_s(LazyComponent, \"FtwX3XzyHW6LsNpWjTaSblHYEHs=\", false, function() {\n    return [\n        useIntersectionObserver\n    ];\n});\n_c6 = LazyComponent;\nfunction PerformanceOptimizer(param) {\n    let { latestArticles, latestServices = [] } = param;\n    _s1();\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PerformanceOptimizer.useEffect\": ()=>{\n            setIsClient(true);\n        }\n    }[\"PerformanceOptimizer.useEffect\"], []);\n    if (!isClient) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AdSkeleton, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n                    lineNumber: 114,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ArticlesSkeleton, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n                    lineNumber: 115,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ServicesSkeleton, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n                    lineNumber: 116,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LazyComponent, {\n                fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AdSkeleton, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 32\n                }, void 0),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n                    fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AdSkeleton, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 29\n                    }, void 0),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LazyAdBannerTop, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n                    lineNumber: 125,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n                lineNumber: 124,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LazyComponent, {\n                fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ArticlesSkeleton, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n                    lineNumber: 131,\n                    columnNumber: 32\n                }, void 0),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n                    fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ArticlesSkeleton, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 29\n                    }, void 0),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LazyFeaturedArticlesSection, {\n                        articles: latestArticles\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n                lineNumber: 131,\n                columnNumber: 7\n            }, this),\n            latestServices && latestServices.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LazyComponent, {\n                fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ServicesSkeleton, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n                    lineNumber: 139,\n                    columnNumber: 34\n                }, void 0),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n                    fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ServicesSkeleton, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 31\n                    }, void 0),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LazyServicesSection, {\n                        services: latestServices\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n                    lineNumber: 140,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\PerformanceOptimizer.tsx\",\n                lineNumber: 139,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s1(PerformanceOptimizer, \"k460N28PNzD7zo1YW47Q9UigQis=\");\n_c7 = PerformanceOptimizer;\n// Hook for intersection observer\nfunction useIntersectionObserver() {\n    let { rootMargin = '0px', threshold = 0.1 } = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    _s2();\n    const [ref, setRef] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isIntersecting, setIsIntersecting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"useIntersectionObserver.useEffect\": ()=>{\n            if (!ref) return;\n            const observer = new IntersectionObserver({\n                \"useIntersectionObserver.useEffect\": (param)=>{\n                    let [entry] = param;\n                    if (entry.isIntersecting) {\n                        setIsIntersecting(true);\n                        observer.disconnect();\n                    }\n                }\n            }[\"useIntersectionObserver.useEffect\"], {\n                rootMargin,\n                threshold\n            });\n            observer.observe(ref);\n            return ({\n                \"useIntersectionObserver.useEffect\": ()=>observer.disconnect()\n            })[\"useIntersectionObserver.useEffect\"];\n        }\n    }[\"useIntersectionObserver.useEffect\"], [\n        ref,\n        rootMargin,\n        threshold\n    ]);\n    return [\n        setRef,\n        isIntersecting\n    ];\n}\n_s2(useIntersectionObserver, \"rO9taFyS6DEQG6eLdFtcL3uw0s8=\");\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PerformanceOptimizer);\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7;\n$RefreshReg$(_c, \"LazyFeaturedArticlesSection\");\n$RefreshReg$(_c1, \"LazyServicesSection\");\n$RefreshReg$(_c2, \"LazyAdBannerTop\");\n$RefreshReg$(_c3, \"ArticlesSkeleton\");\n$RefreshReg$(_c4, \"ServicesSkeleton\");\n$RefreshReg$(_c5, \"AdSkeleton\");\n$RefreshReg$(_c6, \"LazyComponent\");\n$RefreshReg$(_c7, \"PerformanceOptimizer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/PerformanceOptimizer.tsx\n"));

/***/ })

});