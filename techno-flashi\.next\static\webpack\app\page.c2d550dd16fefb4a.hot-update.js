"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/SimpleHeroSection.tsx":
/*!**********************************************!*\
  !*** ./src/components/SimpleHeroSection.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SimpleHeroSection: () => (/* binding */ SimpleHeroSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ SimpleHeroSection auto */ \nvar _s = $RefreshSig$();\n\n\nfunction SimpleHeroSection() {\n    _s();\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const animationRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    const mouseRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        x: 0,\n        y: 0\n    });\n    const particlesRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SimpleHeroSection.useEffect\": ()=>{\n            const canvas = canvasRef.current;\n            if (!canvas) return;\n            const ctx = canvas.getContext('2d');\n            if (!ctx) return;\n            // إعداد الكانفاس\n            const resizeCanvas = {\n                \"SimpleHeroSection.useEffect.resizeCanvas\": ()=>{\n                    canvas.width = window.innerWidth;\n                    canvas.height = window.innerHeight;\n                }\n            }[\"SimpleHeroSection.useEffect.resizeCanvas\"];\n            resizeCanvas();\n            window.addEventListener('resize', resizeCanvas);\n            // إنشاء الجسيمات الذكية المتطورة\n            const createParticles = {\n                \"SimpleHeroSection.useEffect.createParticles\": ()=>{\n                    const particles = [];\n                    const colors = [\n                        '#8b5cf6',\n                        '#a855f7',\n                        '#c084fc',\n                        '#d8b4fe',\n                        '#e879f9',\n                        '#f0abfc',\n                        '#fbbf24',\n                        '#f59e0b',\n                        '#06b6d4',\n                        '#0891b2',\n                        '#10b981',\n                        '#059669'\n                    ];\n                    for(let i = 0; i < 150; i++){\n                        particles.push({\n                            x: Math.random() * canvas.width,\n                            y: Math.random() * canvas.height,\n                            vx: (Math.random() - 0.5) * 1.5,\n                            vy: (Math.random() - 0.5) * 1.5,\n                            size: Math.random() * 6 + 1,\n                            color: colors[Math.floor(Math.random() * colors.length)],\n                            opacity: Math.random() * 0.6 + 0.2,\n                            pulse: Math.random() * Math.PI * 2,\n                            pulseSpeed: 0.01 + Math.random() * 0.03,\n                            rotationSpeed: (Math.random() - 0.5) * 0.03,\n                            magnetism: Math.random() * 0.5 + 0.5,\n                            trail: [],\n                            energy: Math.random() * 100 + 50 // طاقة الجسيمة\n                        });\n                    }\n                    particlesRef.current = particles;\n                }\n            }[\"SimpleHeroSection.useEffect.createParticles\"];\n            createParticles();\n            // معالج حركة الماوس\n            const handleMouseMove = {\n                \"SimpleHeroSection.useEffect.handleMouseMove\": (e)=>{\n                    mouseRef.current = {\n                        x: e.clientX / window.innerWidth * 2 - 1,\n                        y: -(e.clientY / window.innerHeight) * 2 + 1\n                    };\n                }\n            }[\"SimpleHeroSection.useEffect.handleMouseMove\"];\n            window.addEventListener('mousemove', handleMouseMove);\n            // دالة رسم السداسي\n            const drawHexagon = {\n                \"SimpleHeroSection.useEffect.drawHexagon\": (ctx, x, y, size)=>{\n                    ctx.beginPath();\n                    for(let i = 0; i < 6; i++){\n                        const angle = i * Math.PI / 3;\n                        const px = x + size * Math.cos(angle);\n                        const py = y + size * Math.sin(angle);\n                        if (i === 0) {\n                            ctx.moveTo(px, py);\n                        } else {\n                            ctx.lineTo(px, py);\n                        }\n                    }\n                    ctx.closePath();\n                    ctx.stroke();\n                }\n            }[\"SimpleHeroSection.useEffect.drawHexagon\"];\n            // حلقة الرسم المتطورة مع تأثيرات مبهرة\n            const animate = {\n                \"SimpleHeroSection.useEffect.animate\": ()=>{\n                    // مسح تدريجي بدلاً من المسح الكامل\n                    ctx.fillStyle = 'rgba(255, 255, 255, 0.05)';\n                    ctx.fillRect(0, 0, canvas.width, canvas.height);\n                    const time = Date.now() * 0.001;\n                    const mouseX = mouseRef.current.x * canvas.width * 0.5 + canvas.width * 0.5;\n                    const mouseY = mouseRef.current.y * canvas.height * 0.5 + canvas.height * 0.5;\n                    // رسم موجات ديناميكية في الخلفية\n                    const waveCount = 5;\n                    for(let i = 0; i < waveCount; i++){\n                        ctx.beginPath();\n                        ctx.strokeStyle = \"rgba(168, 85, 247, \".concat(0.02 + Math.sin(time + i) * 0.01, \")\");\n                        ctx.lineWidth = 2;\n                        for(let x = 0; x <= canvas.width; x += 10){\n                            const y = canvas.height * 0.5 + Math.sin(x * 0.01 + time + i) * 50 * (i + 1) + Math.sin(x * 0.005 + time * 0.5 + i) * 30;\n                            if (x === 0) {\n                                ctx.moveTo(x, y);\n                            } else {\n                                ctx.lineTo(x, y);\n                            }\n                        }\n                        ctx.stroke();\n                    }\n                    // رسم شبكة سداسية متحركة\n                    const hexSize = 60;\n                    const hexRows = Math.ceil(canvas.height / (hexSize * 0.75)) + 2;\n                    const hexCols = Math.ceil(canvas.width / (hexSize * Math.sqrt(3))) + 2;\n                    ctx.strokeStyle = \"rgba(139, 92, 246, \".concat(0.04 + Math.sin(time * 0.5) * 0.02, \")\");\n                    ctx.lineWidth = 1;\n                    for(let row = 0; row < hexRows; row++){\n                        for(let col = 0; col < hexCols; col++){\n                            const x = col * hexSize * Math.sqrt(3) + row % 2 * hexSize * Math.sqrt(3) * 0.5;\n                            const y = row * hexSize * 0.75;\n                            const distanceToMouse = Math.sqrt((x - mouseX) ** 2 + (y - mouseY) ** 2);\n                            const influence = Math.max(0, 1 - distanceToMouse / 200);\n                            if (influence > 0.1) {\n                                drawHexagon(ctx, x + Math.sin(time + row + col) * 5, y + Math.cos(time + row + col) * 5, hexSize * 0.3);\n                            }\n                        }\n                    }\n                    // رسم دوائر متوهجة ناعمة\n                    const glowCircles = 5;\n                    for(let i = 0; i < glowCircles; i++){\n                        const x = canvas.width * (0.1 + i * 0.2);\n                        const y = canvas.height * (0.2 + Math.sin(time * 0.3 + i) * 0.3);\n                        const radius = 150 + Math.sin(time * 0.4 + i) * 50;\n                        const gradient = ctx.createRadialGradient(x, y, 0, x, y, radius);\n                        const colors = [\n                            \"rgba(168, 85, 247, \".concat(0.05 + Math.sin(time + i) * 0.02, \")\"),\n                            \"rgba(236, 72, 153, \".concat(0.03 + Math.sin(time + i + 1) * 0.015, \")\"),\n                            \"rgba(251, 191, 36, \".concat(0.02 + Math.sin(time + i + 2) * 0.01, \")\")\n                        ];\n                        gradient.addColorStop(0, colors[i % colors.length]);\n                        gradient.addColorStop(0.7, colors[(i + 1) % colors.length].replace(/[\\d.]+\\)/, '0.01)'));\n                        gradient.addColorStop(1, 'rgba(255, 255, 255, 0)');\n                        ctx.fillStyle = gradient;\n                        ctx.beginPath();\n                        ctx.arc(x, y, radius, 0, Math.PI * 2);\n                        ctx.fill();\n                    }\n                    // رسم أشكال هندسية متحركة\n                    const shapes = 3;\n                    for(let i = 0; i < shapes; i++){\n                        const x = canvas.width * (0.3 + i * 0.2);\n                        const y = canvas.height * (0.4 + Math.cos(time * 0.2 + i) * 0.2);\n                        const size = 30 + Math.sin(time + i) * 10;\n                        const rotation = time * 0.5 + i;\n                        ctx.save();\n                        ctx.translate(x, y);\n                        ctx.rotate(rotation);\n                        const gradient = ctx.createLinearGradient(-size, -size, size, size);\n                        gradient.addColorStop(0, \"rgba(168, 85, 247, \".concat(0.1 + Math.sin(time + i) * 0.05, \")\"));\n                        gradient.addColorStop(1, \"rgba(236, 72, 153, \".concat(0.05 + Math.cos(time + i) * 0.03, \")\"));\n                        ctx.fillStyle = gradient;\n                        ctx.beginPath();\n                        ctx.roundRect(-size / 2, -size / 2, size, size, 8);\n                        ctx.fill();\n                        ctx.restore();\n                    }\n                    // تحديث ورسم الجسيمات الذكية المتطورة\n                    particlesRef.current.forEach({\n                        \"SimpleHeroSection.useEffect.animate\": (particle, index)=>{\n                            // التأكد من صحة القيم\n                            particle.size = Math.max(1, particle.size || 2);\n                            particle.energy = Math.max(50, Math.min(100, particle.energy || 50));\n                            particle.opacity = Math.max(0.1, Math.min(1, particle.opacity || 0.5));\n                            // حفظ الموضع السابق للمسار\n                            particle.trail.push({\n                                x: particle.x,\n                                y: particle.y\n                            });\n                            if (particle.trail.length > 5) {\n                                particle.trail.shift();\n                            }\n                            // تحديث الموضع مع فيزياء متقدمة\n                            particle.x += particle.vx;\n                            particle.y += particle.vy;\n                            particle.pulse += particle.pulseSpeed;\n                            // تأثير الماوس المغناطيسي المتقدم\n                            const mouseInfluence = 120;\n                            const dx = mouseX - particle.x;\n                            const dy = mouseY - particle.y;\n                            const distance = Math.sqrt(dx * dx + dy * dy);\n                            if (distance < mouseInfluence) {\n                                const force = (mouseInfluence - distance) / mouseInfluence * particle.magnetism;\n                                const angle = Math.atan2(dy, dx);\n                                particle.vx += Math.cos(angle) * force * 0.003;\n                                particle.vy += Math.sin(angle) * force * 0.003;\n                                // تأثير الطاقة\n                                particle.energy = Math.min(100, particle.energy + force * 2);\n                            } else {\n                                // تقليل الطاقة تدريجياً\n                                particle.energy = Math.max(50, particle.energy - 0.5);\n                            }\n                            // تطبيق الاحتكاك\n                            particle.vx *= 0.99;\n                            particle.vy *= 0.99;\n                            // حدود الشاشة مع ارتداد ديناميكي\n                            if (particle.x < 0 || particle.x > canvas.width) {\n                                particle.vx *= -0.7;\n                                particle.x = Math.max(0, Math.min(canvas.width, particle.x));\n                            }\n                            if (particle.y < 0 || particle.y > canvas.height) {\n                                particle.vy *= -0.7;\n                                particle.y = Math.max(0, Math.min(canvas.height, particle.y));\n                            }\n                            // رسم مسار الجسيمة\n                            if (particle.trail.length > 1) {\n                                ctx.beginPath();\n                                ctx.strokeStyle = particle.color.replace(')', ', 0.2)').replace('rgb', 'rgba');\n                                ctx.lineWidth = 1;\n                                for(let i = 0; i < particle.trail.length - 1; i++){\n                                    const alpha = i / particle.trail.length;\n                                    ctx.globalAlpha = alpha * 0.3;\n                                    if (i === 0) {\n                                        ctx.moveTo(particle.trail[i].x, particle.trail[i].y);\n                                    } else {\n                                        ctx.lineTo(particle.trail[i].x, particle.trail[i].y);\n                                    }\n                                }\n                                ctx.stroke();\n                            }\n                            // رسم الجسيمة مع تأثيرات متقدمة\n                            const energyFactor = particle.energy / 100;\n                            const pulseSize = Math.max(1, particle.size + Math.sin(particle.pulse) * 2 * energyFactor);\n                            const pulseOpacity = Math.max(0.1, particle.opacity + Math.sin(particle.pulse) * 0.3 * energyFactor);\n                            // رسم الهالة الخارجية\n                            const outerRadius = Math.max(1, pulseSize * 4);\n                            const outerGradient = ctx.createRadialGradient(particle.x, particle.y, 0, particle.x, particle.y, outerRadius);\n                            outerGradient.addColorStop(0, particle.color.replace(')', ', 0.4)').replace('rgb', 'rgba'));\n                            outerGradient.addColorStop(0.5, particle.color.replace(')', ', 0.2)').replace('rgb', 'rgba'));\n                            outerGradient.addColorStop(1, particle.color.replace(')', ', 0)').replace('rgb', 'rgba'));\n                            ctx.fillStyle = outerGradient;\n                            ctx.globalAlpha = pulseOpacity * 0.6 * energyFactor;\n                            ctx.beginPath();\n                            ctx.arc(particle.x, particle.y, outerRadius, 0, Math.PI * 2);\n                            ctx.fill();\n                            // رسم الهالة الداخلية\n                            const innerRadius = Math.max(1, pulseSize * 2);\n                            const innerGradient = ctx.createRadialGradient(particle.x, particle.y, 0, particle.x, particle.y, innerRadius);\n                            innerGradient.addColorStop(0, particle.color.replace(')', ', 0.8)').replace('rgb', 'rgba'));\n                            innerGradient.addColorStop(1, particle.color.replace(')', ', 0.2)').replace('rgb', 'rgba'));\n                            ctx.fillStyle = innerGradient;\n                            ctx.globalAlpha = pulseOpacity * 0.8;\n                            ctx.beginPath();\n                            ctx.arc(particle.x, particle.y, innerRadius, 0, Math.PI * 2);\n                            ctx.fill();\n                            // رسم الجسيمة الأساسية\n                            ctx.beginPath();\n                            ctx.arc(particle.x, particle.y, Math.max(0.5, pulseSize), 0, Math.PI * 2);\n                            ctx.fillStyle = particle.color;\n                            ctx.globalAlpha = Math.max(0.1, Math.min(1, pulseOpacity));\n                            ctx.fill();\n                            // رسم خطوط الاتصال الذكية المتطورة\n                            particlesRef.current.forEach({\n                                \"SimpleHeroSection.useEffect.animate\": (otherParticle, otherIndex)=>{\n                                    if (index !== otherIndex && index < otherIndex) {\n                                        const dx = particle.x - otherParticle.x;\n                                        const dy = particle.y - otherParticle.y;\n                                        const distance = Math.sqrt(dx * dx + dy * dy);\n                                        if (distance < 150) {\n                                            const opacity = Math.max(0, 0.2 * (1 - distance / 150));\n                                            const energyBonus = (particle.energy + otherParticle.energy) / 200;\n                                            const finalOpacity = Math.max(0.01, Math.min(1, opacity * (1 + energyBonus)));\n                                            // خط متدرج ديناميكي\n                                            const gradient = ctx.createLinearGradient(particle.x, particle.y, otherParticle.x, otherParticle.y);\n                                            const color1 = particle.color.replace(')', ', ' + finalOpacity + ')').replace('rgb', 'rgba');\n                                            const color2 = otherParticle.color.replace(')', ', ' + finalOpacity + ')').replace('rgb', 'rgba');\n                                            const midColor = \"rgba(192, 132, 252, \".concat(finalOpacity * 1.2, \")\");\n                                            gradient.addColorStop(0, color1);\n                                            gradient.addColorStop(0.5, midColor);\n                                            gradient.addColorStop(1, color2);\n                                            // رسم خط متموج\n                                            ctx.beginPath();\n                                            ctx.strokeStyle = gradient;\n                                            ctx.lineWidth = 1 + finalOpacity * 3;\n                                            const steps = 10;\n                                            for(let i = 0; i <= steps; i++){\n                                                const t = i / steps;\n                                                const x = particle.x + (otherParticle.x - particle.x) * t;\n                                                const y = particle.y + (otherParticle.y - particle.y) * t;\n                                                // إضافة تموج ناعم\n                                                const waveOffset = Math.sin(t * Math.PI * 2 + time) * 5 * finalOpacity;\n                                                const perpX = -(otherParticle.y - particle.y) / distance;\n                                                const perpY = (otherParticle.x - particle.x) / distance;\n                                                const finalX = x + perpX * waveOffset;\n                                                const finalY = y + perpY * waveOffset;\n                                                if (i === 0) {\n                                                    ctx.moveTo(finalX, finalY);\n                                                } else {\n                                                    ctx.lineTo(finalX, finalY);\n                                                }\n                                            }\n                                            ctx.stroke();\n                                            // إضافة نقاط ضوئية على الخط\n                                            if (finalOpacity > 0.1) {\n                                                const midX = (particle.x + otherParticle.x) / 2;\n                                                const midY = (particle.y + otherParticle.y) / 2;\n                                                ctx.beginPath();\n                                                ctx.arc(midX, midY, Math.max(0.5, 2 * finalOpacity), 0, Math.PI * 2);\n                                                ctx.fillStyle = \"rgba(255, 255, 255, \".concat(Math.max(0.1, finalOpacity * 0.8), \")\");\n                                                ctx.fill();\n                                            }\n                                        }\n                                    }\n                                }\n                            }[\"SimpleHeroSection.useEffect.animate\"]);\n                        }\n                    }[\"SimpleHeroSection.useEffect.animate\"]);\n                    ctx.globalAlpha = 1;\n                    animationRef.current = requestAnimationFrame(animate);\n                }\n            }[\"SimpleHeroSection.useEffect.animate\"];\n            animate();\n            // التنظيف\n            return ({\n                \"SimpleHeroSection.useEffect\": ()=>{\n                    if (animationRef.current) {\n                        cancelAnimationFrame(animationRef.current);\n                    }\n                    window.removeEventListener('resize', resizeCanvas);\n                    window.removeEventListener('mousemove', handleMouseMove);\n                }\n            })[\"SimpleHeroSection.useEffect\"];\n        }\n    }[\"SimpleHeroSection.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-white via-purple-50 to-pink-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                ref: canvasRef,\n                className: \"absolute inset-0 w-full h-full\",\n                style: {\n                    zIndex: 1\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                lineNumber: 397,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-t from-white/30 via-transparent to-purple-50/20\",\n                style: {\n                    zIndex: 2\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                lineNumber: 404,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 text-center px-4 max-w-6xl mx-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-5xl md:text-7xl font-bold text-gray-900 mb-6 leading-tight\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"bg-gradient-to-r from-purple-600 via-pink-600 to-purple-600 bg-clip-text text-transparent\",\n                                    children: \"TechnoFlash\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                    lineNumber: 410,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 409,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl md:text-2xl text-gray-700 mb-8 max-w-3xl mx-auto leading-relaxed\",\n                                children: \"منصتك الشاملة لأحدث التقنيات وأدوات الذكاء الاصطناعي\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 414,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                        lineNumber: 408,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row gap-4 justify-center items-center mb-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/articles\",\n                                className: \"group bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-4 rounded-xl font-semibold text-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"استكشف المقالات\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 425,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5 group-hover:translate-x-1 transition-transform\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M13 7l5 5m0 0l-5 5m5-5H6\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                            lineNumber: 427,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 426,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 421,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/ai-tools\",\n                                className: \"group border-2 border-purple-400 text-purple-400 px-8 py-4 rounded-xl font-semibold text-lg hover:bg-purple-400 hover:text-white transition-all duration-300 flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"أدوات الذكاء الاصطناعي\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 435,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5 group-hover:rotate-12 transition-transform\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M13 10V3L4 14h7v7l9-11h-7z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                            lineNumber: 437,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 436,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 431,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                        lineNumber: 420,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-2\",\n                                        children: \"100+\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 445,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-600\",\n                                        children: \"مقال تقني\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 446,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 444,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-2\",\n                                        children: \"50+\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 449,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-600\",\n                                        children: \"أداة ذكاء اصطناعي\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 450,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 448,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-2\",\n                                        children: \"1000+\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 453,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-600\",\n                                        children: \"قارئ نشط\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 454,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 452,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                        lineNumber: 443,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                lineNumber: 407,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-8 left-1/2 transform -translate-x-1/2 z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-bounce\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-8 h-8 border-2 border-gray-400/60 rounded-full flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-4 h-4 text-gray-600/80\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M19 14l-7 7m0 0l-7-7m7 7V3\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 464,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                            lineNumber: 463,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                        lineNumber: 462,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                    lineNumber: 461,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                lineNumber: 460,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n        lineNumber: 395,\n        columnNumber: 5\n    }, this);\n}\n_s(SimpleHeroSection, \"/8IKySTmGZxFymijsBTHUGtuaQs=\");\n_c = SimpleHeroSection;\nvar _c;\n$RefreshReg$(_c, \"SimpleHeroSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/SimpleHeroSection.tsx\n"));

/***/ })

});