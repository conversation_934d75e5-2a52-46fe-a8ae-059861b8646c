/* تنسيق محتوى المقالات - خلفية فاتحة */

.article-content {
  /* الإعدادات الأساسية */
  padding: 20px;
  max-width: 700px;
  margin: 0 auto;
  font-family: 'Cairo', 'Tajawal', system-ui, -apple-system, sans-serif;
  line-height: 1.7;
  color: #1C1C1C;
  background-color: #FFFFFF;
}

/* النصوص الأساسية */
.article-content p {
  font-size: 1.1rem;
  line-height: 1.7;
  margin-bottom: 1.5rem;
  color: #1C1C1C;
}

/* العناوين */
.article-content h1 {
  font-size: 2rem;
  font-weight: 700;
  line-height: 1.4;
  color: #111111;
  margin-top: 2rem;
  margin-bottom: 1rem;
}

.article-content h2 {
  font-size: 1.5rem;
  font-weight: 700;
  line-height: 1.4;
  color: #111111;
  margin-top: 1.8rem;
  margin-bottom: 0.8rem;
}

.article-content h3 {
  font-size: 1.2rem;
  font-weight: 600;
  line-height: 1.4;
  color: #111111;
  margin-top: 1.5rem;
  margin-bottom: 0.6rem;
}

.article-content h4,
.article-content h5,
.article-content h6 {
  font-size: 1.1rem;
  font-weight: 600;
  line-height: 1.4;
  color: #111111;
  margin-top: 1.2rem;
  margin-bottom: 0.5rem;
}

/* الروابط */
.article-content a {
  color: #0070f3;
  text-decoration: underline;
  transition: color 0.2s ease;
}

.article-content a:hover {
  color: #0050c0;
  text-decoration: underline;
}

/* القوائم */
.article-content ul,
.article-content ol {
  margin: 1.5rem 0;
  padding-right: 1.5rem;
}

.article-content li {
  margin-bottom: 0.5rem;
  line-height: 1.6;
  color: #1C1C1C;
}

/* الاقتباسات */
.article-content blockquote {
  border-right: 4px solid #0070f3;
  padding: 1rem 1.5rem;
  margin: 2rem 0;
  background-color: #f8f9fa;
  font-style: italic;
  color: #2c3e50;
}

/* الكود */
.article-content code {
  background-color: #f1f3f4;
  padding: 0.2rem 0.4rem;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  color: #d73a49;
}

.article-content pre {
  background-color: #f6f8fa;
  padding: 1rem;
  border-radius: 8px;
  overflow-x: auto;
  margin: 1.5rem 0;
  border: 1px solid #e1e4e8;
}

.article-content pre code {
  background-color: transparent;
  padding: 0;
  color: #24292e;
}

/* الجداول */
.article-content table {
  width: 100%;
  border-collapse: collapse;
  margin: 2rem 0;
  border: 1px solid #e1e4e8;
}

.article-content th,
.article-content td {
  padding: 0.75rem;
  text-align: right;
  border-bottom: 1px solid #e1e4e8;
}

.article-content th {
  background-color: #f6f8fa;
  font-weight: 600;
  color: #24292e;
}

/* الصور */
.article-content img {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  margin: 1.5rem 0;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* خط فاصل */
.article-content hr {
  border: none;
  height: 2px;
  background-color: #e1e4e8;
  margin: 2rem 0;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
  .article-content {
    padding: 15px;
    font-size: 1rem;
  }
  
  .article-content h1 {
    font-size: 1.8rem;
  }
  
  .article-content h2 {
    font-size: 1.4rem;
  }
  
  .article-content h3 {
    font-size: 1.2rem;
  }
  
  .article-content p {
    font-size: 1rem;
  }
}

/* تحسينات للطباعة */
@media print {
  .article-content {
    color: #000;
    background: #fff;
  }
  
  .article-content a {
    color: #000;
    text-decoration: underline;
  }
}

/* تحسين التباين للوضوح */
.article-content strong,
.article-content b {
  font-weight: 700;
  color: #111111;
}

.article-content em,
.article-content i {
  font-style: italic;
  color: #2c3e50;
}

/* تحسين المسافات بين العناصر */
.article-content > *:first-child {
  margin-top: 0;
}

.article-content > *:last-child {
  margin-bottom: 0;
}
