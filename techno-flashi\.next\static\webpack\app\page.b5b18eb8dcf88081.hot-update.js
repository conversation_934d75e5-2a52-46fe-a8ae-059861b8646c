"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/SimpleHeroSection.tsx":
/*!**********************************************!*\
  !*** ./src/components/SimpleHeroSection.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SimpleHeroSection: () => (/* binding */ SimpleHeroSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ SimpleHeroSection auto */ \nvar _s = $RefreshSig$();\n\n\nfunction SimpleHeroSection() {\n    _s();\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const animationRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    const mouseRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        x: 0,\n        y: 0\n    });\n    const particlesRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SimpleHeroSection.useEffect\": ()=>{\n            const canvas = canvasRef.current;\n            if (!canvas) return;\n            const ctx = canvas.getContext('2d');\n            if (!ctx) return;\n            // إعداد الكانفاس\n            const resizeCanvas = {\n                \"SimpleHeroSection.useEffect.resizeCanvas\": ()=>{\n                    canvas.width = window.innerWidth;\n                    canvas.height = window.innerHeight;\n                }\n            }[\"SimpleHeroSection.useEffect.resizeCanvas\"];\n            resizeCanvas();\n            window.addEventListener('resize', resizeCanvas);\n            // إنشاء الجسيمات المحسنة للثيم الفاتح\n            const createParticles = {\n                \"SimpleHeroSection.useEffect.createParticles\": ()=>{\n                    const particles = [];\n                    const colors = [\n                        '#8b5cf6',\n                        '#a855f7',\n                        '#c084fc',\n                        '#d8b4fe',\n                        '#e879f9',\n                        '#f0abfc',\n                        '#fbbf24',\n                        '#f59e0b'\n                    ];\n                    for(let i = 0; i < 100; i++){\n                        particles.push({\n                            x: Math.random() * canvas.width,\n                            y: Math.random() * canvas.height,\n                            vx: (Math.random() - 0.5) * 1.2,\n                            vy: (Math.random() - 0.5) * 1.2,\n                            size: Math.random() * 5 + 2,\n                            color: colors[Math.floor(Math.random() * colors.length)],\n                            opacity: Math.random() * 0.4 + 0.1,\n                            pulse: Math.random() * Math.PI * 2,\n                            pulseSpeed: 0.015 + Math.random() * 0.025,\n                            rotationSpeed: (Math.random() - 0.5) * 0.02\n                        });\n                    }\n                    particlesRef.current = particles;\n                }\n            }[\"SimpleHeroSection.useEffect.createParticles\"];\n            createParticles();\n            // معالج حركة الماوس\n            const handleMouseMove = {\n                \"SimpleHeroSection.useEffect.handleMouseMove\": (e)=>{\n                    mouseRef.current = {\n                        x: e.clientX / window.innerWidth * 2 - 1,\n                        y: -(e.clientY / window.innerHeight) * 2 + 1\n                    };\n                }\n            }[\"SimpleHeroSection.useEffect.handleMouseMove\"];\n            window.addEventListener('mousemove', handleMouseMove);\n            // حلقة الرسم المحسنة\n            const animate = {\n                \"SimpleHeroSection.useEffect.animate\": ()=>{\n                    ctx.clearRect(0, 0, canvas.width, canvas.height);\n                    // رسم الشبكة المتحركة للثيم الفاتح\n                    const time = Date.now() * 0.001;\n                    const gridSize = 80;\n                    // شبكة ناعمة مع تأثير موجي\n                    ctx.strokeStyle = \"rgba(139, 92, 246, \".concat(0.03 + Math.sin(time) * 0.01, \")\");\n                    ctx.lineWidth = 0.5;\n                    for(let x = 0; x < canvas.width; x += gridSize){\n                        const offset = Math.sin(time + x * 0.008) * 8;\n                        ctx.beginPath();\n                        ctx.moveTo(x + offset, 0);\n                        ctx.lineTo(x + offset, canvas.height);\n                        ctx.stroke();\n                    }\n                    for(let y = 0; y < canvas.height; y += gridSize){\n                        const offset = Math.cos(time + y * 0.008) * 6;\n                        ctx.beginPath();\n                        ctx.moveTo(0, y + offset);\n                        ctx.lineTo(canvas.width, y + offset);\n                        ctx.stroke();\n                    }\n                    // رسم دوائر متوهجة ناعمة\n                    const glowCircles = 5;\n                    for(let i = 0; i < glowCircles; i++){\n                        const x = canvas.width * (0.1 + i * 0.2);\n                        const y = canvas.height * (0.2 + Math.sin(time * 0.3 + i) * 0.3);\n                        const radius = 150 + Math.sin(time * 0.4 + i) * 50;\n                        const gradient = ctx.createRadialGradient(x, y, 0, x, y, radius);\n                        const colors = [\n                            \"rgba(168, 85, 247, \".concat(0.05 + Math.sin(time + i) * 0.02, \")\"),\n                            \"rgba(236, 72, 153, \".concat(0.03 + Math.sin(time + i + 1) * 0.015, \")\"),\n                            \"rgba(251, 191, 36, \".concat(0.02 + Math.sin(time + i + 2) * 0.01, \")\")\n                        ];\n                        gradient.addColorStop(0, colors[i % colors.length]);\n                        gradient.addColorStop(0.7, colors[(i + 1) % colors.length].replace(/[\\d.]+\\)/, '0.01)'));\n                        gradient.addColorStop(1, 'rgba(255, 255, 255, 0)');\n                        ctx.fillStyle = gradient;\n                        ctx.beginPath();\n                        ctx.arc(x, y, radius, 0, Math.PI * 2);\n                        ctx.fill();\n                    }\n                    // رسم أشكال هندسية متحركة\n                    const shapes = 3;\n                    for(let i = 0; i < shapes; i++){\n                        const x = canvas.width * (0.3 + i * 0.2);\n                        const y = canvas.height * (0.4 + Math.cos(time * 0.2 + i) * 0.2);\n                        const size = 30 + Math.sin(time + i) * 10;\n                        const rotation = time * 0.5 + i;\n                        ctx.save();\n                        ctx.translate(x, y);\n                        ctx.rotate(rotation);\n                        const gradient = ctx.createLinearGradient(-size, -size, size, size);\n                        gradient.addColorStop(0, \"rgba(168, 85, 247, \".concat(0.1 + Math.sin(time + i) * 0.05, \")\"));\n                        gradient.addColorStop(1, \"rgba(236, 72, 153, \".concat(0.05 + Math.cos(time + i) * 0.03, \")\"));\n                        ctx.fillStyle = gradient;\n                        ctx.beginPath();\n                        ctx.roundRect(-size / 2, -size / 2, size, size, 8);\n                        ctx.fill();\n                        ctx.restore();\n                    }\n                    // تحديث ورسم الجسيمات المحسنة\n                    particlesRef.current.forEach({\n                        \"SimpleHeroSection.useEffect.animate\": (particle, index)=>{\n                            // تحديث الموضع\n                            particle.x += particle.vx;\n                            particle.y += particle.vy;\n                            // تحديث التأثير النابض\n                            particle.pulse += particle.pulseSpeed;\n                            // تأثير الماوس المحسن\n                            const mouseInfluence = 80;\n                            const dx = mouseRef.current.x * canvas.width * 0.5 + canvas.width * 0.5 - particle.x;\n                            const dy = mouseRef.current.y * canvas.height * 0.5 + canvas.height * 0.5 - particle.y;\n                            const distance = Math.sqrt(dx * dx + dy * dy);\n                            if (distance < mouseInfluence) {\n                                const force = (mouseInfluence - distance) / mouseInfluence;\n                                particle.vx += dx * force * 0.002;\n                                particle.vy += dy * force * 0.002;\n                            }\n                            // حدود الشاشة مع ارتداد ناعم\n                            if (particle.x < 0 || particle.x > canvas.width) {\n                                particle.vx *= -0.8;\n                                particle.x = Math.max(0, Math.min(canvas.width, particle.x));\n                            }\n                            if (particle.y < 0 || particle.y > canvas.height) {\n                                particle.vy *= -0.8;\n                                particle.y = Math.max(0, Math.min(canvas.height, particle.y));\n                            }\n                            // رسم الجسيمة مع تأثير متوهج\n                            const pulseSize = particle.size + Math.sin(particle.pulse) * 1;\n                            const pulseOpacity = particle.opacity + Math.sin(particle.pulse) * 0.2;\n                            // رسم الهالة\n                            const gradient = ctx.createRadialGradient(particle.x, particle.y, 0, particle.x, particle.y, pulseSize * 3);\n                            gradient.addColorStop(0, particle.color.replace(')', ', 0.3)').replace('rgb', 'rgba'));\n                            gradient.addColorStop(1, particle.color.replace(')', ', 0)').replace('rgb', 'rgba'));\n                            ctx.fillStyle = gradient;\n                            ctx.globalAlpha = pulseOpacity * 0.5;\n                            ctx.beginPath();\n                            ctx.arc(particle.x, particle.y, pulseSize * 3, 0, Math.PI * 2);\n                            ctx.fill();\n                            // رسم الجسيمة الأساسية\n                            ctx.beginPath();\n                            ctx.arc(particle.x, particle.y, pulseSize, 0, Math.PI * 2);\n                            ctx.fillStyle = particle.color;\n                            ctx.globalAlpha = pulseOpacity;\n                            ctx.fill();\n                            // رسم خطوط الاتصال المحسنة\n                            particlesRef.current.forEach({\n                                \"SimpleHeroSection.useEffect.animate\": (otherParticle, otherIndex)=>{\n                                    if (index !== otherIndex && index < otherIndex) {\n                                        const dx = particle.x - otherParticle.x;\n                                        const dy = particle.y - otherParticle.y;\n                                        const distance = Math.sqrt(dx * dx + dy * dy);\n                                        if (distance < 120) {\n                                            const opacity = 0.15 * (1 - distance / 120);\n                                            const gradient = ctx.createLinearGradient(particle.x, particle.y, otherParticle.x, otherParticle.y);\n                                            gradient.addColorStop(0, \"rgba(168, 85, 247, \".concat(opacity, \")\"));\n                                            gradient.addColorStop(0.5, \"rgba(192, 132, 252, \".concat(opacity * 1.5, \")\"));\n                                            gradient.addColorStop(1, \"rgba(168, 85, 247, \".concat(opacity, \")\"));\n                                            ctx.beginPath();\n                                            ctx.moveTo(particle.x, particle.y);\n                                            ctx.lineTo(otherParticle.x, otherParticle.y);\n                                            ctx.strokeStyle = gradient;\n                                            ctx.lineWidth = 1 + opacity * 2;\n                                            ctx.stroke();\n                                        }\n                                    }\n                                }\n                            }[\"SimpleHeroSection.useEffect.animate\"]);\n                        }\n                    }[\"SimpleHeroSection.useEffect.animate\"]);\n                    ctx.globalAlpha = 1;\n                    animationRef.current = requestAnimationFrame(animate);\n                }\n            }[\"SimpleHeroSection.useEffect.animate\"];\n            animate();\n            // التنظيف\n            return ({\n                \"SimpleHeroSection.useEffect\": ()=>{\n                    if (animationRef.current) {\n                        cancelAnimationFrame(animationRef.current);\n                    }\n                    window.removeEventListener('resize', resizeCanvas);\n                    window.removeEventListener('mousemove', handleMouseMove);\n                }\n            })[\"SimpleHeroSection.useEffect\"];\n        }\n    }[\"SimpleHeroSection.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-white via-purple-50 to-pink-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                ref: canvasRef,\n                className: \"absolute inset-0 w-full h-full\",\n                style: {\n                    zIndex: 1\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                lineNumber: 250,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-t from-white/30 via-transparent to-purple-50/20\",\n                style: {\n                    zIndex: 2\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                lineNumber: 257,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 text-center px-4 max-w-6xl mx-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-5xl md:text-7xl font-bold text-gray-900 mb-6 leading-tight\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"bg-gradient-to-r from-purple-600 via-pink-600 to-purple-600 bg-clip-text text-transparent\",\n                                    children: \"TechnoFlash\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                    lineNumber: 263,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 262,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl md:text-2xl text-gray-700 mb-8 max-w-3xl mx-auto leading-relaxed\",\n                                children: \"منصتك الشاملة لأحدث التقنيات وأدوات الذكاء الاصطناعي\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 267,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                        lineNumber: 261,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row gap-4 justify-center items-center mb-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/articles\",\n                                className: \"group bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-4 rounded-xl font-semibold text-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"استكشف المقالات\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5 group-hover:translate-x-1 transition-transform\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M13 7l5 5m0 0l-5 5m5-5H6\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 279,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 274,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/ai-tools\",\n                                className: \"group border-2 border-purple-400 text-purple-400 px-8 py-4 rounded-xl font-semibold text-lg hover:bg-purple-400 hover:text-white transition-all duration-300 flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"أدوات الذكاء الاصطناعي\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5 group-hover:rotate-12 transition-transform\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M13 10V3L4 14h7v7l9-11h-7z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                            lineNumber: 290,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 289,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 284,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                        lineNumber: 273,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-2\",\n                                        children: \"100+\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 298,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-600\",\n                                        children: \"مقال تقني\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 297,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-2\",\n                                        children: \"50+\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 302,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-600\",\n                                        children: \"أداة ذكاء اصطناعي\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 303,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 301,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-2\",\n                                        children: \"1000+\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 306,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-600\",\n                                        children: \"قارئ نشط\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 307,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 305,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                        lineNumber: 296,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                lineNumber: 260,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-8 left-1/2 transform -translate-x-1/2 z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-bounce\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-8 h-8 border-2 border-white/40 rounded-full flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-4 h-4 text-white/60\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M19 14l-7 7m0 0l-7-7m7 7V3\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 317,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                            lineNumber: 316,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                        lineNumber: 315,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                    lineNumber: 314,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                lineNumber: 313,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n        lineNumber: 248,\n        columnNumber: 5\n    }, this);\n}\n_s(SimpleHeroSection, \"/8IKySTmGZxFymijsBTHUGtuaQs=\");\n_c = SimpleHeroSection;\nvar _c;\n$RefreshReg$(_c, \"SimpleHeroSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/SimpleHeroSection.tsx\n"));

/***/ })

});