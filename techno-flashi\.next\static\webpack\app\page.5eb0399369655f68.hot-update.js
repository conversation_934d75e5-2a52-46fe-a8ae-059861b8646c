"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/HeroSection.tsx":
/*!****************************************!*\
  !*** ./src/components/HeroSection.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HeroSection: () => (/* binding */ HeroSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! three */ \"(app-pages-browser)/./node_modules/three/build/three.core.js\");\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! three */ \"(app-pages-browser)/./node_modules/three/build/three.module.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ HeroSection auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction HeroSection() {\n    _s();\n    const mountRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [isWebGLSupported, setIsWebGLSupported] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const sceneRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    const rendererRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    const animationRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    const mouseRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        x: 0,\n        y: 0\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HeroSection.useEffect\": ()=>{\n            if (!mountRef.current) return;\n            let scene;\n            let camera;\n            let renderer;\n            let particles;\n            let geometricShapes;\n            let ambientLight;\n            let pointLight;\n            try {\n                // إعداد المشهد\n                scene = new three__WEBPACK_IMPORTED_MODULE_3__.Scene();\n                sceneRef.current = scene;\n                // إعداد الكاميرا\n                camera = new three__WEBPACK_IMPORTED_MODULE_3__.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);\n                camera.position.z = 5;\n                // إعداد المُرسِل مع معالجة الأخطاء\n                renderer = new three__WEBPACK_IMPORTED_MODULE_4__.WebGLRenderer({\n                    antialias: true,\n                    alpha: true,\n                    powerPreference: \"high-performance\",\n                    failIfMajorPerformanceCaveat: false,\n                    preserveDrawingBuffer: false\n                });\n                // فحص دعم WebGL\n                const gl = renderer.getContext();\n                if (!gl) {\n                    throw new Error('WebGL not supported');\n                }\n                renderer.setSize(window.innerWidth, window.innerHeight);\n                renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));\n                renderer.setClearColor(0x000000, 0);\n                rendererRef.current = renderer;\n                mountRef.current.appendChild(renderer.domElement);\n                // إعداد الإضاءة\n                ambientLight = new three__WEBPACK_IMPORTED_MODULE_3__.AmbientLight(0x8b5cf6, 0.4);\n                scene.add(ambientLight);\n                pointLight = new three__WEBPACK_IMPORTED_MODULE_3__.PointLight(0xa855f7, 1, 100);\n                pointLight.position.set(10, 10, 10);\n                scene.add(pointLight);\n                // إنشاء جسيمات ثلاثية الأبعاد\n                const particleCount = 1000;\n                const positions = new Float32Array(particleCount * 3);\n                const colors = new Float32Array(particleCount * 3);\n                const sizes = new Float32Array(particleCount);\n                const colorPalette = [\n                    new three__WEBPACK_IMPORTED_MODULE_3__.Color(0x8b5cf6),\n                    new three__WEBPACK_IMPORTED_MODULE_3__.Color(0xa855f7),\n                    new three__WEBPACK_IMPORTED_MODULE_3__.Color(0xc084fc),\n                    new three__WEBPACK_IMPORTED_MODULE_3__.Color(0xd8b4fe)\n                ];\n                for(let i = 0; i < particleCount; i++){\n                    // المواقع العشوائية\n                    positions[i * 3] = (Math.random() - 0.5) * 20;\n                    positions[i * 3 + 1] = (Math.random() - 0.5) * 20;\n                    positions[i * 3 + 2] = (Math.random() - 0.5) * 20;\n                    // الألوان العشوائية\n                    const color = colorPalette[Math.floor(Math.random() * colorPalette.length)];\n                    colors[i * 3] = color.r;\n                    colors[i * 3 + 1] = color.g;\n                    colors[i * 3 + 2] = color.b;\n                    // الأحجام العشوائية\n                    sizes[i] = Math.random() * 3 + 1;\n                }\n                const particleGeometry = new three__WEBPACK_IMPORTED_MODULE_3__.BufferGeometry();\n                particleGeometry.setAttribute('position', new three__WEBPACK_IMPORTED_MODULE_3__.BufferAttribute(positions, 3));\n                particleGeometry.setAttribute('color', new three__WEBPACK_IMPORTED_MODULE_3__.BufferAttribute(colors, 3));\n                particleGeometry.setAttribute('size', new three__WEBPACK_IMPORTED_MODULE_3__.BufferAttribute(sizes, 1));\n                // إنشاء Shader Material مع معالجة الأخطاء\n                let particleMaterial;\n                try {\n                    particleMaterial = new three__WEBPACK_IMPORTED_MODULE_3__.ShaderMaterial({\n                        uniforms: {\n                            time: {\n                                value: 0\n                            },\n                            mouse: {\n                                value: new three__WEBPACK_IMPORTED_MODULE_3__.Vector2()\n                            }\n                        },\n                        vertexShader: \"\\n          attribute float size;\\n          attribute vec3 color;\\n          varying vec3 vColor;\\n          uniform float time;\\n          uniform vec2 mouse;\\n\\n          void main() {\\n            vColor = color;\\n            vec3 pos = position;\\n\\n            // تأثير الموجة\\n            pos.y += sin(pos.x * 0.5 + time) * 0.5;\\n            pos.x += cos(pos.z * 0.5 + time) * 0.3;\\n\\n            // تأثير الماوس\\n            vec2 mouseInfluence = mouse * 0.1;\\n            pos.xy += mouseInfluence * (1.0 - length(pos.xy) * 0.1);\\n\\n            vec4 mvPosition = modelViewMatrix * vec4(pos, 1.0);\\n            gl_PointSize = size * (300.0 / max(-mvPosition.z, 1.0));\\n            gl_Position = projectionMatrix * mvPosition;\\n          }\\n        \",\n                        fragmentShader: \"\\n          varying vec3 vColor;\\n\\n          void main() {\\n            float distanceToCenter = distance(gl_PointCoord, vec2(0.5));\\n            float alpha = 1.0 - smoothstep(0.0, 0.5, distanceToCenter);\\n            gl_FragColor = vec4(vColor, alpha * 0.8);\\n          }\\n        \",\n                        transparent: true,\n                        vertexColors: true,\n                        blending: three__WEBPACK_IMPORTED_MODULE_3__.AdditiveBlending\n                    });\n                    // فحص إذا كان الـ shader تم تجميعه بنجاح\n                    if (!particleMaterial.vertexShader || !particleMaterial.fragmentShader) {\n                        throw new Error('Shader compilation failed');\n                    }\n                } catch (shaderError) {\n                    console.warn('Shader creation failed, using basic material:', shaderError);\n                    // استخدام مادة أساسية كبديل\n                    particleMaterial = new three__WEBPACK_IMPORTED_MODULE_3__.PointsMaterial({\n                        size: 2,\n                        vertexColors: true,\n                        transparent: true,\n                        opacity: 0.8,\n                        blending: three__WEBPACK_IMPORTED_MODULE_3__.AdditiveBlending\n                    });\n                }\n                particles = new three__WEBPACK_IMPORTED_MODULE_3__.Points(particleGeometry, particleMaterial);\n                scene.add(particles);\n                // إنشاء أشكال هندسية معقدة\n                geometricShapes = new three__WEBPACK_IMPORTED_MODULE_3__.Group();\n                // مكعبات متحركة\n                for(let i = 0; i < 5; i++){\n                    const cubeGeometry = new three__WEBPACK_IMPORTED_MODULE_3__.BoxGeometry(0.5, 0.5, 0.5);\n                    const cubeMaterial = new three__WEBPACK_IMPORTED_MODULE_3__.MeshPhongMaterial({\n                        color: colorPalette[i % colorPalette.length],\n                        transparent: true,\n                        opacity: 0.3,\n                        wireframe: true\n                    });\n                    const cube = new three__WEBPACK_IMPORTED_MODULE_3__.Mesh(cubeGeometry, cubeMaterial);\n                    cube.position.set((Math.random() - 0.5) * 10, (Math.random() - 0.5) * 10, (Math.random() - 0.5) * 10);\n                    geometricShapes.add(cube);\n                }\n                // كرات متوهجة\n                for(let i = 0; i < 3; i++){\n                    const sphereGeometry = new three__WEBPACK_IMPORTED_MODULE_3__.SphereGeometry(0.3, 16, 16);\n                    const sphereMaterial = new three__WEBPACK_IMPORTED_MODULE_3__.MeshPhongMaterial({\n                        color: colorPalette[(i + 2) % colorPalette.length],\n                        transparent: true,\n                        opacity: 0.6,\n                        emissive: colorPalette[(i + 2) % colorPalette.length],\n                        emissiveIntensity: 0.2\n                    });\n                    const sphere = new three__WEBPACK_IMPORTED_MODULE_3__.Mesh(sphereGeometry, sphereMaterial);\n                    sphere.position.set((Math.random() - 0.5) * 8, (Math.random() - 0.5) * 8, (Math.random() - 0.5) * 8);\n                    geometricShapes.add(sphere);\n                }\n                scene.add(geometricShapes);\n                // حلقة الرسم والتحريك\n                const clock = new three__WEBPACK_IMPORTED_MODULE_3__.Clock();\n                const animate = {\n                    \"HeroSection.useEffect.animate\": ()=>{\n                        const elapsedTime = clock.getElapsedTime();\n                        // تحريك الجسيمات مع فحص النوع\n                        if (particles && particles.material) {\n                            if (particles.material instanceof three__WEBPACK_IMPORTED_MODULE_3__.ShaderMaterial && particles.material.uniforms) {\n                                particles.material.uniforms.time.value = elapsedTime;\n                                particles.material.uniforms.mouse.value.set(mouseRef.current.x, mouseRef.current.y);\n                            }\n                        }\n                        // تدوير الجسيمات\n                        if (particles) {\n                            particles.rotation.y = elapsedTime * 0.05;\n                            particles.rotation.x = Math.sin(elapsedTime * 0.03) * 0.1;\n                        }\n                        // تحريك الأشكال الهندسية\n                        geometricShapes.children.forEach({\n                            \"HeroSection.useEffect.animate\": (shape, index)=>{\n                                if (shape instanceof three__WEBPACK_IMPORTED_MODULE_3__.Mesh) {\n                                    // دوران مختلف لكل شكل\n                                    shape.rotation.x += 0.01 * (index + 1);\n                                    shape.rotation.y += 0.015 * (index + 1);\n                                    // حركة تموجية\n                                    shape.position.y += Math.sin(elapsedTime + index) * 0.002;\n                                    shape.position.x += Math.cos(elapsedTime * 0.5 + index) * 0.001;\n                                }\n                            }\n                        }[\"HeroSection.useEffect.animate\"]);\n                        // تحريك الإضاءة\n                        pointLight.position.x = Math.sin(elapsedTime) * 5;\n                        pointLight.position.z = Math.cos(elapsedTime) * 5;\n                        pointLight.intensity = 0.8 + Math.sin(elapsedTime * 2) * 0.2;\n                        // تأثير الماوس على الكاميرا\n                        camera.position.x += (mouseRef.current.x * 0.001 - camera.position.x) * 0.05;\n                        camera.position.y += (-mouseRef.current.y * 0.001 - camera.position.y) * 0.05;\n                        camera.lookAt(scene.position);\n                        renderer.render(scene, camera);\n                        animationRef.current = requestAnimationFrame(animate);\n                    }\n                }[\"HeroSection.useEffect.animate\"];\n                animate();\n                // معالج حركة الماوس\n                const handleMouseMove = {\n                    \"HeroSection.useEffect.handleMouseMove\": (event)=>{\n                        mouseRef.current.x = event.clientX / window.innerWidth * 2 - 1;\n                        mouseRef.current.y = -(event.clientY / window.innerHeight) * 2 + 1;\n                    }\n                }[\"HeroSection.useEffect.handleMouseMove\"];\n                // معالج تغيير حجم النافذة\n                const handleResize = {\n                    \"HeroSection.useEffect.handleResize\": ()=>{\n                        camera.aspect = window.innerWidth / window.innerHeight;\n                        camera.updateProjectionMatrix();\n                        renderer.setSize(window.innerWidth, window.innerHeight);\n                        renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));\n                    }\n                }[\"HeroSection.useEffect.handleResize\"];\n                // إضافة المستمعين\n                window.addEventListener('mousemove', handleMouseMove);\n                window.addEventListener('resize', handleResize);\n                // التنظيف\n                return ({\n                    \"HeroSection.useEffect\": ()=>{\n                        if (animationRef.current) {\n                            cancelAnimationFrame(animationRef.current);\n                        }\n                        window.removeEventListener('mousemove', handleMouseMove);\n                        window.removeEventListener('resize', handleResize);\n                        // تنظيف Three.js\n                        if (mountRef.current && renderer.domElement) {\n                            mountRef.current.removeChild(renderer.domElement);\n                        }\n                        // تنظيف الذاكرة بأمان\n                        if (scene) {\n                            scene.clear();\n                        }\n                        if (renderer) {\n                            renderer.dispose();\n                        }\n                        // تنظيف الهندسة والمواد بأمان\n                        if (particles) {\n                            if (particles.geometry) {\n                                particles.geometry.dispose();\n                            }\n                            if (particles.material) {\n                                particles.material.dispose();\n                            }\n                        }\n                        if (geometricShapes) {\n                            geometricShapes.children.forEach({\n                                \"HeroSection.useEffect\": (child)=>{\n                                    if (child instanceof three__WEBPACK_IMPORTED_MODULE_3__.Mesh) {\n                                        if (child.geometry) {\n                                            child.geometry.dispose();\n                                        }\n                                        if (child.material) {\n                                            child.material.dispose();\n                                        }\n                                    }\n                                }\n                            }[\"HeroSection.useEffect\"]);\n                        }\n                    }\n                })[\"HeroSection.useEffect\"];\n            } catch (error) {\n                console.warn('WebGL/Three.js initialization failed:', error);\n                setIsWebGLSupported(false);\n            }\n        }\n    }[\"HeroSection.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900\",\n        children: [\n            isWebGLSupported && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: mountRef,\n                className: \"absolute inset-0 w-full h-full\",\n                style: {\n                    zIndex: 1\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                lineNumber: 344,\n                columnNumber: 9\n            }, this),\n            !isWebGLSupported && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-purple-600/20 via-pink-600/20 to-purple-600/20\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                        lineNumber: 354,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(139,92,246,0.1),transparent_70%)]\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                        lineNumber: 355,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-[conic-gradient(from_0deg_at_50%_50%,rgba(168,85,247,0.1),rgba(192,132,252,0.1),rgba(168,85,247,0.1))]\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                        lineNumber: 356,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                lineNumber: 353,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-t from-slate-900/50 via-transparent to-slate-900/30\",\n                style: {\n                    zIndex: 2\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                lineNumber: 361,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 container mx-auto px-6 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"inline-flex items-center px-6 py-3 bg-white/10 backdrop-blur-md text-purple-200 text-sm font-semibold rounded-full mb-8 border border-purple-300/30 shadow-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"mr-2 text-lg\",\n                                children: \"✨\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                                lineNumber: 367,\n                                columnNumber: 11\n                            }, this),\n                            \"بوابتك للمستقبل التقني\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                        lineNumber: 366,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-5xl md:text-7xl lg:text-8xl font-bold text-white leading-tight mb-6\",\n                        children: [\n                            \"مستقبلك التقني\",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                                lineNumber: 374,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-transparent bg-clip-text bg-gradient-to-r from-purple-400 via-pink-400 to-purple-600 animate-pulse\",\n                                children: \"يبدأ من هنا\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                                lineNumber: 375,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                        lineNumber: 372,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"max-w-4xl mx-auto text-xl md:text-2xl text-gray-300 mb-12 leading-relaxed font-light\",\n                        children: \"اكتشف أحدث التقنيات، أدوات الذكاء الاصطناعي المتطورة، ومقالات تقنية متخصصة لتطوير مهاراتك وتحقيق أهدافك في عالم التكنولوجيا المتطور.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                        lineNumber: 381,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row justify-center items-center gap-6 mb-20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/articles\",\n                                className: \"group relative bg-gradient-to-r from-purple-600 to-pink-600 text-white px-10 py-5 rounded-2xl shadow-2xl hover:shadow-purple-500/25 transition-all duration-500 transform hover:scale-105 font-semibold text-lg min-w-[220px] overflow-hidden\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-gradient-to-r from-purple-700 to-pink-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                                        lineNumber: 392,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"relative flex items-center justify-center\",\n                                        children: [\n                                            \"ابدأ الاستكشاف\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-6 h-6 mr-3 group-hover:translate-x-1 transition-transform duration-300\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M15 19l-7-7 7-7\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                                                    lineNumber: 396,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                                                lineNumber: 395,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                                        lineNumber: 393,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                                lineNumber: 388,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/ai-tools\",\n                                className: \"group relative bg-white/10 backdrop-blur-md text-white px-10 py-5 rounded-2xl border border-white/30 hover:bg-white/20 hover:border-purple-400/50 transition-all duration-500 font-semibold text-lg min-w-[220px] shadow-xl\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"flex items-center justify-center\",\n                                    children: [\n                                        \"أدوات الذكاء الاصطناعي\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-6 h-6 mr-3 group-hover:translate-x-1 transition-transform duration-300\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M13 10V3L4 14h7v7l9-11h-7z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                                                lineNumber: 408,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                                            lineNumber: 407,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                                    lineNumber: 405,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                                lineNumber: 401,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                        lineNumber: 387,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-4 gap-8 max-w-5xl mx-auto\",\n                        children: [\n                            {\n                                number: \"500+\",\n                                label: \"مقال تقني\"\n                            },\n                            {\n                                number: \"50+\",\n                                label: \"أداة ذكية\"\n                            },\n                            {\n                                number: \"10K+\",\n                                label: \"قارئ نشط\"\n                            },\n                            {\n                                number: \"24/7\",\n                                label: \"دعم فني\"\n                            }\n                        ].map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center group\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-4xl md:text-5xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-pink-400 mb-3 group-hover:scale-110 transition-transform duration-300\",\n                                        children: stat.number\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                                        lineNumber: 423,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm md:text-base font-medium\",\n                                        children: stat.label\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                                        lineNumber: 426,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                                lineNumber: 422,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                        lineNumber: 415,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                lineNumber: 364,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-8 left-1/2 transform -translate-x-1/2 z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-bounce\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-8 h-8 border-2 border-white/40 rounded-full flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-4 h-4 text-white/60\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M19 14l-7 7m0 0l-7-7m7 7V3\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                                lineNumber: 439,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                            lineNumber: 438,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                        lineNumber: 437,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                    lineNumber: 436,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                lineNumber: 435,\n                columnNumber: 7\n            }, this),\n            isWebGLSupported && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-4 right-4 z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-green-500/20 text-green-400 px-3 py-1 rounded-full text-xs font-medium border border-green-500/30\",\n                    children: \"✓ WebGL Active\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                    lineNumber: 448,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                lineNumber: 447,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n        lineNumber: 341,\n        columnNumber: 5\n    }, this);\n}\n_s(HeroSection, \"kYS9FaXFF20tBPBsKpAJ4sL0XtE=\");\n_c = HeroSection;\nvar _c;\n$RefreshReg$(_c, \"HeroSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL0hlcm9TZWN0aW9uLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBRW9EO0FBQ3JCO0FBQ0Y7QUFFdEIsU0FBU0s7O0lBQ2QsTUFBTUMsV0FBV0wsNkNBQU1BLENBQWlCO0lBQ3hDLE1BQU0sQ0FBQ00sa0JBQWtCQyxvQkFBb0IsR0FBR04sK0NBQVFBLENBQUM7SUFDekQsTUFBTU8sV0FBV1IsNkNBQU1BO0lBQ3ZCLE1BQU1TLGNBQWNULDZDQUFNQTtJQUMxQixNQUFNVSxlQUFlViw2Q0FBTUE7SUFDM0IsTUFBTVcsV0FBV1gsNkNBQU1BLENBQUM7UUFBRVksR0FBRztRQUFHQyxHQUFHO0lBQUU7SUFFckNkLGdEQUFTQTtpQ0FBQztZQUNSLElBQUksQ0FBQ00sU0FBU1MsT0FBTyxFQUFFO1lBRXZCLElBQUlDO1lBQ0osSUFBSUM7WUFDSixJQUFJQztZQUNKLElBQUlDO1lBQ0osSUFBSUM7WUFDSixJQUFJQztZQUNKLElBQUlDO1lBRUosSUFBSTtnQkFDRixlQUFlO2dCQUNmTixRQUFRLElBQUliLHdDQUFXO2dCQUN2Qk0sU0FBU00sT0FBTyxHQUFHQztnQkFFbkIsaUJBQWlCO2dCQUNqQkMsU0FBUyxJQUFJZCxvREFBdUIsQ0FDbEMsSUFDQXNCLE9BQU9DLFVBQVUsR0FBR0QsT0FBT0UsV0FBVyxFQUN0QyxLQUNBO2dCQUVGVixPQUFPVyxRQUFRLENBQUNDLENBQUMsR0FBRztnQkFFcEIsbUNBQW1DO2dCQUNuQ1gsV0FBVyxJQUFJZixnREFBbUIsQ0FBQztvQkFDakM0QixXQUFXO29CQUNYQyxPQUFPO29CQUNQQyxpQkFBaUI7b0JBQ2pCQyw4QkFBOEI7b0JBQzlCQyx1QkFBdUI7Z0JBQ3pCO2dCQUVBLGdCQUFnQjtnQkFDaEIsTUFBTUMsS0FBS2xCLFNBQVNtQixVQUFVO2dCQUM5QixJQUFJLENBQUNELElBQUk7b0JBQ1AsTUFBTSxJQUFJRSxNQUFNO2dCQUNsQjtnQkFFQXBCLFNBQVNxQixPQUFPLENBQUNkLE9BQU9DLFVBQVUsRUFBRUQsT0FBT0UsV0FBVztnQkFDdERULFNBQVNzQixhQUFhLENBQUNDLEtBQUtDLEdBQUcsQ0FBQ2pCLE9BQU9rQixnQkFBZ0IsRUFBRTtnQkFDekR6QixTQUFTMEIsYUFBYSxDQUFDLFVBQVU7Z0JBQ2pDbEMsWUFBWUssT0FBTyxHQUFHRztnQkFFdEJaLFNBQVNTLE9BQU8sQ0FBQzhCLFdBQVcsQ0FBQzNCLFNBQVM0QixVQUFVO2dCQUVoRCxnQkFBZ0I7Z0JBQ2hCekIsZUFBZSxJQUFJbEIsK0NBQWtCLENBQUMsVUFBVTtnQkFDaERhLE1BQU1nQyxHQUFHLENBQUMzQjtnQkFFVkMsYUFBYSxJQUFJbkIsNkNBQWdCLENBQUMsVUFBVSxHQUFHO2dCQUMvQ21CLFdBQVdNLFFBQVEsQ0FBQ3NCLEdBQUcsQ0FBQyxJQUFJLElBQUk7Z0JBQ2hDbEMsTUFBTWdDLEdBQUcsQ0FBQzFCO2dCQUVWLDhCQUE4QjtnQkFDOUIsTUFBTTZCLGdCQUFnQjtnQkFDdEIsTUFBTUMsWUFBWSxJQUFJQyxhQUFhRixnQkFBZ0I7Z0JBQ25ELE1BQU1HLFNBQVMsSUFBSUQsYUFBYUYsZ0JBQWdCO2dCQUNoRCxNQUFNSSxRQUFRLElBQUlGLGFBQWFGO2dCQUUvQixNQUFNSyxlQUFlO29CQUNuQixJQUFJckQsd0NBQVcsQ0FBQztvQkFDaEIsSUFBSUEsd0NBQVcsQ0FBQztvQkFDaEIsSUFBSUEsd0NBQVcsQ0FBQztvQkFDaEIsSUFBSUEsd0NBQVcsQ0FBQztpQkFDakI7Z0JBRUQsSUFBSyxJQUFJdUQsSUFBSSxHQUFHQSxJQUFJUCxlQUFlTyxJQUFLO29CQUN0QyxvQkFBb0I7b0JBQ3BCTixTQUFTLENBQUNNLElBQUksRUFBRSxHQUFHLENBQUNqQixLQUFLa0IsTUFBTSxLQUFLLEdBQUUsSUFBSztvQkFDM0NQLFNBQVMsQ0FBQ00sSUFBSSxJQUFJLEVBQUUsR0FBRyxDQUFDakIsS0FBS2tCLE1BQU0sS0FBSyxHQUFFLElBQUs7b0JBQy9DUCxTQUFTLENBQUNNLElBQUksSUFBSSxFQUFFLEdBQUcsQ0FBQ2pCLEtBQUtrQixNQUFNLEtBQUssR0FBRSxJQUFLO29CQUUvQyxvQkFBb0I7b0JBQ3BCLE1BQU1DLFFBQVFKLFlBQVksQ0FBQ2YsS0FBS29CLEtBQUssQ0FBQ3BCLEtBQUtrQixNQUFNLEtBQUtILGFBQWFNLE1BQU0sRUFBRTtvQkFDM0VSLE1BQU0sQ0FBQ0ksSUFBSSxFQUFFLEdBQUdFLE1BQU1HLENBQUM7b0JBQ3ZCVCxNQUFNLENBQUNJLElBQUksSUFBSSxFQUFFLEdBQUdFLE1BQU1JLENBQUM7b0JBQzNCVixNQUFNLENBQUNJLElBQUksSUFBSSxFQUFFLEdBQUdFLE1BQU1LLENBQUM7b0JBRTNCLG9CQUFvQjtvQkFDcEJWLEtBQUssQ0FBQ0csRUFBRSxHQUFHakIsS0FBS2tCLE1BQU0sS0FBSyxJQUFJO2dCQUNqQztnQkFFQSxNQUFNTyxtQkFBbUIsSUFBSS9ELGlEQUFvQjtnQkFDakQrRCxpQkFBaUJFLFlBQVksQ0FBQyxZQUFZLElBQUlqRSxrREFBcUIsQ0FBQ2lELFdBQVc7Z0JBQy9FYyxpQkFBaUJFLFlBQVksQ0FBQyxTQUFTLElBQUlqRSxrREFBcUIsQ0FBQ21ELFFBQVE7Z0JBQ3pFWSxpQkFBaUJFLFlBQVksQ0FBQyxRQUFRLElBQUlqRSxrREFBcUIsQ0FBQ29ELE9BQU87Z0JBRXZFLDBDQUEwQztnQkFDMUMsSUFBSWU7Z0JBRUosSUFBSTtvQkFDRkEsbUJBQW1CLElBQUluRSxpREFBb0IsQ0FBQzt3QkFDMUNxRSxVQUFVOzRCQUNSQyxNQUFNO2dDQUFFQyxPQUFPOzRCQUFFOzRCQUNqQkMsT0FBTztnQ0FBRUQsT0FBTyxJQUFJdkUsMENBQWE7NEJBQUc7d0JBQ3RDO3dCQUNGMEUsY0FBZTt3QkF3QmZDLGdCQUFpQjt3QkFTZkMsYUFBYTt3QkFDYkMsY0FBYzt3QkFDZEMsVUFBVTlFLG1EQUFzQjtvQkFDbEM7b0JBRUEseUNBQXlDO29CQUN6QyxJQUFJLENBQUNtRSxpQkFBaUJPLFlBQVksSUFBSSxDQUFDUCxpQkFBaUJRLGNBQWMsRUFBRTt3QkFDdEUsTUFBTSxJQUFJeEMsTUFBTTtvQkFDbEI7Z0JBRUYsRUFBRSxPQUFPNkMsYUFBYTtvQkFDcEJDLFFBQVFDLElBQUksQ0FBQyxpREFBaURGO29CQUU5RCw0QkFBNEI7b0JBQzVCYixtQkFBbUIsSUFBSW5FLGlEQUFvQixDQUFDO3dCQUMxQ29GLE1BQU07d0JBQ05QLGNBQWM7d0JBQ2RELGFBQWE7d0JBQ2JTLFNBQVM7d0JBQ1RQLFVBQVU5RSxtREFBc0I7b0JBQ2xDO2dCQUNGO2dCQUVBZ0IsWUFBWSxJQUFJaEIseUNBQVksQ0FBQytELGtCQUFrQkk7Z0JBQy9DdEQsTUFBTWdDLEdBQUcsQ0FBQzdCO2dCQUVWLDJCQUEyQjtnQkFDM0JDLGtCQUFrQixJQUFJakIsd0NBQVc7Z0JBRWpDLGdCQUFnQjtnQkFDaEIsSUFBSyxJQUFJdUQsSUFBSSxHQUFHQSxJQUFJLEdBQUdBLElBQUs7b0JBQzFCLE1BQU1pQyxlQUFlLElBQUl4Riw4Q0FBaUIsQ0FBQyxLQUFLLEtBQUs7b0JBQ3JELE1BQU0wRixlQUFlLElBQUkxRixvREFBdUIsQ0FBQzt3QkFDL0N5RCxPQUFPSixZQUFZLENBQUNFLElBQUlGLGFBQWFNLE1BQU0sQ0FBQzt3QkFDNUNpQixhQUFhO3dCQUNiUyxTQUFTO3dCQUNUTyxXQUFXO29CQUNiO29CQUNBLE1BQU1DLE9BQU8sSUFBSTdGLHVDQUFVLENBQUN3RixjQUFjRTtvQkFFMUNHLEtBQUtwRSxRQUFRLENBQUNzQixHQUFHLENBQ2YsQ0FBQ1QsS0FBS2tCLE1BQU0sS0FBSyxHQUFFLElBQUssSUFDeEIsQ0FBQ2xCLEtBQUtrQixNQUFNLEtBQUssR0FBRSxJQUFLLElBQ3hCLENBQUNsQixLQUFLa0IsTUFBTSxLQUFLLEdBQUUsSUFBSztvQkFHMUJ2QyxnQkFBZ0I0QixHQUFHLENBQUNnRDtnQkFDdEI7Z0JBRUEsY0FBYztnQkFDZCxJQUFLLElBQUl0QyxJQUFJLEdBQUdBLElBQUksR0FBR0EsSUFBSztvQkFDMUIsTUFBTXdDLGlCQUFpQixJQUFJL0YsaURBQW9CLENBQUMsS0FBSyxJQUFJO29CQUN6RCxNQUFNaUcsaUJBQWlCLElBQUlqRyxvREFBdUIsQ0FBQzt3QkFDakR5RCxPQUFPSixZQUFZLENBQUMsQ0FBQ0UsSUFBSSxLQUFLRixhQUFhTSxNQUFNLENBQUM7d0JBQ2xEaUIsYUFBYTt3QkFDYlMsU0FBUzt3QkFDVGEsVUFBVTdDLFlBQVksQ0FBQyxDQUFDRSxJQUFJLEtBQUtGLGFBQWFNLE1BQU0sQ0FBQzt3QkFDckR3QyxtQkFBbUI7b0JBQ3JCO29CQUNBLE1BQU1DLFNBQVMsSUFBSXBHLHVDQUFVLENBQUMrRixnQkFBZ0JFO29CQUU5Q0csT0FBTzNFLFFBQVEsQ0FBQ3NCLEdBQUcsQ0FDakIsQ0FBQ1QsS0FBS2tCLE1BQU0sS0FBSyxHQUFFLElBQUssR0FDeEIsQ0FBQ2xCLEtBQUtrQixNQUFNLEtBQUssR0FBRSxJQUFLLEdBQ3hCLENBQUNsQixLQUFLa0IsTUFBTSxLQUFLLEdBQUUsSUFBSztvQkFHMUJ2QyxnQkFBZ0I0QixHQUFHLENBQUN1RDtnQkFDdEI7Z0JBRUF2RixNQUFNZ0MsR0FBRyxDQUFDNUI7Z0JBRVYsc0JBQXNCO2dCQUN0QixNQUFNb0YsUUFBUSxJQUFJckcsd0NBQVc7Z0JBRTdCLE1BQU11RztxREFBVTt3QkFDZCxNQUFNQyxjQUFjSCxNQUFNSSxjQUFjO3dCQUV4Qyw4QkFBOEI7d0JBQzlCLElBQUl6RixhQUFhQSxVQUFVMEYsUUFBUSxFQUFFOzRCQUNuQyxJQUFJMUYsVUFBVTBGLFFBQVEsWUFBWTFHLGlEQUFvQixJQUFJZ0IsVUFBVTBGLFFBQVEsQ0FBQ3JDLFFBQVEsRUFBRTtnQ0FDckZyRCxVQUFVMEYsUUFBUSxDQUFDckMsUUFBUSxDQUFDQyxJQUFJLENBQUNDLEtBQUssR0FBR2lDO2dDQUN6Q3hGLFVBQVUwRixRQUFRLENBQUNyQyxRQUFRLENBQUNHLEtBQUssQ0FBQ0QsS0FBSyxDQUFDeEIsR0FBRyxDQUN6Q3RDLFNBQVNHLE9BQU8sQ0FBQ0YsQ0FBQyxFQUNsQkQsU0FBU0csT0FBTyxDQUFDRCxDQUFDOzRCQUV0Qjt3QkFDRjt3QkFFQSxpQkFBaUI7d0JBQ2pCLElBQUlLLFdBQVc7NEJBQ2JBLFVBQVUyRixRQUFRLENBQUNoRyxDQUFDLEdBQUc2RixjQUFjOzRCQUNyQ3hGLFVBQVUyRixRQUFRLENBQUNqRyxDQUFDLEdBQUc0QixLQUFLc0UsR0FBRyxDQUFDSixjQUFjLFFBQVE7d0JBQ3hEO3dCQUVBLHlCQUF5Qjt3QkFDekJ2RixnQkFBZ0I0RixRQUFRLENBQUNDLE9BQU87NkRBQUMsQ0FBQ0MsT0FBT0M7Z0NBQ3ZDLElBQUlELGlCQUFpQi9HLHVDQUFVLEVBQUU7b0NBQy9CLHNCQUFzQjtvQ0FDdEIrRyxNQUFNSixRQUFRLENBQUNqRyxDQUFDLElBQUksT0FBUXNHLENBQUFBLFFBQVE7b0NBQ3BDRCxNQUFNSixRQUFRLENBQUNoRyxDQUFDLElBQUksUUFBU3FHLENBQUFBLFFBQVE7b0NBRXJDLGNBQWM7b0NBQ2RELE1BQU10RixRQUFRLENBQUNkLENBQUMsSUFBSTJCLEtBQUtzRSxHQUFHLENBQUNKLGNBQWNRLFNBQVM7b0NBQ3BERCxNQUFNdEYsUUFBUSxDQUFDZixDQUFDLElBQUk0QixLQUFLMkUsR0FBRyxDQUFDVCxjQUFjLE1BQU1RLFNBQVM7Z0NBQzVEOzRCQUNGOzt3QkFFQSxnQkFBZ0I7d0JBQ2hCN0YsV0FBV00sUUFBUSxDQUFDZixDQUFDLEdBQUc0QixLQUFLc0UsR0FBRyxDQUFDSixlQUFlO3dCQUNoRHJGLFdBQVdNLFFBQVEsQ0FBQ0MsQ0FBQyxHQUFHWSxLQUFLMkUsR0FBRyxDQUFDVCxlQUFlO3dCQUNoRHJGLFdBQVcrRixTQUFTLEdBQUcsTUFBTTVFLEtBQUtzRSxHQUFHLENBQUNKLGNBQWMsS0FBSzt3QkFFekQsNEJBQTRCO3dCQUM1QjFGLE9BQU9XLFFBQVEsQ0FBQ2YsQ0FBQyxJQUFJLENBQUNELFNBQVNHLE9BQU8sQ0FBQ0YsQ0FBQyxHQUFHLFFBQVFJLE9BQU9XLFFBQVEsQ0FBQ2YsQ0FBQyxJQUFJO3dCQUN4RUksT0FBT1csUUFBUSxDQUFDZCxDQUFDLElBQUksQ0FBQyxDQUFDRixTQUFTRyxPQUFPLENBQUNELENBQUMsR0FBRyxRQUFRRyxPQUFPVyxRQUFRLENBQUNkLENBQUMsSUFBSTt3QkFDekVHLE9BQU9xRyxNQUFNLENBQUN0RyxNQUFNWSxRQUFRO3dCQUU1QlYsU0FBU3FHLE1BQU0sQ0FBQ3ZHLE9BQU9DO3dCQUN2Qk4sYUFBYUksT0FBTyxHQUFHeUcsc0JBQXNCZDtvQkFDL0M7O2dCQUVBQTtnQkFFQSxvQkFBb0I7Z0JBQ3BCLE1BQU1lOzZEQUFrQixDQUFDQzt3QkFDdkI5RyxTQUFTRyxPQUFPLENBQUNGLENBQUMsR0FBRyxNQUFPOEcsT0FBTyxHQUFHbEcsT0FBT0MsVUFBVSxHQUFJLElBQUk7d0JBQy9EZCxTQUFTRyxPQUFPLENBQUNELENBQUMsR0FBRyxDQUFFNEcsQ0FBQUEsTUFBTUUsT0FBTyxHQUFHbkcsT0FBT0UsV0FBVyxJQUFJLElBQUk7b0JBQ25FOztnQkFFQSwwQkFBMEI7Z0JBQzFCLE1BQU1rRzswREFBZTt3QkFDbkI1RyxPQUFPNkcsTUFBTSxHQUFHckcsT0FBT0MsVUFBVSxHQUFHRCxPQUFPRSxXQUFXO3dCQUN0RFYsT0FBTzhHLHNCQUFzQjt3QkFDN0I3RyxTQUFTcUIsT0FBTyxDQUFDZCxPQUFPQyxVQUFVLEVBQUVELE9BQU9FLFdBQVc7d0JBQ3REVCxTQUFTc0IsYUFBYSxDQUFDQyxLQUFLQyxHQUFHLENBQUNqQixPQUFPa0IsZ0JBQWdCLEVBQUU7b0JBQzNEOztnQkFFQSxrQkFBa0I7Z0JBQ2xCbEIsT0FBT3VHLGdCQUFnQixDQUFDLGFBQWFQO2dCQUNyQ2hHLE9BQU91RyxnQkFBZ0IsQ0FBQyxVQUFVSDtnQkFFbEMsVUFBVTtnQkFDVjs2Q0FBTzt3QkFDTCxJQUFJbEgsYUFBYUksT0FBTyxFQUFFOzRCQUN4QmtILHFCQUFxQnRILGFBQWFJLE9BQU87d0JBQzNDO3dCQUVBVSxPQUFPeUcsbUJBQW1CLENBQUMsYUFBYVQ7d0JBQ3hDaEcsT0FBT3lHLG1CQUFtQixDQUFDLFVBQVVMO3dCQUVyQyxpQkFBaUI7d0JBQ2pCLElBQUl2SCxTQUFTUyxPQUFPLElBQUlHLFNBQVM0QixVQUFVLEVBQUU7NEJBQzNDeEMsU0FBU1MsT0FBTyxDQUFDb0gsV0FBVyxDQUFDakgsU0FBUzRCLFVBQVU7d0JBQ2xEO3dCQUVBLHNCQUFzQjt3QkFDdEIsSUFBSTlCLE9BQU87NEJBQ1RBLE1BQU1vSCxLQUFLO3dCQUNiO3dCQUNBLElBQUlsSCxVQUFVOzRCQUNaQSxTQUFTbUgsT0FBTzt3QkFDbEI7d0JBRUEsOEJBQThCO3dCQUM5QixJQUFJbEgsV0FBVzs0QkFDYixJQUFJQSxVQUFVbUgsUUFBUSxFQUFFO2dDQUN0Qm5ILFVBQVVtSCxRQUFRLENBQUNELE9BQU87NEJBQzVCOzRCQUNBLElBQUlsSCxVQUFVMEYsUUFBUSxFQUFFO2dDQUNyQjFGLFVBQVUwRixRQUFRLENBQW9Cd0IsT0FBTzs0QkFDaEQ7d0JBQ0Y7d0JBRUEsSUFBSWpILGlCQUFpQjs0QkFDbkJBLGdCQUFnQjRGLFFBQVEsQ0FBQ0MsT0FBTzt5REFBQ3NCLENBQUFBO29DQUMvQixJQUFJQSxpQkFBaUJwSSx1Q0FBVSxFQUFFO3dDQUMvQixJQUFJb0ksTUFBTUQsUUFBUSxFQUFFOzRDQUNsQkMsTUFBTUQsUUFBUSxDQUFDRCxPQUFPO3dDQUN4Qjt3Q0FDQSxJQUFJRSxNQUFNMUIsUUFBUSxFQUFFOzRDQUNqQjBCLE1BQU0xQixRQUFRLENBQW9Cd0IsT0FBTzt3Q0FDNUM7b0NBQ0Y7Z0NBQ0Y7O3dCQUNGO29CQUNGOztZQUVGLEVBQUUsT0FBT0csT0FBTztnQkFDZHBELFFBQVFDLElBQUksQ0FBQyx5Q0FBeUNtRDtnQkFDdERoSSxvQkFBb0I7WUFDdEI7UUFDRjtnQ0FBRyxFQUFFO0lBRUwscUJBQ0UsOERBQUNpSTtRQUFRQyxXQUFVOztZQUVoQm5JLGtDQUNDLDhEQUFDb0k7Z0JBQ0NDLEtBQUt0STtnQkFDTG9JLFdBQVU7Z0JBQ1ZHLE9BQU87b0JBQUVDLFFBQVE7Z0JBQUU7Ozs7OztZQUt0QixDQUFDdkksa0NBQ0EsOERBQUNvSTtnQkFBSUQsV0FBVTs7a0NBQ2IsOERBQUNDO3dCQUFJRCxXQUFVOzs7Ozs7a0NBQ2YsOERBQUNDO3dCQUFJRCxXQUFVOzs7Ozs7a0NBQ2YsOERBQUNDO3dCQUFJRCxXQUFVOzs7Ozs7Ozs7Ozs7MEJBS25CLDhEQUFDQztnQkFBSUQsV0FBVTtnQkFBc0ZHLE9BQU87b0JBQUVDLFFBQVE7Z0JBQUU7Ozs7OzswQkFHeEgsOERBQUNIO2dCQUFJRCxXQUFVOztrQ0FFYiw4REFBQ0M7d0JBQUlELFdBQVU7OzBDQUNiLDhEQUFDSztnQ0FBS0wsV0FBVTswQ0FBZTs7Ozs7OzRCQUFROzs7Ozs7O2tDQUt6Qyw4REFBQ007d0JBQUdOLFdBQVU7OzRCQUEyRTswQ0FFdkYsOERBQUNPOzs7OzswQ0FDRCw4REFBQ0Y7Z0NBQUtMLFdBQVU7MENBQTBHOzs7Ozs7Ozs7Ozs7a0NBTTVILDhEQUFDUTt3QkFBRVIsV0FBVTtrQ0FBdUY7Ozs7OztrQ0FNcEcsOERBQUNDO3dCQUFJRCxXQUFVOzswQ0FDYiw4REFBQ3RJLGtEQUFJQTtnQ0FDSCtJLE1BQUs7Z0NBQ0xULFdBQVU7O2tEQUVWLDhEQUFDQzt3Q0FBSUQsV0FBVTs7Ozs7O2tEQUNmLDhEQUFDSzt3Q0FBS0wsV0FBVTs7NENBQTRDOzBEQUUxRCw4REFBQ1U7Z0RBQUlWLFdBQVU7Z0RBQTJFVyxNQUFLO2dEQUFPQyxRQUFPO2dEQUFlQyxTQUFROzBEQUNsSSw0RUFBQ0M7b0RBQUtDLGVBQWM7b0RBQVFDLGdCQUFlO29EQUFRQyxhQUFhO29EQUFHQyxHQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FLM0UsOERBQUN4SixrREFBSUE7Z0NBQ0grSSxNQUFLO2dDQUNMVCxXQUFVOzBDQUVWLDRFQUFDSztvQ0FBS0wsV0FBVTs7d0NBQW1DO3NEQUVqRCw4REFBQ1U7NENBQUlWLFdBQVU7NENBQTJFVyxNQUFLOzRDQUFPQyxRQUFPOzRDQUFlQyxTQUFRO3NEQUNsSSw0RUFBQ0M7Z0RBQUtDLGVBQWM7Z0RBQVFDLGdCQUFlO2dEQUFRQyxhQUFhO2dEQUFHQyxHQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQU83RSw4REFBQ2pCO3dCQUFJRCxXQUFVO2tDQUNaOzRCQUNDO2dDQUFFbUIsUUFBUTtnQ0FBUUMsT0FBTzs0QkFBWTs0QkFDckM7Z0NBQUVELFFBQVE7Z0NBQU9DLE9BQU87NEJBQVk7NEJBQ3BDO2dDQUFFRCxRQUFRO2dDQUFRQyxPQUFPOzRCQUFXOzRCQUNwQztnQ0FBRUQsUUFBUTtnQ0FBUUMsT0FBTzs0QkFBVTt5QkFDcEMsQ0FBQ0MsR0FBRyxDQUFDLENBQUNDLE1BQU03QyxzQkFDWCw4REFBQ3dCO2dDQUFnQkQsV0FBVTs7a0RBQ3pCLDhEQUFDQzt3Q0FBSUQsV0FBVTtrREFDWnNCLEtBQUtILE1BQU07Ozs7OztrREFFZCw4REFBQ2xCO3dDQUFJRCxXQUFVO2tEQUNac0IsS0FBS0YsS0FBSzs7Ozs7OzsrQkFMTDNDOzs7Ozs7Ozs7Ozs7Ozs7OzBCQWFoQiw4REFBQ3dCO2dCQUFJRCxXQUFVOzBCQUNiLDRFQUFDQztvQkFBSUQsV0FBVTs4QkFDYiw0RUFBQ0M7d0JBQUlELFdBQVU7a0NBQ2IsNEVBQUNVOzRCQUFJVixXQUFVOzRCQUF3QlcsTUFBSzs0QkFBT0MsUUFBTzs0QkFBZUMsU0FBUTtzQ0FDL0UsNEVBQUNDO2dDQUFLQyxlQUFjO2dDQUFRQyxnQkFBZTtnQ0FBUUMsYUFBYTtnQ0FBR0MsR0FBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7WUFPNUVySixrQ0FDQyw4REFBQ29JO2dCQUFJRCxXQUFVOzBCQUNiLDRFQUFDQztvQkFBSUQsV0FBVTs4QkFBdUc7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBT2hJO0dBaGNnQnJJO0tBQUFBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGlzbWFpbFxcRG93bmxvYWRzXFwxMTExMTExMTExMTExMVxcdGVjaG5vLWZsYXNoaVxcc3JjXFxjb21wb25lbnRzXFxIZXJvU2VjdGlvbi50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyB1c2VFZmZlY3QsIHVzZVJlZiwgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgKiBhcyBUSFJFRSBmcm9tICd0aHJlZSc7XG5pbXBvcnQgTGluayBmcm9tICduZXh0L2xpbmsnO1xuXG5leHBvcnQgZnVuY3Rpb24gSGVyb1NlY3Rpb24oKSB7XG4gIGNvbnN0IG1vdW50UmVmID0gdXNlUmVmPEhUTUxEaXZFbGVtZW50PihudWxsKTtcbiAgY29uc3QgW2lzV2ViR0xTdXBwb3J0ZWQsIHNldElzV2ViR0xTdXBwb3J0ZWRdID0gdXNlU3RhdGUodHJ1ZSk7XG4gIGNvbnN0IHNjZW5lUmVmID0gdXNlUmVmPFRIUkVFLlNjZW5lPigpO1xuICBjb25zdCByZW5kZXJlclJlZiA9IHVzZVJlZjxUSFJFRS5XZWJHTFJlbmRlcmVyPigpO1xuICBjb25zdCBhbmltYXRpb25SZWYgPSB1c2VSZWY8bnVtYmVyPigpO1xuICBjb25zdCBtb3VzZVJlZiA9IHVzZVJlZih7IHg6IDAsIHk6IDAgfSk7XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAoIW1vdW50UmVmLmN1cnJlbnQpIHJldHVybjtcblxuICAgIGxldCBzY2VuZTogVEhSRUUuU2NlbmU7XG4gICAgbGV0IGNhbWVyYTogVEhSRUUuUGVyc3BlY3RpdmVDYW1lcmE7XG4gICAgbGV0IHJlbmRlcmVyOiBUSFJFRS5XZWJHTFJlbmRlcmVyO1xuICAgIGxldCBwYXJ0aWNsZXM6IFRIUkVFLlBvaW50cztcbiAgICBsZXQgZ2VvbWV0cmljU2hhcGVzOiBUSFJFRS5Hcm91cDtcbiAgICBsZXQgYW1iaWVudExpZ2h0OiBUSFJFRS5BbWJpZW50TGlnaHQ7XG4gICAgbGV0IHBvaW50TGlnaHQ6IFRIUkVFLlBvaW50TGlnaHQ7XG5cbiAgICB0cnkge1xuICAgICAgLy8g2KXYudiv2KfYryDYp9mE2YXYtNmH2K9cbiAgICAgIHNjZW5lID0gbmV3IFRIUkVFLlNjZW5lKCk7XG4gICAgICBzY2VuZVJlZi5jdXJyZW50ID0gc2NlbmU7XG5cbiAgICAgIC8vINil2LnYr9in2K8g2KfZhNmD2KfZhdmK2LHYp1xuICAgICAgY2FtZXJhID0gbmV3IFRIUkVFLlBlcnNwZWN0aXZlQ2FtZXJhKFxuICAgICAgICA3NSxcbiAgICAgICAgd2luZG93LmlubmVyV2lkdGggLyB3aW5kb3cuaW5uZXJIZWlnaHQsXG4gICAgICAgIDAuMSxcbiAgICAgICAgMTAwMFxuICAgICAgKTtcbiAgICAgIGNhbWVyYS5wb3NpdGlvbi56ID0gNTtcblxuICAgICAgLy8g2KXYudiv2KfYryDYp9mE2YXZj9ix2LPZkNmEINmF2Lkg2YXYudin2YTYrNipINin2YTYo9iu2LfYp9ihXG4gICAgICByZW5kZXJlciA9IG5ldyBUSFJFRS5XZWJHTFJlbmRlcmVyKHtcbiAgICAgICAgYW50aWFsaWFzOiB0cnVlLFxuICAgICAgICBhbHBoYTogdHJ1ZSxcbiAgICAgICAgcG93ZXJQcmVmZXJlbmNlOiBcImhpZ2gtcGVyZm9ybWFuY2VcIixcbiAgICAgICAgZmFpbElmTWFqb3JQZXJmb3JtYW5jZUNhdmVhdDogZmFsc2UsXG4gICAgICAgIHByZXNlcnZlRHJhd2luZ0J1ZmZlcjogZmFsc2VcbiAgICAgIH0pO1xuXG4gICAgICAvLyDZgdit2LUg2K/YudmFIFdlYkdMXG4gICAgICBjb25zdCBnbCA9IHJlbmRlcmVyLmdldENvbnRleHQoKTtcbiAgICAgIGlmICghZ2wpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdXZWJHTCBub3Qgc3VwcG9ydGVkJyk7XG4gICAgICB9XG5cbiAgICAgIHJlbmRlcmVyLnNldFNpemUod2luZG93LmlubmVyV2lkdGgsIHdpbmRvdy5pbm5lckhlaWdodCk7XG4gICAgICByZW5kZXJlci5zZXRQaXhlbFJhdGlvKE1hdGgubWluKHdpbmRvdy5kZXZpY2VQaXhlbFJhdGlvLCAyKSk7XG4gICAgICByZW5kZXJlci5zZXRDbGVhckNvbG9yKDB4MDAwMDAwLCAwKTtcbiAgICAgIHJlbmRlcmVyUmVmLmN1cnJlbnQgPSByZW5kZXJlcjtcblxuICAgICAgbW91bnRSZWYuY3VycmVudC5hcHBlbmRDaGlsZChyZW5kZXJlci5kb21FbGVtZW50KTtcblxuICAgICAgLy8g2KXYudiv2KfYryDYp9mE2KXYttin2KHYqVxuICAgICAgYW1iaWVudExpZ2h0ID0gbmV3IFRIUkVFLkFtYmllbnRMaWdodCgweDhiNWNmNiwgMC40KTtcbiAgICAgIHNjZW5lLmFkZChhbWJpZW50TGlnaHQpO1xuXG4gICAgICBwb2ludExpZ2h0ID0gbmV3IFRIUkVFLlBvaW50TGlnaHQoMHhhODU1ZjcsIDEsIDEwMCk7XG4gICAgICBwb2ludExpZ2h0LnBvc2l0aW9uLnNldCgxMCwgMTAsIDEwKTtcbiAgICAgIHNjZW5lLmFkZChwb2ludExpZ2h0KTtcblxuICAgICAgLy8g2KXZhti02KfYoSDYrNiz2YrZhdin2Kog2KvZhNin2KvZitipINin2YTYo9io2LnYp9ivXG4gICAgICBjb25zdCBwYXJ0aWNsZUNvdW50ID0gMTAwMDtcbiAgICAgIGNvbnN0IHBvc2l0aW9ucyA9IG5ldyBGbG9hdDMyQXJyYXkocGFydGljbGVDb3VudCAqIDMpO1xuICAgICAgY29uc3QgY29sb3JzID0gbmV3IEZsb2F0MzJBcnJheShwYXJ0aWNsZUNvdW50ICogMyk7XG4gICAgICBjb25zdCBzaXplcyA9IG5ldyBGbG9hdDMyQXJyYXkocGFydGljbGVDb3VudCk7XG5cbiAgICAgIGNvbnN0IGNvbG9yUGFsZXR0ZSA9IFtcbiAgICAgICAgbmV3IFRIUkVFLkNvbG9yKDB4OGI1Y2Y2KSwgLy8g2KjZhtmB2LPYrNmKXG4gICAgICAgIG5ldyBUSFJFRS5Db2xvcigweGE4NTVmNyksIC8vINio2YbZgdiz2KzZiiDZgdin2KrYrVxuICAgICAgICBuZXcgVEhSRUUuQ29sb3IoMHhjMDg0ZmMpLCAvLyDYqNmG2YHYs9is2Yog2KPZgdiq2K1cbiAgICAgICAgbmV3IFRIUkVFLkNvbG9yKDB4ZDhiNGZlKSwgLy8g2KjZhtmB2LPYrNmKINi02KfYrdioXG4gICAgICBdO1xuXG4gICAgICBmb3IgKGxldCBpID0gMDsgaSA8IHBhcnRpY2xlQ291bnQ7IGkrKykge1xuICAgICAgICAvLyDYp9mE2YXZiNin2YLYuSDYp9mE2LnYtNmI2KfYptmK2KlcbiAgICAgICAgcG9zaXRpb25zW2kgKiAzXSA9IChNYXRoLnJhbmRvbSgpIC0gMC41KSAqIDIwO1xuICAgICAgICBwb3NpdGlvbnNbaSAqIDMgKyAxXSA9IChNYXRoLnJhbmRvbSgpIC0gMC41KSAqIDIwO1xuICAgICAgICBwb3NpdGlvbnNbaSAqIDMgKyAyXSA9IChNYXRoLnJhbmRvbSgpIC0gMC41KSAqIDIwO1xuXG4gICAgICAgIC8vINin2YTYo9mE2YjYp9mGINin2YTYudi02YjYp9im2YrYqVxuICAgICAgICBjb25zdCBjb2xvciA9IGNvbG9yUGFsZXR0ZVtNYXRoLmZsb29yKE1hdGgucmFuZG9tKCkgKiBjb2xvclBhbGV0dGUubGVuZ3RoKV07XG4gICAgICAgIGNvbG9yc1tpICogM10gPSBjb2xvci5yO1xuICAgICAgICBjb2xvcnNbaSAqIDMgKyAxXSA9IGNvbG9yLmc7XG4gICAgICAgIGNvbG9yc1tpICogMyArIDJdID0gY29sb3IuYjtcblxuICAgICAgICAvLyDYp9mE2KPYrdis2KfZhSDYp9mE2LnYtNmI2KfYptmK2KlcbiAgICAgICAgc2l6ZXNbaV0gPSBNYXRoLnJhbmRvbSgpICogMyArIDE7XG4gICAgICB9XG5cbiAgICAgIGNvbnN0IHBhcnRpY2xlR2VvbWV0cnkgPSBuZXcgVEhSRUUuQnVmZmVyR2VvbWV0cnkoKTtcbiAgICAgIHBhcnRpY2xlR2VvbWV0cnkuc2V0QXR0cmlidXRlKCdwb3NpdGlvbicsIG5ldyBUSFJFRS5CdWZmZXJBdHRyaWJ1dGUocG9zaXRpb25zLCAzKSk7XG4gICAgICBwYXJ0aWNsZUdlb21ldHJ5LnNldEF0dHJpYnV0ZSgnY29sb3InLCBuZXcgVEhSRUUuQnVmZmVyQXR0cmlidXRlKGNvbG9ycywgMykpO1xuICAgICAgcGFydGljbGVHZW9tZXRyeS5zZXRBdHRyaWJ1dGUoJ3NpemUnLCBuZXcgVEhSRUUuQnVmZmVyQXR0cmlidXRlKHNpemVzLCAxKSk7XG5cbiAgICAgIC8vINil2YbYtNin2KEgU2hhZGVyIE1hdGVyaWFsINmF2Lkg2YXYudin2YTYrNipINin2YTYo9iu2LfYp9ihXG4gICAgICBsZXQgcGFydGljbGVNYXRlcmlhbDogVEhSRUUuU2hhZGVyTWF0ZXJpYWw7XG5cbiAgICAgIHRyeSB7XG4gICAgICAgIHBhcnRpY2xlTWF0ZXJpYWwgPSBuZXcgVEhSRUUuU2hhZGVyTWF0ZXJpYWwoe1xuICAgICAgICAgIHVuaWZvcm1zOiB7XG4gICAgICAgICAgICB0aW1lOiB7IHZhbHVlOiAwIH0sXG4gICAgICAgICAgICBtb3VzZTogeyB2YWx1ZTogbmV3IFRIUkVFLlZlY3RvcjIoKSB9XG4gICAgICAgICAgfSxcbiAgICAgICAgdmVydGV4U2hhZGVyOiBgXG4gICAgICAgICAgYXR0cmlidXRlIGZsb2F0IHNpemU7XG4gICAgICAgICAgYXR0cmlidXRlIHZlYzMgY29sb3I7XG4gICAgICAgICAgdmFyeWluZyB2ZWMzIHZDb2xvcjtcbiAgICAgICAgICB1bmlmb3JtIGZsb2F0IHRpbWU7XG4gICAgICAgICAgdW5pZm9ybSB2ZWMyIG1vdXNlO1xuXG4gICAgICAgICAgdm9pZCBtYWluKCkge1xuICAgICAgICAgICAgdkNvbG9yID0gY29sb3I7XG4gICAgICAgICAgICB2ZWMzIHBvcyA9IHBvc2l0aW9uO1xuXG4gICAgICAgICAgICAvLyDYqtij2KvZitixINin2YTZhdmI2KzYqVxuICAgICAgICAgICAgcG9zLnkgKz0gc2luKHBvcy54ICogMC41ICsgdGltZSkgKiAwLjU7XG4gICAgICAgICAgICBwb3MueCArPSBjb3MocG9zLnogKiAwLjUgKyB0aW1lKSAqIDAuMztcblxuICAgICAgICAgICAgLy8g2KrYo9ir2YrYsSDYp9mE2YXYp9mI2LNcbiAgICAgICAgICAgIHZlYzIgbW91c2VJbmZsdWVuY2UgPSBtb3VzZSAqIDAuMTtcbiAgICAgICAgICAgIHBvcy54eSArPSBtb3VzZUluZmx1ZW5jZSAqICgxLjAgLSBsZW5ndGgocG9zLnh5KSAqIDAuMSk7XG5cbiAgICAgICAgICAgIHZlYzQgbXZQb3NpdGlvbiA9IG1vZGVsVmlld01hdHJpeCAqIHZlYzQocG9zLCAxLjApO1xuICAgICAgICAgICAgZ2xfUG9pbnRTaXplID0gc2l6ZSAqICgzMDAuMCAvIG1heCgtbXZQb3NpdGlvbi56LCAxLjApKTtcbiAgICAgICAgICAgIGdsX1Bvc2l0aW9uID0gcHJvamVjdGlvbk1hdHJpeCAqIG12UG9zaXRpb247XG4gICAgICAgICAgfVxuICAgICAgICBgLFxuICAgICAgICBmcmFnbWVudFNoYWRlcjogYFxuICAgICAgICAgIHZhcnlpbmcgdmVjMyB2Q29sb3I7XG5cbiAgICAgICAgICB2b2lkIG1haW4oKSB7XG4gICAgICAgICAgICBmbG9hdCBkaXN0YW5jZVRvQ2VudGVyID0gZGlzdGFuY2UoZ2xfUG9pbnRDb29yZCwgdmVjMigwLjUpKTtcbiAgICAgICAgICAgIGZsb2F0IGFscGhhID0gMS4wIC0gc21vb3Roc3RlcCgwLjAsIDAuNSwgZGlzdGFuY2VUb0NlbnRlcik7XG4gICAgICAgICAgICBnbF9GcmFnQ29sb3IgPSB2ZWM0KHZDb2xvciwgYWxwaGEgKiAwLjgpO1xuICAgICAgICAgIH1cbiAgICAgICAgYCxcbiAgICAgICAgICB0cmFuc3BhcmVudDogdHJ1ZSxcbiAgICAgICAgICB2ZXJ0ZXhDb2xvcnM6IHRydWUsXG4gICAgICAgICAgYmxlbmRpbmc6IFRIUkVFLkFkZGl0aXZlQmxlbmRpbmdcbiAgICAgICAgfSk7XG5cbiAgICAgICAgLy8g2YHYrdi1INil2LDYpyDZg9in2YYg2KfZhNmAIHNoYWRlciDYqtmFINiq2KzZhdmK2LnZhyDYqNmG2KzYp9itXG4gICAgICAgIGlmICghcGFydGljbGVNYXRlcmlhbC52ZXJ0ZXhTaGFkZXIgfHwgIXBhcnRpY2xlTWF0ZXJpYWwuZnJhZ21lbnRTaGFkZXIpIHtcbiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ1NoYWRlciBjb21waWxhdGlvbiBmYWlsZWQnKTtcbiAgICAgICAgfVxuXG4gICAgICB9IGNhdGNoIChzaGFkZXJFcnJvcikge1xuICAgICAgICBjb25zb2xlLndhcm4oJ1NoYWRlciBjcmVhdGlvbiBmYWlsZWQsIHVzaW5nIGJhc2ljIG1hdGVyaWFsOicsIHNoYWRlckVycm9yKTtcblxuICAgICAgICAvLyDYp9iz2KrYrtiv2KfZhSDZhdin2K/YqSDYo9iz2KfYs9mK2Kkg2YPYqNiv2YrZhFxuICAgICAgICBwYXJ0aWNsZU1hdGVyaWFsID0gbmV3IFRIUkVFLlBvaW50c01hdGVyaWFsKHtcbiAgICAgICAgICBzaXplOiAyLFxuICAgICAgICAgIHZlcnRleENvbG9yczogdHJ1ZSxcbiAgICAgICAgICB0cmFuc3BhcmVudDogdHJ1ZSxcbiAgICAgICAgICBvcGFjaXR5OiAwLjgsXG4gICAgICAgICAgYmxlbmRpbmc6IFRIUkVFLkFkZGl0aXZlQmxlbmRpbmdcbiAgICAgICAgfSk7XG4gICAgICB9XG5cbiAgICAgIHBhcnRpY2xlcyA9IG5ldyBUSFJFRS5Qb2ludHMocGFydGljbGVHZW9tZXRyeSwgcGFydGljbGVNYXRlcmlhbCk7XG4gICAgICBzY2VuZS5hZGQocGFydGljbGVzKTtcblxuICAgICAgLy8g2KXZhti02KfYoSDYo9i02YPYp9mEINmH2YbYr9iz2YrYqSDZhdi52YLYr9ipXG4gICAgICBnZW9tZXRyaWNTaGFwZXMgPSBuZXcgVEhSRUUuR3JvdXAoKTtcblxuICAgICAgLy8g2YXZg9i52KjYp9iqINmF2KrYrdix2YPYqVxuICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCA1OyBpKyspIHtcbiAgICAgICAgY29uc3QgY3ViZUdlb21ldHJ5ID0gbmV3IFRIUkVFLkJveEdlb21ldHJ5KDAuNSwgMC41LCAwLjUpO1xuICAgICAgICBjb25zdCBjdWJlTWF0ZXJpYWwgPSBuZXcgVEhSRUUuTWVzaFBob25nTWF0ZXJpYWwoe1xuICAgICAgICAgIGNvbG9yOiBjb2xvclBhbGV0dGVbaSAlIGNvbG9yUGFsZXR0ZS5sZW5ndGhdLFxuICAgICAgICAgIHRyYW5zcGFyZW50OiB0cnVlLFxuICAgICAgICAgIG9wYWNpdHk6IDAuMyxcbiAgICAgICAgICB3aXJlZnJhbWU6IHRydWVcbiAgICAgICAgfSk7XG4gICAgICAgIGNvbnN0IGN1YmUgPSBuZXcgVEhSRUUuTWVzaChjdWJlR2VvbWV0cnksIGN1YmVNYXRlcmlhbCk7XG5cbiAgICAgICAgY3ViZS5wb3NpdGlvbi5zZXQoXG4gICAgICAgICAgKE1hdGgucmFuZG9tKCkgLSAwLjUpICogMTAsXG4gICAgICAgICAgKE1hdGgucmFuZG9tKCkgLSAwLjUpICogMTAsXG4gICAgICAgICAgKE1hdGgucmFuZG9tKCkgLSAwLjUpICogMTBcbiAgICAgICAgKTtcblxuICAgICAgICBnZW9tZXRyaWNTaGFwZXMuYWRkKGN1YmUpO1xuICAgICAgfVxuXG4gICAgICAvLyDZg9ix2KfYqiDZhdiq2YjZh9is2KlcbiAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgMzsgaSsrKSB7XG4gICAgICAgIGNvbnN0IHNwaGVyZUdlb21ldHJ5ID0gbmV3IFRIUkVFLlNwaGVyZUdlb21ldHJ5KDAuMywgMTYsIDE2KTtcbiAgICAgICAgY29uc3Qgc3BoZXJlTWF0ZXJpYWwgPSBuZXcgVEhSRUUuTWVzaFBob25nTWF0ZXJpYWwoe1xuICAgICAgICAgIGNvbG9yOiBjb2xvclBhbGV0dGVbKGkgKyAyKSAlIGNvbG9yUGFsZXR0ZS5sZW5ndGhdLFxuICAgICAgICAgIHRyYW5zcGFyZW50OiB0cnVlLFxuICAgICAgICAgIG9wYWNpdHk6IDAuNixcbiAgICAgICAgICBlbWlzc2l2ZTogY29sb3JQYWxldHRlWyhpICsgMikgJSBjb2xvclBhbGV0dGUubGVuZ3RoXSxcbiAgICAgICAgICBlbWlzc2l2ZUludGVuc2l0eTogMC4yXG4gICAgICAgIH0pO1xuICAgICAgICBjb25zdCBzcGhlcmUgPSBuZXcgVEhSRUUuTWVzaChzcGhlcmVHZW9tZXRyeSwgc3BoZXJlTWF0ZXJpYWwpO1xuXG4gICAgICAgIHNwaGVyZS5wb3NpdGlvbi5zZXQoXG4gICAgICAgICAgKE1hdGgucmFuZG9tKCkgLSAwLjUpICogOCxcbiAgICAgICAgICAoTWF0aC5yYW5kb20oKSAtIDAuNSkgKiA4LFxuICAgICAgICAgIChNYXRoLnJhbmRvbSgpIC0gMC41KSAqIDhcbiAgICAgICAgKTtcblxuICAgICAgICBnZW9tZXRyaWNTaGFwZXMuYWRkKHNwaGVyZSk7XG4gICAgICB9XG5cbiAgICAgIHNjZW5lLmFkZChnZW9tZXRyaWNTaGFwZXMpO1xuXG4gICAgICAvLyDYrdmE2YLYqSDYp9mE2LHYs9mFINmI2KfZhNiq2K3YsdmK2YNcbiAgICAgIGNvbnN0IGNsb2NrID0gbmV3IFRIUkVFLkNsb2NrKCk7XG5cbiAgICAgIGNvbnN0IGFuaW1hdGUgPSAoKSA9PiB7XG4gICAgICAgIGNvbnN0IGVsYXBzZWRUaW1lID0gY2xvY2suZ2V0RWxhcHNlZFRpbWUoKTtcblxuICAgICAgICAvLyDYqtit2LHZitmDINin2YTYrNiz2YrZhdin2Kog2YXYuSDZgdit2LUg2KfZhNmG2YjYuVxuICAgICAgICBpZiAocGFydGljbGVzICYmIHBhcnRpY2xlcy5tYXRlcmlhbCkge1xuICAgICAgICAgIGlmIChwYXJ0aWNsZXMubWF0ZXJpYWwgaW5zdGFuY2VvZiBUSFJFRS5TaGFkZXJNYXRlcmlhbCAmJiBwYXJ0aWNsZXMubWF0ZXJpYWwudW5pZm9ybXMpIHtcbiAgICAgICAgICAgIHBhcnRpY2xlcy5tYXRlcmlhbC51bmlmb3Jtcy50aW1lLnZhbHVlID0gZWxhcHNlZFRpbWU7XG4gICAgICAgICAgICBwYXJ0aWNsZXMubWF0ZXJpYWwudW5pZm9ybXMubW91c2UudmFsdWUuc2V0KFxuICAgICAgICAgICAgICBtb3VzZVJlZi5jdXJyZW50LngsXG4gICAgICAgICAgICAgIG1vdXNlUmVmLmN1cnJlbnQueVxuICAgICAgICAgICAgKTtcbiAgICAgICAgICB9XG4gICAgICAgIH1cblxuICAgICAgICAvLyDYqtiv2YjZitixINin2YTYrNiz2YrZhdin2KpcbiAgICAgICAgaWYgKHBhcnRpY2xlcykge1xuICAgICAgICAgIHBhcnRpY2xlcy5yb3RhdGlvbi55ID0gZWxhcHNlZFRpbWUgKiAwLjA1O1xuICAgICAgICAgIHBhcnRpY2xlcy5yb3RhdGlvbi54ID0gTWF0aC5zaW4oZWxhcHNlZFRpbWUgKiAwLjAzKSAqIDAuMTtcbiAgICAgICAgfVxuXG4gICAgICAgIC8vINiq2K3YsdmK2YMg2KfZhNij2LTZg9in2YQg2KfZhNmH2YbYr9iz2YrYqVxuICAgICAgICBnZW9tZXRyaWNTaGFwZXMuY2hpbGRyZW4uZm9yRWFjaCgoc2hhcGUsIGluZGV4KSA9PiB7XG4gICAgICAgICAgaWYgKHNoYXBlIGluc3RhbmNlb2YgVEhSRUUuTWVzaCkge1xuICAgICAgICAgICAgLy8g2K/ZiNix2KfZhiDZhdiu2KrZhNmBINmE2YPZhCDYtNmD2YRcbiAgICAgICAgICAgIHNoYXBlLnJvdGF0aW9uLnggKz0gMC4wMSAqIChpbmRleCArIDEpO1xuICAgICAgICAgICAgc2hhcGUucm90YXRpb24ueSArPSAwLjAxNSAqIChpbmRleCArIDEpO1xuXG4gICAgICAgICAgICAvLyDYrdix2YPYqSDYqtmF2YjYrNmK2KlcbiAgICAgICAgICAgIHNoYXBlLnBvc2l0aW9uLnkgKz0gTWF0aC5zaW4oZWxhcHNlZFRpbWUgKyBpbmRleCkgKiAwLjAwMjtcbiAgICAgICAgICAgIHNoYXBlLnBvc2l0aW9uLnggKz0gTWF0aC5jb3MoZWxhcHNlZFRpbWUgKiAwLjUgKyBpbmRleCkgKiAwLjAwMTtcbiAgICAgICAgICB9XG4gICAgICAgIH0pO1xuXG4gICAgICAgIC8vINiq2K3YsdmK2YMg2KfZhNil2LbYp9ih2KlcbiAgICAgICAgcG9pbnRMaWdodC5wb3NpdGlvbi54ID0gTWF0aC5zaW4oZWxhcHNlZFRpbWUpICogNTtcbiAgICAgICAgcG9pbnRMaWdodC5wb3NpdGlvbi56ID0gTWF0aC5jb3MoZWxhcHNlZFRpbWUpICogNTtcbiAgICAgICAgcG9pbnRMaWdodC5pbnRlbnNpdHkgPSAwLjggKyBNYXRoLnNpbihlbGFwc2VkVGltZSAqIDIpICogMC4yO1xuXG4gICAgICAgIC8vINiq2KPYq9mK2LEg2KfZhNmF2KfZiNizINi52YTZiSDYp9mE2YPYp9mF2YrYsdinXG4gICAgICAgIGNhbWVyYS5wb3NpdGlvbi54ICs9IChtb3VzZVJlZi5jdXJyZW50LnggKiAwLjAwMSAtIGNhbWVyYS5wb3NpdGlvbi54KSAqIDAuMDU7XG4gICAgICAgIGNhbWVyYS5wb3NpdGlvbi55ICs9ICgtbW91c2VSZWYuY3VycmVudC55ICogMC4wMDEgLSBjYW1lcmEucG9zaXRpb24ueSkgKiAwLjA1O1xuICAgICAgICBjYW1lcmEubG9va0F0KHNjZW5lLnBvc2l0aW9uKTtcblxuICAgICAgICByZW5kZXJlci5yZW5kZXIoc2NlbmUsIGNhbWVyYSk7XG4gICAgICAgIGFuaW1hdGlvblJlZi5jdXJyZW50ID0gcmVxdWVzdEFuaW1hdGlvbkZyYW1lKGFuaW1hdGUpO1xuICAgICAgfTtcblxuICAgICAgYW5pbWF0ZSgpO1xuXG4gICAgICAvLyDZhdi52KfZhNisINit2LHZg9ipINin2YTZhdin2YjYs1xuICAgICAgY29uc3QgaGFuZGxlTW91c2VNb3ZlID0gKGV2ZW50OiBNb3VzZUV2ZW50KSA9PiB7XG4gICAgICAgIG1vdXNlUmVmLmN1cnJlbnQueCA9IChldmVudC5jbGllbnRYIC8gd2luZG93LmlubmVyV2lkdGgpICogMiAtIDE7XG4gICAgICAgIG1vdXNlUmVmLmN1cnJlbnQueSA9IC0oZXZlbnQuY2xpZW50WSAvIHdpbmRvdy5pbm5lckhlaWdodCkgKiAyICsgMTtcbiAgICAgIH07XG5cbiAgICAgIC8vINmF2LnYp9mE2Kwg2KrYutmK2YrYsSDYrdis2YUg2KfZhNmG2KfZgdiw2KlcbiAgICAgIGNvbnN0IGhhbmRsZVJlc2l6ZSA9ICgpID0+IHtcbiAgICAgICAgY2FtZXJhLmFzcGVjdCA9IHdpbmRvdy5pbm5lcldpZHRoIC8gd2luZG93LmlubmVySGVpZ2h0O1xuICAgICAgICBjYW1lcmEudXBkYXRlUHJvamVjdGlvbk1hdHJpeCgpO1xuICAgICAgICByZW5kZXJlci5zZXRTaXplKHdpbmRvdy5pbm5lcldpZHRoLCB3aW5kb3cuaW5uZXJIZWlnaHQpO1xuICAgICAgICByZW5kZXJlci5zZXRQaXhlbFJhdGlvKE1hdGgubWluKHdpbmRvdy5kZXZpY2VQaXhlbFJhdGlvLCAyKSk7XG4gICAgICB9O1xuXG4gICAgICAvLyDYpdi22KfZgdipINin2YTZhdiz2KrZhdi52YrZhlxuICAgICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoJ21vdXNlbW92ZScsIGhhbmRsZU1vdXNlTW92ZSk7XG4gICAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcigncmVzaXplJywgaGFuZGxlUmVzaXplKTtcblxuICAgICAgLy8g2KfZhNiq2YbYuNmK2YFcbiAgICAgIHJldHVybiAoKSA9PiB7XG4gICAgICAgIGlmIChhbmltYXRpb25SZWYuY3VycmVudCkge1xuICAgICAgICAgIGNhbmNlbEFuaW1hdGlvbkZyYW1lKGFuaW1hdGlvblJlZi5jdXJyZW50KTtcbiAgICAgICAgfVxuXG4gICAgICAgIHdpbmRvdy5yZW1vdmVFdmVudExpc3RlbmVyKCdtb3VzZW1vdmUnLCBoYW5kbGVNb3VzZU1vdmUpO1xuICAgICAgICB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcigncmVzaXplJywgaGFuZGxlUmVzaXplKTtcblxuICAgICAgICAvLyDYqtmG2LjZitmBIFRocmVlLmpzXG4gICAgICAgIGlmIChtb3VudFJlZi5jdXJyZW50ICYmIHJlbmRlcmVyLmRvbUVsZW1lbnQpIHtcbiAgICAgICAgICBtb3VudFJlZi5jdXJyZW50LnJlbW92ZUNoaWxkKHJlbmRlcmVyLmRvbUVsZW1lbnQpO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8g2KrZhti42YrZgSDYp9mE2LDYp9mD2LHYqSDYqNij2YXYp9mGXG4gICAgICAgIGlmIChzY2VuZSkge1xuICAgICAgICAgIHNjZW5lLmNsZWFyKCk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHJlbmRlcmVyKSB7XG4gICAgICAgICAgcmVuZGVyZXIuZGlzcG9zZSgpO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8g2KrZhti42YrZgSDYp9mE2YfZhtiv2LPYqSDZiNin2YTZhdmI2KfYryDYqNij2YXYp9mGXG4gICAgICAgIGlmIChwYXJ0aWNsZXMpIHtcbiAgICAgICAgICBpZiAocGFydGljbGVzLmdlb21ldHJ5KSB7XG4gICAgICAgICAgICBwYXJ0aWNsZXMuZ2VvbWV0cnkuZGlzcG9zZSgpO1xuICAgICAgICAgIH1cbiAgICAgICAgICBpZiAocGFydGljbGVzLm1hdGVyaWFsKSB7XG4gICAgICAgICAgICAocGFydGljbGVzLm1hdGVyaWFsIGFzIFRIUkVFLk1hdGVyaWFsKS5kaXNwb3NlKCk7XG4gICAgICAgICAgfVxuICAgICAgICB9XG5cbiAgICAgICAgaWYgKGdlb21ldHJpY1NoYXBlcykge1xuICAgICAgICAgIGdlb21ldHJpY1NoYXBlcy5jaGlsZHJlbi5mb3JFYWNoKGNoaWxkID0+IHtcbiAgICAgICAgICAgIGlmIChjaGlsZCBpbnN0YW5jZW9mIFRIUkVFLk1lc2gpIHtcbiAgICAgICAgICAgICAgaWYgKGNoaWxkLmdlb21ldHJ5KSB7XG4gICAgICAgICAgICAgICAgY2hpbGQuZ2VvbWV0cnkuZGlzcG9zZSgpO1xuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgIGlmIChjaGlsZC5tYXRlcmlhbCkge1xuICAgICAgICAgICAgICAgIChjaGlsZC5tYXRlcmlhbCBhcyBUSFJFRS5NYXRlcmlhbCkuZGlzcG9zZSgpO1xuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgfSk7XG4gICAgICAgIH1cbiAgICAgIH07XG5cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS53YXJuKCdXZWJHTC9UaHJlZS5qcyBpbml0aWFsaXphdGlvbiBmYWlsZWQ6JywgZXJyb3IpO1xuICAgICAgc2V0SXNXZWJHTFN1cHBvcnRlZChmYWxzZSk7XG4gICAgfVxuICB9LCBbXSk7XG5cbiAgcmV0dXJuIChcbiAgICA8c2VjdGlvbiBjbGFzc05hbWU9XCJyZWxhdGl2ZSBtaW4taC1zY3JlZW4gZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgb3ZlcmZsb3ctaGlkZGVuIGJnLWdyYWRpZW50LXRvLWJyIGZyb20tc2xhdGUtOTAwIHZpYS1wdXJwbGUtOTAwIHRvLXNsYXRlLTkwMFwiPlxuICAgICAgey8qIFRocmVlLmpzIENhbnZhcyDZhNmE2K7ZhNmB2YrYqSDYq9mE2KfYq9mK2Kkg2KfZhNij2KjYudin2K8gKi99XG4gICAgICB7aXNXZWJHTFN1cHBvcnRlZCAmJiAoXG4gICAgICAgIDxkaXZcbiAgICAgICAgICByZWY9e21vdW50UmVmfVxuICAgICAgICAgIGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgdy1mdWxsIGgtZnVsbFwiXG4gICAgICAgICAgc3R5bGU9e3sgekluZGV4OiAxIH19XG4gICAgICAgIC8+XG4gICAgICApfVxuXG4gICAgICB7LyogRmFsbGJhY2sgZ3JhZGllbnQg2YTZhNij2KzZh9iy2Kkg2KfZhNiq2Yog2YTYpyDYqtiv2LnZhSBXZWJHTCAqL31cbiAgICAgIHshaXNXZWJHTFN1cHBvcnRlZCAmJiAoXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCBiZy1ncmFkaWVudC10by1iciBmcm9tLXB1cnBsZS02MDAvMjAgdmlhLXBpbmstNjAwLzIwIHRvLXB1cnBsZS02MDAvMjBcIiAvPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCBiZy1bcmFkaWFsLWdyYWRpZW50KGNpcmNsZV9hdF81MCVfNTAlLHJnYmEoMTM5LDkyLDI0NiwwLjEpLHRyYW5zcGFyZW50XzcwJSldXCIgLz5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgYmctW2NvbmljLWdyYWRpZW50KGZyb21fMGRlZ19hdF81MCVfNTAlLHJnYmEoMTY4LDg1LDI0NywwLjEpLHJnYmEoMTkyLDEzMiwyNTIsMC4xKSxyZ2JhKDE2OCw4NSwyNDcsMC4xKSldXCIgLz5cbiAgICAgICAgPC9kaXY+XG4gICAgICApfVxuXG4gICAgICB7Lyog2LfYqNmC2Kkg2KrYo9ir2YrYsSDYpdi22KfZgdmK2KkgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgYmctZ3JhZGllbnQtdG8tdCBmcm9tLXNsYXRlLTkwMC81MCB2aWEtdHJhbnNwYXJlbnQgdG8tc2xhdGUtOTAwLzMwXCIgc3R5bGU9e3sgekluZGV4OiAyIH19IC8+XG5cbiAgICAgIHsvKiDYp9mE2YXYrdiq2YjZiSDYp9mE2LHYptmK2LPZiiAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmUgei0xMCBjb250YWluZXIgbXgtYXV0byBweC02IHRleHQtY2VudGVyXCI+XG4gICAgICAgIHsvKiDYp9mE2LTYudin2LEg2KfZhNi12LrZitixICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBweC02IHB5LTMgYmctd2hpdGUvMTAgYmFja2Ryb3AtYmx1ci1tZCB0ZXh0LXB1cnBsZS0yMDAgdGV4dC1zbSBmb250LXNlbWlib2xkIHJvdW5kZWQtZnVsbCBtYi04IGJvcmRlciBib3JkZXItcHVycGxlLTMwMC8zMCBzaGFkb3ctbGdcIj5cbiAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJtci0yIHRleHQtbGdcIj7inKg8L3NwYW4+XG4gICAgICAgICAg2KjZiNin2KjYqtmDINmE2YTZhdiz2KrZgtio2YQg2KfZhNiq2YLZhtmKXG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiDYp9mE2LnZhtmI2KfZhiDYp9mE2LHYptmK2LPZiiAqL31cbiAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtNXhsIG1kOnRleHQtN3hsIGxnOnRleHQtOHhsIGZvbnQtYm9sZCB0ZXh0LXdoaXRlIGxlYWRpbmctdGlnaHQgbWItNlwiPlxuICAgICAgICAgINmF2LPYqtmC2KjZhNmDINin2YTYqtmC2YbZilxuICAgICAgICAgIDxiciAvPlxuICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtdHJhbnNwYXJlbnQgYmctY2xpcC10ZXh0IGJnLWdyYWRpZW50LXRvLXIgZnJvbS1wdXJwbGUtNDAwIHZpYS1waW5rLTQwMCB0by1wdXJwbGUtNjAwIGFuaW1hdGUtcHVsc2VcIj5cbiAgICAgICAgICAgINmK2KjYr9ijINmF2YYg2YfZhtinXG4gICAgICAgICAgPC9zcGFuPlxuICAgICAgICA8L2gxPlxuXG4gICAgICAgIHsvKiDYp9mE2YjYtdmBICovfVxuICAgICAgICA8cCBjbGFzc05hbWU9XCJtYXgtdy00eGwgbXgtYXV0byB0ZXh0LXhsIG1kOnRleHQtMnhsIHRleHQtZ3JheS0zMDAgbWItMTIgbGVhZGluZy1yZWxheGVkIGZvbnQtbGlnaHRcIj5cbiAgICAgICAgICDYp9mD2KrYtNmBINij2K3Yr9irINin2YTYqtmC2YbZitin2KrYjCDYo9iv2YjYp9iqINin2YTYsNmD2KfYoSDYp9mE2KfYtdi32YbYp9i52Yog2KfZhNmF2KrYt9mI2LHYqdiMINmI2YXZgtin2YTYp9iqINiq2YLZhtmK2Kkg2YXYqtiu2LXYtdipINmE2KrYt9mI2YrYsSDZhdmH2KfYsdin2KrZg1xuICAgICAgICAgINmI2KrYrdmC2YrZgiDYo9mH2K/Yp9mB2YMg2YHZiiDYudin2YTZhSDYp9mE2KrZg9mG2YjZhNmI2KzZitinINin2YTZhdiq2LfZiNixLlxuICAgICAgICA8L3A+XG5cbiAgICAgICAgey8qINij2LLYsdin2LEg2KfZhNil2KzYsdin2KEgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBzbTpmbGV4LXJvdyBqdXN0aWZ5LWNlbnRlciBpdGVtcy1jZW50ZXIgZ2FwLTYgbWItMjBcIj5cbiAgICAgICAgICA8TGlua1xuICAgICAgICAgICAgaHJlZj1cIi9hcnRpY2xlc1wiXG4gICAgICAgICAgICBjbGFzc05hbWU9XCJncm91cCByZWxhdGl2ZSBiZy1ncmFkaWVudC10by1yIGZyb20tcHVycGxlLTYwMCB0by1waW5rLTYwMCB0ZXh0LXdoaXRlIHB4LTEwIHB5LTUgcm91bmRlZC0yeGwgc2hhZG93LTJ4bCBob3ZlcjpzaGFkb3ctcHVycGxlLTUwMC8yNSB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi01MDAgdHJhbnNmb3JtIGhvdmVyOnNjYWxlLTEwNSBmb250LXNlbWlib2xkIHRleHQtbGcgbWluLXctWzIyMHB4XSBvdmVyZmxvdy1oaWRkZW5cIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCBiZy1ncmFkaWVudC10by1yIGZyb20tcHVycGxlLTcwMCB0by1waW5rLTcwMCBvcGFjaXR5LTAgZ3JvdXAtaG92ZXI6b3BhY2l0eS0xMDAgdHJhbnNpdGlvbi1vcGFjaXR5IGR1cmF0aW9uLTMwMFwiIC8+XG4gICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJyZWxhdGl2ZSBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgICDYp9io2K/YoyDYp9mE2KfYs9iq2YPYtNin2YFcbiAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTYgaC02IG1yLTMgZ3JvdXAtaG92ZXI6dHJhbnNsYXRlLXgtMSB0cmFuc2l0aW9uLXRyYW5zZm9ybSBkdXJhdGlvbi0zMDBcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgICAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9ezJ9IGQ9XCJNMTUgMTlsLTctNyA3LTdcIiAvPlxuICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICA8L0xpbms+XG5cbiAgICAgICAgICA8TGlua1xuICAgICAgICAgICAgaHJlZj1cIi9haS10b29sc1wiXG4gICAgICAgICAgICBjbGFzc05hbWU9XCJncm91cCByZWxhdGl2ZSBiZy13aGl0ZS8xMCBiYWNrZHJvcC1ibHVyLW1kIHRleHQtd2hpdGUgcHgtMTAgcHktNSByb3VuZGVkLTJ4bCBib3JkZXIgYm9yZGVyLXdoaXRlLzMwIGhvdmVyOmJnLXdoaXRlLzIwIGhvdmVyOmJvcmRlci1wdXJwbGUtNDAwLzUwIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTUwMCBmb250LXNlbWlib2xkIHRleHQtbGcgbWluLXctWzIyMHB4XSBzaGFkb3cteGxcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICAgINij2K/ZiNin2Kog2KfZhNiw2YPYp9ihINin2YTYp9i12LfZhtin2LnZilxuICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNiBoLTYgbXItMyBncm91cC1ob3Zlcjp0cmFuc2xhdGUteC0xIHRyYW5zaXRpb24tdHJhbnNmb3JtIGR1cmF0aW9uLTMwMFwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn0gZD1cIk0xMyAxMFYzTDQgMTRoN3Y3bDktMTFoLTd6XCIgLz5cbiAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgPC9MaW5rPlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7Lyog2KXYrdi12KfYptmK2KfYqiDYs9ix2YrYudipICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTIgbWQ6Z3JpZC1jb2xzLTQgZ2FwLTggbWF4LXctNXhsIG14LWF1dG9cIj5cbiAgICAgICAgICB7W1xuICAgICAgICAgICAgeyBudW1iZXI6IFwiNTAwK1wiLCBsYWJlbDogXCLZhdmC2KfZhCDYqtmC2YbZilwiIH0sXG4gICAgICAgICAgICB7IG51bWJlcjogXCI1MCtcIiwgbGFiZWw6IFwi2KPYr9in2Kkg2LDZg9mK2KlcIiB9LFxuICAgICAgICAgICAgeyBudW1iZXI6IFwiMTBLK1wiLCBsYWJlbDogXCLZgtin2LHYpiDZhti02LdcIiB9LFxuICAgICAgICAgICAgeyBudW1iZXI6IFwiMjQvN1wiLCBsYWJlbDogXCLYr9i52YUg2YHZhtmKXCIgfVxuICAgICAgICAgIF0ubWFwKChzdGF0LCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgPGRpdiBrZXk9e2luZGV4fSBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBncm91cFwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtNHhsIG1kOnRleHQtNXhsIGZvbnQtYm9sZCB0ZXh0LXRyYW5zcGFyZW50IGJnLWNsaXAtdGV4dCBiZy1ncmFkaWVudC10by1yIGZyb20tcHVycGxlLTQwMCB0by1waW5rLTQwMCBtYi0zIGdyb3VwLWhvdmVyOnNjYWxlLTExMCB0cmFuc2l0aW9uLXRyYW5zZm9ybSBkdXJhdGlvbi0zMDBcIj5cbiAgICAgICAgICAgICAgICB7c3RhdC5udW1iZXJ9XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDAgdGV4dC1zbSBtZDp0ZXh0LWJhc2UgZm9udC1tZWRpdW1cIj5cbiAgICAgICAgICAgICAgICB7c3RhdC5sYWJlbH1cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApKX1cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qINmF2KTYtNixINin2YTYqtmF2LHZitixICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBib3R0b20tOCBsZWZ0LTEvMiB0cmFuc2Zvcm0gLXRyYW5zbGF0ZS14LTEvMiB6LTEwXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYW5pbWF0ZS1ib3VuY2VcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctOCBoLTggYm9yZGVyLTIgYm9yZGVyLXdoaXRlLzQwIHJvdW5kZWQtZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTQgaC00IHRleHQtd2hpdGUvNjBcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgICAgICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPXsyfSBkPVwiTTE5IDE0bC03IDdtMCAwbC03LTdtNyA3VjNcIiAvPlxuICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiDZhdik2LTYsSBXZWJHTCAqL31cbiAgICAgIHtpc1dlYkdMU3VwcG9ydGVkICYmIChcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSB0b3AtNCByaWdodC00IHotMTBcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyZWVuLTUwMC8yMCB0ZXh0LWdyZWVuLTQwMCBweC0zIHB5LTEgcm91bmRlZC1mdWxsIHRleHQteHMgZm9udC1tZWRpdW0gYm9yZGVyIGJvcmRlci1ncmVlbi01MDAvMzBcIj5cbiAgICAgICAgICAgIOKckyBXZWJHTCBBY3RpdmVcbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICApfVxuICAgIDwvc2VjdGlvbj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJ1c2VFZmZlY3QiLCJ1c2VSZWYiLCJ1c2VTdGF0ZSIsIlRIUkVFIiwiTGluayIsIkhlcm9TZWN0aW9uIiwibW91bnRSZWYiLCJpc1dlYkdMU3VwcG9ydGVkIiwic2V0SXNXZWJHTFN1cHBvcnRlZCIsInNjZW5lUmVmIiwicmVuZGVyZXJSZWYiLCJhbmltYXRpb25SZWYiLCJtb3VzZVJlZiIsIngiLCJ5IiwiY3VycmVudCIsInNjZW5lIiwiY2FtZXJhIiwicmVuZGVyZXIiLCJwYXJ0aWNsZXMiLCJnZW9tZXRyaWNTaGFwZXMiLCJhbWJpZW50TGlnaHQiLCJwb2ludExpZ2h0IiwiU2NlbmUiLCJQZXJzcGVjdGl2ZUNhbWVyYSIsIndpbmRvdyIsImlubmVyV2lkdGgiLCJpbm5lckhlaWdodCIsInBvc2l0aW9uIiwieiIsIldlYkdMUmVuZGVyZXIiLCJhbnRpYWxpYXMiLCJhbHBoYSIsInBvd2VyUHJlZmVyZW5jZSIsImZhaWxJZk1ham9yUGVyZm9ybWFuY2VDYXZlYXQiLCJwcmVzZXJ2ZURyYXdpbmdCdWZmZXIiLCJnbCIsImdldENvbnRleHQiLCJFcnJvciIsInNldFNpemUiLCJzZXRQaXhlbFJhdGlvIiwiTWF0aCIsIm1pbiIsImRldmljZVBpeGVsUmF0aW8iLCJzZXRDbGVhckNvbG9yIiwiYXBwZW5kQ2hpbGQiLCJkb21FbGVtZW50IiwiQW1iaWVudExpZ2h0IiwiYWRkIiwiUG9pbnRMaWdodCIsInNldCIsInBhcnRpY2xlQ291bnQiLCJwb3NpdGlvbnMiLCJGbG9hdDMyQXJyYXkiLCJjb2xvcnMiLCJzaXplcyIsImNvbG9yUGFsZXR0ZSIsIkNvbG9yIiwiaSIsInJhbmRvbSIsImNvbG9yIiwiZmxvb3IiLCJsZW5ndGgiLCJyIiwiZyIsImIiLCJwYXJ0aWNsZUdlb21ldHJ5IiwiQnVmZmVyR2VvbWV0cnkiLCJzZXRBdHRyaWJ1dGUiLCJCdWZmZXJBdHRyaWJ1dGUiLCJwYXJ0aWNsZU1hdGVyaWFsIiwiU2hhZGVyTWF0ZXJpYWwiLCJ1bmlmb3JtcyIsInRpbWUiLCJ2YWx1ZSIsIm1vdXNlIiwiVmVjdG9yMiIsInZlcnRleFNoYWRlciIsImZyYWdtZW50U2hhZGVyIiwidHJhbnNwYXJlbnQiLCJ2ZXJ0ZXhDb2xvcnMiLCJibGVuZGluZyIsIkFkZGl0aXZlQmxlbmRpbmciLCJzaGFkZXJFcnJvciIsImNvbnNvbGUiLCJ3YXJuIiwiUG9pbnRzTWF0ZXJpYWwiLCJzaXplIiwib3BhY2l0eSIsIlBvaW50cyIsIkdyb3VwIiwiY3ViZUdlb21ldHJ5IiwiQm94R2VvbWV0cnkiLCJjdWJlTWF0ZXJpYWwiLCJNZXNoUGhvbmdNYXRlcmlhbCIsIndpcmVmcmFtZSIsImN1YmUiLCJNZXNoIiwic3BoZXJlR2VvbWV0cnkiLCJTcGhlcmVHZW9tZXRyeSIsInNwaGVyZU1hdGVyaWFsIiwiZW1pc3NpdmUiLCJlbWlzc2l2ZUludGVuc2l0eSIsInNwaGVyZSIsImNsb2NrIiwiQ2xvY2siLCJhbmltYXRlIiwiZWxhcHNlZFRpbWUiLCJnZXRFbGFwc2VkVGltZSIsIm1hdGVyaWFsIiwicm90YXRpb24iLCJzaW4iLCJjaGlsZHJlbiIsImZvckVhY2giLCJzaGFwZSIsImluZGV4IiwiY29zIiwiaW50ZW5zaXR5IiwibG9va0F0IiwicmVuZGVyIiwicmVxdWVzdEFuaW1hdGlvbkZyYW1lIiwiaGFuZGxlTW91c2VNb3ZlIiwiZXZlbnQiLCJjbGllbnRYIiwiY2xpZW50WSIsImhhbmRsZVJlc2l6ZSIsImFzcGVjdCIsInVwZGF0ZVByb2plY3Rpb25NYXRyaXgiLCJhZGRFdmVudExpc3RlbmVyIiwiY2FuY2VsQW5pbWF0aW9uRnJhbWUiLCJyZW1vdmVFdmVudExpc3RlbmVyIiwicmVtb3ZlQ2hpbGQiLCJjbGVhciIsImRpc3Bvc2UiLCJnZW9tZXRyeSIsImNoaWxkIiwiZXJyb3IiLCJzZWN0aW9uIiwiY2xhc3NOYW1lIiwiZGl2IiwicmVmIiwic3R5bGUiLCJ6SW5kZXgiLCJzcGFuIiwiaDEiLCJiciIsInAiLCJocmVmIiwic3ZnIiwiZmlsbCIsInN0cm9rZSIsInZpZXdCb3giLCJwYXRoIiwic3Ryb2tlTGluZWNhcCIsInN0cm9rZUxpbmVqb2luIiwic3Ryb2tlV2lkdGgiLCJkIiwibnVtYmVyIiwibGFiZWwiLCJtYXAiLCJzdGF0Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/HeroSection.tsx\n"));

/***/ })

});