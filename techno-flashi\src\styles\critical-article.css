/* Critical CSS for Article Pages - Optimized for LCP */
/* This CSS contains only the essential styles needed for above-the-fold content */

/* Reset and base styles */
*,*::before,*::after{box-sizing:border-box;border-width:0;border-style:solid;border-color:#e5e7eb}
*::before,*::after{--tw-content:''}
html{line-height:1.5;-webkit-text-size-adjust:100%;-moz-tab-size:4;tab-size:4;font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",<PERSON><PERSON>,"Noto Sans",sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji";font-feature-settings:normal;font-variation-settings:normal}
body{margin:0;line-height:inherit}

/* Critical layout styles */
.min-h-screen{min-height:100vh}
.bg-white{background-color:#fff}
.text-text-primary{color:#1f2937}

/* Header critical styles */
.header{position:sticky;top:0;z-index:50;background-color:#fff;border-bottom:1px solid #e5e7eb}
.container{max-width:1200px;margin:0 auto;padding:0 1rem}

/* Article header critical styles */
.article-header{padding:2rem 0;max-width:800px;margin:0 auto}
.article-title{font-size:2.5rem;font-weight:700;line-height:1.2;color:#111;margin-bottom:1rem}
.article-meta{color:#6b7280;font-size:0.875rem;margin-bottom:1.5rem}
.article-excerpt{font-size:1.125rem;line-height:1.6;color:#4b5563;margin-bottom:2rem}

/* Featured image critical styles */
.featured-image{width:100%;height:auto;border-radius:0.5rem;margin-bottom:2rem}

/* Article content critical styles */
.article-content{max-width:800px;margin:0 auto;padding:0 1rem}
.article-content h1,.article-content h2,.article-content h3{color:#111;font-weight:600;margin:1.5rem 0 1rem}
.article-content h1{font-size:2rem}
.article-content h2{font-size:1.5rem}
.article-content h3{font-size:1.25rem}
.article-content p{color:#374151;line-height:1.7;margin-bottom:1rem;font-size:1.1rem}
.article-content a{color:#2563eb;text-decoration:underline}
.article-content a:hover{color:#1d4ed8}

/* Responsive styles */
@media (max-width: 768px) {
  .article-title{font-size:1.875rem}
  .article-content{padding:0 0.75rem}
  .container{padding:0 0.75rem}
}

/* Font loading optimization */
@font-face{
  font-family:'Cairo';
  font-style:normal;
  font-weight:400;
  font-display:swap;
  src:url('https://fonts.gstatic.com/s/cairo/v28/SLXgc1nY6HkvalIhTp2mxdt0UX8.woff2') format('woff2');
  unicode-range:U+0600-06FF,U+200C-200E,U+2010-2011,U+204F,U+2E41,U+FB50-FDFF,U+FE80-FEFC;
}

/* Critical utilities */
.sr-only{position:absolute;width:1px;height:1px;padding:0;margin:-1px;overflow:hidden;clip:rect(0,0,0,0);white-space:nowrap;border-width:0}
.block{display:block}
.flex{display:flex}
.hidden{display:none}
.w-full{width:100%}
.h-auto{height:auto}
.max-w-none{max-width:none}
.mx-auto{margin-left:auto;margin-right:auto}
.mb-2{margin-bottom:0.5rem}
.mb-4{margin-bottom:1rem}
.mb-6{margin-bottom:1.5rem}
.mb-8{margin-bottom:2rem}
.p-4{padding:1rem}
.px-4{padding-left:1rem;padding-right:1rem}
.py-2{padding-top:0.5rem;padding-bottom:0.5rem}
.py-8{padding-top:2rem;padding-bottom:2rem}
.text-sm{font-size:0.875rem;line-height:1.25rem}
.text-base{font-size:1rem;line-height:1.5rem}
.text-lg{font-size:1.125rem;line-height:1.75rem}
.text-xl{font-size:1.25rem;line-height:1.75rem}
.text-2xl{font-size:1.5rem;line-height:2rem}
.text-3xl{font-size:1.875rem;line-height:2.25rem}
.font-medium{font-weight:500}
.font-semibold{font-weight:600}
.font-bold{font-weight:700}
.leading-tight{line-height:1.25}
.leading-relaxed{line-height:1.625}
.text-gray-600{color:#4b5563}
.text-gray-700{color:#374151}
.text-gray-900{color:#111827}
.rounded{border-radius:0.25rem}
.rounded-lg{border-radius:0.5rem}
.border{border-width:1px}
.border-gray-200{border-color:#e5e7eb}
.bg-gray-50{background-color:#f9fafb}
.shadow{box-shadow:0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)}

/* Performance optimizations */
img{max-width:100%;height:auto}
.article-content img{border-radius:0.5rem;margin:1rem 0}

/* Preload critical resources */
.preload-critical{
  content-visibility:auto;
  contain-intrinsic-size:0 500px;
}

/* Reduce layout shift */
.article-skeleton{
  background:linear-gradient(90deg,#f0f0f0 25%,transparent 37%,#f0f0f0 63%);
  background-size:400% 100%;
  animation:skeleton 1.5s ease-in-out infinite;
}

@keyframes skeleton{
  0%{background-position:100% 50%}
  100%{background-position:0% 50%}
}

/* Critical prose styles for article content */
.prose{color:#374151;max-width:65ch}
.prose p{margin-top:1.25em;margin-bottom:1.25em}
.prose img{margin-top:2em;margin-bottom:2em}
.prose h1,.prose h2,.prose h3,.prose h4{color:#111827;font-weight:600}
.prose h1{font-size:2.25em;margin-top:0;margin-bottom:0.8888889em;line-height:1.1111111}
.prose h2{font-size:1.5em;margin-top:2em;margin-bottom:1em;line-height:1.3333333}
.prose h3{font-size:1.25em;margin-top:1.6em;margin-bottom:0.6em;line-height:1.6}
.prose ul,.prose ol{margin-top:1.25em;margin-bottom:1.25em;padding-left:1.625em}
.prose li{margin-top:0.5em;margin-bottom:0.5em}
.prose blockquote{font-weight:500;font-style:italic;color:#111827;border-left-width:0.25rem;border-left-color:#e5e7eb;quotes:"\201C""\201D""\2018""\2019";margin-top:1.6em;margin-bottom:1.6em;padding-left:1em}
.prose code{color:#111827;font-weight:600;font-size:0.875em}
.prose pre{color:#e5e7eb;background-color:#1f2937;overflow-x:auto;font-weight:400;font-size:0.875em;line-height:1.7142857;margin-top:1.7142857em;margin-bottom:1.7142857em;border-radius:0.375rem;padding:0.8571429em 1.1428571em}

/* RTL support for Arabic content */
[dir="rtl"] .prose{text-align:right}
[dir="rtl"] .prose ul,[dir="rtl"] .prose ol{padding-right:1.625em;padding-left:0}
[dir="rtl"] .prose blockquote{border-right-width:0.25rem;border-right-color:#e5e7eb;border-left-width:0;padding-right:1em;padding-left:0}
