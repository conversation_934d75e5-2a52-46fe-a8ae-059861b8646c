"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/HeroSection.tsx":
/*!****************************************!*\
  !*** ./src/components/HeroSection.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HeroSection: () => (/* binding */ HeroSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! three */ \"(app-pages-browser)/./node_modules/three/build/three.core.js\");\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! three */ \"(app-pages-browser)/./node_modules/three/build/three.module.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ HeroSection auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction HeroSection() {\n    _s();\n    const mountRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [isWebGLSupported, setIsWebGLSupported] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const sceneRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    const rendererRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    const animationRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    const mouseRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        x: 0,\n        y: 0\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HeroSection.useEffect\": ()=>{\n            if (!mountRef.current) return;\n            let scene;\n            let camera;\n            let renderer;\n            let particles;\n            let geometricShapes;\n            let ambientLight;\n            let pointLight;\n            try {\n                // إعداد المشهد\n                scene = new three__WEBPACK_IMPORTED_MODULE_3__.Scene();\n                sceneRef.current = scene;\n                // إعداد الكاميرا\n                camera = new three__WEBPACK_IMPORTED_MODULE_3__.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);\n                camera.position.z = 5;\n                // إعداد المُرسِل مع معالجة الأخطاء\n                renderer = new three__WEBPACK_IMPORTED_MODULE_4__.WebGLRenderer({\n                    antialias: true,\n                    alpha: true,\n                    powerPreference: \"high-performance\",\n                    failIfMajorPerformanceCaveat: false,\n                    preserveDrawingBuffer: false\n                });\n                // فحص دعم WebGL\n                const gl = renderer.getContext();\n                if (!gl) {\n                    throw new Error('WebGL not supported');\n                }\n                renderer.setSize(window.innerWidth, window.innerHeight);\n                renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));\n                renderer.setClearColor(0x000000, 0);\n                rendererRef.current = renderer;\n                mountRef.current.appendChild(renderer.domElement);\n                // إعداد الإضاءة\n                ambientLight = new three__WEBPACK_IMPORTED_MODULE_3__.AmbientLight(0x8b5cf6, 0.4);\n                scene.add(ambientLight);\n                pointLight = new three__WEBPACK_IMPORTED_MODULE_3__.PointLight(0xa855f7, 1, 100);\n                pointLight.position.set(10, 10, 10);\n                scene.add(pointLight);\n                // إنشاء جسيمات ثلاثية الأبعاد\n                const particleCount = 1000;\n                const positions = new Float32Array(particleCount * 3);\n                const colors = new Float32Array(particleCount * 3);\n                const sizes = new Float32Array(particleCount);\n                const colorPalette = [\n                    new three__WEBPACK_IMPORTED_MODULE_3__.Color(0x8b5cf6),\n                    new three__WEBPACK_IMPORTED_MODULE_3__.Color(0xa855f7),\n                    new three__WEBPACK_IMPORTED_MODULE_3__.Color(0xc084fc),\n                    new three__WEBPACK_IMPORTED_MODULE_3__.Color(0xd8b4fe)\n                ];\n                for(let i = 0; i < particleCount; i++){\n                    // المواقع العشوائية\n                    positions[i * 3] = (Math.random() - 0.5) * 20;\n                    positions[i * 3 + 1] = (Math.random() - 0.5) * 20;\n                    positions[i * 3 + 2] = (Math.random() - 0.5) * 20;\n                    // الألوان العشوائية\n                    const color = colorPalette[Math.floor(Math.random() * colorPalette.length)];\n                    colors[i * 3] = color.r;\n                    colors[i * 3 + 1] = color.g;\n                    colors[i * 3 + 2] = color.b;\n                    // الأحجام العشوائية\n                    sizes[i] = Math.random() * 3 + 1;\n                }\n                const particleGeometry = new three__WEBPACK_IMPORTED_MODULE_3__.BufferGeometry();\n                particleGeometry.setAttribute('position', new three__WEBPACK_IMPORTED_MODULE_3__.BufferAttribute(positions, 3));\n                particleGeometry.setAttribute('color', new three__WEBPACK_IMPORTED_MODULE_3__.BufferAttribute(colors, 3));\n                particleGeometry.setAttribute('size', new three__WEBPACK_IMPORTED_MODULE_3__.BufferAttribute(sizes, 1));\n                const particleMaterial = new three__WEBPACK_IMPORTED_MODULE_3__.ShaderMaterial({\n                    uniforms: {\n                        time: {\n                            value: 0\n                        },\n                        mouse: {\n                            value: new three__WEBPACK_IMPORTED_MODULE_3__.Vector2()\n                        }\n                    },\n                    vertexShader: \"\\n          attribute float size;\\n          attribute vec3 color;\\n          varying vec3 vColor;\\n          uniform float time;\\n          uniform vec2 mouse;\\n\\n          void main() {\\n            vColor = color;\\n            vec3 pos = position;\\n\\n            // تأثير الموجة\\n            pos.y += sin(pos.x * 0.5 + time) * 0.5;\\n            pos.x += cos(pos.z * 0.5 + time) * 0.3;\\n\\n            // تأثير الماوس\\n            vec2 mouseInfluence = mouse * 0.1;\\n            pos.xy += mouseInfluence * (1.0 - length(pos.xy) * 0.1);\\n\\n            vec4 mvPosition = modelViewMatrix * vec4(pos, 1.0);\\n            gl_PointSize = size * (300.0 / max(-mvPosition.z, 1.0));\\n            gl_Position = projectionMatrix * mvPosition;\\n          }\\n        \",\n                    fragmentShader: \"\\n          varying vec3 vColor;\\n\\n          void main() {\\n            float distanceToCenter = distance(gl_PointCoord, vec2(0.5));\\n            float alpha = 1.0 - smoothstep(0.0, 0.5, distanceToCenter);\\n            gl_FragColor = vec4(vColor, alpha * 0.8);\\n          }\\n        \",\n                    transparent: true,\n                    vertexColors: true,\n                    blending: three__WEBPACK_IMPORTED_MODULE_3__.AdditiveBlending\n                });\n                particles = new three__WEBPACK_IMPORTED_MODULE_3__.Points(particleGeometry, particleMaterial);\n                scene.add(particles);\n                // إنشاء أشكال هندسية معقدة\n                geometricShapes = new three__WEBPACK_IMPORTED_MODULE_3__.Group();\n                // مكعبات متحركة\n                for(let i = 0; i < 5; i++){\n                    const cubeGeometry = new three__WEBPACK_IMPORTED_MODULE_3__.BoxGeometry(0.5, 0.5, 0.5);\n                    const cubeMaterial = new three__WEBPACK_IMPORTED_MODULE_3__.MeshPhongMaterial({\n                        color: colorPalette[i % colorPalette.length],\n                        transparent: true,\n                        opacity: 0.3,\n                        wireframe: true\n                    });\n                    const cube = new three__WEBPACK_IMPORTED_MODULE_3__.Mesh(cubeGeometry, cubeMaterial);\n                    cube.position.set((Math.random() - 0.5) * 10, (Math.random() - 0.5) * 10, (Math.random() - 0.5) * 10);\n                    geometricShapes.add(cube);\n                }\n                // كرات متوهجة\n                for(let i = 0; i < 3; i++){\n                    const sphereGeometry = new three__WEBPACK_IMPORTED_MODULE_3__.SphereGeometry(0.3, 16, 16);\n                    const sphereMaterial = new three__WEBPACK_IMPORTED_MODULE_3__.MeshPhongMaterial({\n                        color: colorPalette[(i + 2) % colorPalette.length],\n                        transparent: true,\n                        opacity: 0.6,\n                        emissive: colorPalette[(i + 2) % colorPalette.length],\n                        emissiveIntensity: 0.2\n                    });\n                    const sphere = new three__WEBPACK_IMPORTED_MODULE_3__.Mesh(sphereGeometry, sphereMaterial);\n                    sphere.position.set((Math.random() - 0.5) * 8, (Math.random() - 0.5) * 8, (Math.random() - 0.5) * 8);\n                    geometricShapes.add(sphere);\n                }\n                scene.add(geometricShapes);\n                // حلقة الرسم والتحريك\n                const clock = new three__WEBPACK_IMPORTED_MODULE_3__.Clock();\n                const animate = {\n                    \"HeroSection.useEffect.animate\": ()=>{\n                        const elapsedTime = clock.getElapsedTime();\n                        // تحريك الجسيمات\n                        if (particles && particles.material) {\n                            particles.material.uniforms.time.value = elapsedTime;\n                            particles.material.uniforms.mouse.value.set(mouseRef.current.x, mouseRef.current.y);\n                        }\n                        // تدوير الجسيمات\n                        if (particles) {\n                            particles.rotation.y = elapsedTime * 0.05;\n                            particles.rotation.x = Math.sin(elapsedTime * 0.03) * 0.1;\n                        }\n                        // تحريك الأشكال الهندسية\n                        geometricShapes.children.forEach({\n                            \"HeroSection.useEffect.animate\": (shape, index)=>{\n                                if (shape instanceof three__WEBPACK_IMPORTED_MODULE_3__.Mesh) {\n                                    // دوران مختلف لكل شكل\n                                    shape.rotation.x += 0.01 * (index + 1);\n                                    shape.rotation.y += 0.015 * (index + 1);\n                                    // حركة تموجية\n                                    shape.position.y += Math.sin(elapsedTime + index) * 0.002;\n                                    shape.position.x += Math.cos(elapsedTime * 0.5 + index) * 0.001;\n                                }\n                            }\n                        }[\"HeroSection.useEffect.animate\"]);\n                        // تحريك الإضاءة\n                        pointLight.position.x = Math.sin(elapsedTime) * 5;\n                        pointLight.position.z = Math.cos(elapsedTime) * 5;\n                        pointLight.intensity = 0.8 + Math.sin(elapsedTime * 2) * 0.2;\n                        // تأثير الماوس على الكاميرا\n                        camera.position.x += (mouseRef.current.x * 0.001 - camera.position.x) * 0.05;\n                        camera.position.y += (-mouseRef.current.y * 0.001 - camera.position.y) * 0.05;\n                        camera.lookAt(scene.position);\n                        renderer.render(scene, camera);\n                        animationRef.current = requestAnimationFrame(animate);\n                    }\n                }[\"HeroSection.useEffect.animate\"];\n                animate();\n                // معالج حركة الماوس\n                const handleMouseMove = {\n                    \"HeroSection.useEffect.handleMouseMove\": (event)=>{\n                        mouseRef.current.x = event.clientX / window.innerWidth * 2 - 1;\n                        mouseRef.current.y = -(event.clientY / window.innerHeight) * 2 + 1;\n                    }\n                }[\"HeroSection.useEffect.handleMouseMove\"];\n                // معالج تغيير حجم النافذة\n                const handleResize = {\n                    \"HeroSection.useEffect.handleResize\": ()=>{\n                        camera.aspect = window.innerWidth / window.innerHeight;\n                        camera.updateProjectionMatrix();\n                        renderer.setSize(window.innerWidth, window.innerHeight);\n                        renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));\n                    }\n                }[\"HeroSection.useEffect.handleResize\"];\n                // إضافة المستمعين\n                window.addEventListener('mousemove', handleMouseMove);\n                window.addEventListener('resize', handleResize);\n                // التنظيف\n                return ({\n                    \"HeroSection.useEffect\": ()=>{\n                        if (animationRef.current) {\n                            cancelAnimationFrame(animationRef.current);\n                        }\n                        window.removeEventListener('mousemove', handleMouseMove);\n                        window.removeEventListener('resize', handleResize);\n                        // تنظيف Three.js\n                        if (mountRef.current && renderer.domElement) {\n                            mountRef.current.removeChild(renderer.domElement);\n                        }\n                        // تنظيف الذاكرة\n                        scene.clear();\n                        renderer.dispose();\n                        // تنظيف الهندسة والمواد\n                        particles.geometry.dispose();\n                        particles.material.dispose();\n                        geometricShapes.children.forEach({\n                            \"HeroSection.useEffect\": (child)=>{\n                                if (child instanceof three__WEBPACK_IMPORTED_MODULE_3__.Mesh) {\n                                    child.geometry.dispose();\n                                    child.material.dispose();\n                                }\n                            }\n                        }[\"HeroSection.useEffect\"]);\n                    }\n                })[\"HeroSection.useEffect\"];\n            } catch (error) {\n                console.warn('WebGL/Three.js initialization failed:', error);\n                setIsWebGLSupported(false);\n            }\n        }\n    }[\"HeroSection.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900\",\n        children: [\n            isWebGLSupported && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: mountRef,\n                className: \"absolute inset-0 w-full h-full\",\n                style: {\n                    zIndex: 1\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                lineNumber: 304,\n                columnNumber: 9\n            }, this),\n            !isWebGLSupported && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-purple-600/20 via-pink-600/20 to-purple-600/20\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                        lineNumber: 314,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(139,92,246,0.1),transparent_70%)]\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                        lineNumber: 315,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-[conic-gradient(from_0deg_at_50%_50%,rgba(168,85,247,0.1),rgba(192,132,252,0.1),rgba(168,85,247,0.1))]\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                        lineNumber: 316,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                lineNumber: 313,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-t from-slate-900/50 via-transparent to-slate-900/30\",\n                style: {\n                    zIndex: 2\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                lineNumber: 321,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 container mx-auto px-6 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"inline-flex items-center px-6 py-3 bg-white/10 backdrop-blur-md text-purple-200 text-sm font-semibold rounded-full mb-8 border border-purple-300/30 shadow-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"mr-2 text-lg\",\n                                children: \"✨\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                                lineNumber: 327,\n                                columnNumber: 11\n                            }, this),\n                            \"بوابتك للمستقبل التقني\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                        lineNumber: 326,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-5xl md:text-7xl lg:text-8xl font-bold text-white leading-tight mb-6\",\n                        children: [\n                            \"مستقبلك التقني\",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                                lineNumber: 334,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-transparent bg-clip-text bg-gradient-to-r from-purple-400 via-pink-400 to-purple-600 animate-pulse\",\n                                children: \"يبدأ من هنا\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                                lineNumber: 335,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                        lineNumber: 332,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"max-w-4xl mx-auto text-xl md:text-2xl text-gray-300 mb-12 leading-relaxed font-light\",\n                        children: \"اكتشف أحدث التقنيات، أدوات الذكاء الاصطناعي المتطورة، ومقالات تقنية متخصصة لتطوير مهاراتك وتحقيق أهدافك في عالم التكنولوجيا المتطور.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                        lineNumber: 341,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row justify-center items-center gap-6 mb-20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/articles\",\n                                className: \"group relative bg-gradient-to-r from-purple-600 to-pink-600 text-white px-10 py-5 rounded-2xl shadow-2xl hover:shadow-purple-500/25 transition-all duration-500 transform hover:scale-105 font-semibold text-lg min-w-[220px] overflow-hidden\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-gradient-to-r from-purple-700 to-pink-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                                        lineNumber: 352,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"relative flex items-center justify-center\",\n                                        children: [\n                                            \"ابدأ الاستكشاف\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-6 h-6 mr-3 group-hover:translate-x-1 transition-transform duration-300\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M15 19l-7-7 7-7\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                                                    lineNumber: 356,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                                                lineNumber: 355,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                                        lineNumber: 353,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                                lineNumber: 348,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/ai-tools\",\n                                className: \"group relative bg-white/10 backdrop-blur-md text-white px-10 py-5 rounded-2xl border border-white/30 hover:bg-white/20 hover:border-purple-400/50 transition-all duration-500 font-semibold text-lg min-w-[220px] shadow-xl\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"flex items-center justify-center\",\n                                    children: [\n                                        \"أدوات الذكاء الاصطناعي\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-6 h-6 mr-3 group-hover:translate-x-1 transition-transform duration-300\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M13 10V3L4 14h7v7l9-11h-7z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                                                lineNumber: 368,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                                            lineNumber: 367,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                                    lineNumber: 365,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                                lineNumber: 361,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                        lineNumber: 347,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-4 gap-8 max-w-5xl mx-auto\",\n                        children: [\n                            {\n                                number: \"500+\",\n                                label: \"مقال تقني\"\n                            },\n                            {\n                                number: \"50+\",\n                                label: \"أداة ذكية\"\n                            },\n                            {\n                                number: \"10K+\",\n                                label: \"قارئ نشط\"\n                            },\n                            {\n                                number: \"24/7\",\n                                label: \"دعم فني\"\n                            }\n                        ].map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center group\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-4xl md:text-5xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-pink-400 mb-3 group-hover:scale-110 transition-transform duration-300\",\n                                        children: stat.number\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                                        lineNumber: 383,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm md:text-base font-medium\",\n                                        children: stat.label\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                                        lineNumber: 386,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                                lineNumber: 382,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                        lineNumber: 375,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                lineNumber: 324,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-8 left-1/2 transform -translate-x-1/2 z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-bounce\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-8 h-8 border-2 border-white/40 rounded-full flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-4 h-4 text-white/60\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M19 14l-7 7m0 0l-7-7m7 7V3\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                                lineNumber: 399,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                            lineNumber: 398,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                        lineNumber: 397,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                    lineNumber: 396,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                lineNumber: 395,\n                columnNumber: 7\n            }, this),\n            isWebGLSupported && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-4 right-4 z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-green-500/20 text-green-400 px-3 py-1 rounded-full text-xs font-medium border border-green-500/30\",\n                    children: \"✓ WebGL Active\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                    lineNumber: 408,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                lineNumber: 407,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n        lineNumber: 301,\n        columnNumber: 5\n    }, this);\n}\n_s(HeroSection, \"kYS9FaXFF20tBPBsKpAJ4sL0XtE=\");\n_c = HeroSection;\nvar _c;\n$RefreshReg$(_c, \"HeroSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/HeroSection.tsx\n"));

/***/ })

});