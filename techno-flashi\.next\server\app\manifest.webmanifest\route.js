/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/manifest.webmanifest/route";
exports.ids = ["app/manifest.webmanifest/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fmanifest.webmanifest%2Froute&page=%2Fmanifest.webmanifest%2Froute&appPaths=&pagePath=private-next-app-dir%2Fmanifest.ts&appDir=C%3A%5CUsers%5Cismail%5CDownloads%5C11111111111111%5Ctechno-flashi%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cismail%5CDownloads%5C11111111111111%5Ctechno-flashi&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fmanifest.webmanifest%2Froute&page=%2Fmanifest.webmanifest%2Froute&appPaths=&pagePath=private-next-app-dir%2Fmanifest.ts&appDir=C%3A%5CUsers%5Cismail%5CDownloads%5C11111111111111%5Ctechno-flashi%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cismail%5CDownloads%5C11111111111111%5Ctechno-flashi&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_metadata_route_loader_filePath_C_3A_5CUsers_5Cismail_5CDownloads_5C11111111111111_5Ctechno_flashi_5Csrc_5Capp_5Cmanifest_ts_isDynamicRouteExtension_1_next_metadata_route___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-metadata-route-loader?filePath=C%3A%5CUsers%5Cismail%5CDownloads%5C11111111111111%5Ctechno-flashi%5Csrc%5Capp%5Cmanifest.ts&isDynamicRouteExtension=1!?__next_metadata_route__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-route-loader.js?filePath=C%3A%5CUsers%5Cismail%5CDownloads%5C11111111111111%5Ctechno-flashi%5Csrc%5Capp%5Cmanifest.ts&isDynamicRouteExtension=1!?__next_metadata_route__\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"standalone\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/manifest.webmanifest/route\",\n        pathname: \"/manifest.webmanifest\",\n        filename: \"manifest\",\n        bundlePath: \"app/manifest.webmanifest/route\"\n    },\n    resolvedPagePath: \"next-metadata-route-loader?filePath=C%3A%5CUsers%5Cismail%5CDownloads%5C11111111111111%5Ctechno-flashi%5Csrc%5Capp%5Cmanifest.ts&isDynamicRouteExtension=1!?__next_metadata_route__\",\n    nextConfigOutput,\n    userland: next_metadata_route_loader_filePath_C_3A_5CUsers_5Cismail_5CDownloads_5C11111111111111_5Ctechno_flashi_5Csrc_5Capp_5Cmanifest_ts_isDynamicRouteExtension_1_next_metadata_route___WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fmanifest.webmanifest%2Froute&page=%2Fmanifest.webmanifest%2Froute&appPaths=&pagePath=private-next-app-dir%2Fmanifest.ts&appDir=C%3A%5CUsers%5Cismail%5CDownloads%5C11111111111111%5Ctechno-flashi%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cismail%5CDownloads%5C11111111111111%5Ctechno-flashi&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-route-loader.js?filePath=C%3A%5CUsers%5Cismail%5CDownloads%5C11111111111111%5Ctechno-flashi%5Csrc%5Capp%5Cmanifest.ts&isDynamicRouteExtension=1!?__next_metadata_route__":
/*!*********************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-route-loader.js?filePath=C%3A%5CUsers%5Cismail%5CDownloads%5C11111111111111%5Ctechno-flashi%5Csrc%5Capp%5Cmanifest.ts&isDynamicRouteExtension=1!?__next_metadata_route__ ***!
  \*********************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var C_Users_ismail_Downloads_11111111111111_techno_flashi_src_app_manifest_ts__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./src/app/manifest.ts */ \"(rsc)/./src/app/manifest.ts\");\n/* harmony import */ var next_dist_build_webpack_loaders_metadata_resolve_route_data__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/webpack/loaders/metadata/resolve-route-data */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/metadata/resolve-route-data.js\");\n/* harmony import */ var next_dist_build_webpack_loaders_metadata_resolve_route_data__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_build_webpack_loaders_metadata_resolve_route_data__WEBPACK_IMPORTED_MODULE_2__);\n/* dynamic asset route */\n\n\n\n\nconst contentType = \"application/manifest+json\"\nconst fileType = \"manifest\"\n\n\n  if (typeof C_Users_ismail_Downloads_11111111111111_techno_flashi_src_app_manifest_ts__WEBPACK_IMPORTED_MODULE_1__[\"default\"] !== 'function') {\n    throw new Error('Default export is missing in \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\app\\\\manifest.ts\"')\n  }\n  \n\n\nasync function GET() {\n  const data = await (0,C_Users_ismail_Downloads_11111111111111_techno_flashi_src_app_manifest_ts__WEBPACK_IMPORTED_MODULE_1__[\"default\"])()\n  const content = (0,next_dist_build_webpack_loaders_metadata_resolve_route_data__WEBPACK_IMPORTED_MODULE_2__.resolveRouteData)(data, fileType)\n\n  return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(content, {\n    headers: {\n      'Content-Type': contentType,\n      'Cache-Control': \"public, max-age=0, must-revalidate\",\n    },\n  })\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLXJvdXRlLWxvYWRlci5qcz9maWxlUGF0aD1DJTNBJTVDVXNlcnMlNUNpc21haWwlNUNEb3dubG9hZHMlNUMxMTExMTExMTExMTExMSU1Q3RlY2huby1mbGFzaGklNUNzcmMlNUNhcHAlNUNtYW5pZmVzdC50cyZpc0R5bmFtaWNSb3V0ZUV4dGVuc2lvbj0xIT9fX25leHRfbWV0YWRhdGFfcm91dGVfXyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFBO0FBQzBDO0FBQzhEO0FBQ1Y7O0FBRTlGO0FBQ0E7OztBQUdBLGFBQWEsaUhBQU87QUFDcEI7QUFDQTtBQUNBOzs7QUFHTztBQUNQLHFCQUFxQixxSEFBTztBQUM1QixrQkFBa0IsNkdBQWdCOztBQUVsQyxhQUFhLHFEQUFZO0FBQ3pCO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTCxHQUFHO0FBQ0giLCJzb3VyY2VzIjpbIj9fX25leHRfbWV0YWRhdGFfcm91dGVfXyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKiBkeW5hbWljIGFzc2V0IHJvdXRlICovXG5pbXBvcnQgeyBOZXh0UmVzcG9uc2UgfSBmcm9tICduZXh0L3NlcnZlcidcbmltcG9ydCBoYW5kbGVyIGZyb20gXCJDOlxcXFxVc2Vyc1xcXFxpc21haWxcXFxcRG93bmxvYWRzXFxcXDExMTExMTExMTExMTExXFxcXHRlY2huby1mbGFzaGlcXFxcc3JjXFxcXGFwcFxcXFxtYW5pZmVzdC50c1wiXG5pbXBvcnQgeyByZXNvbHZlUm91dGVEYXRhIH0gZnJvbSAnbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9tZXRhZGF0YS9yZXNvbHZlLXJvdXRlLWRhdGEnXG5cbmNvbnN0IGNvbnRlbnRUeXBlID0gXCJhcHBsaWNhdGlvbi9tYW5pZmVzdCtqc29uXCJcbmNvbnN0IGZpbGVUeXBlID0gXCJtYW5pZmVzdFwiXG5cblxuICBpZiAodHlwZW9mIGhhbmRsZXIgIT09ICdmdW5jdGlvbicpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ0RlZmF1bHQgZXhwb3J0IGlzIG1pc3NpbmcgaW4gXCJDOlxcXFxVc2Vyc1xcXFxpc21haWxcXFxcRG93bmxvYWRzXFxcXDExMTExMTExMTExMTExXFxcXHRlY2huby1mbGFzaGlcXFxcc3JjXFxcXGFwcFxcXFxtYW5pZmVzdC50c1wiJylcbiAgfVxuICBcblxuXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gR0VUKCkge1xuICBjb25zdCBkYXRhID0gYXdhaXQgaGFuZGxlcigpXG4gIGNvbnN0IGNvbnRlbnQgPSByZXNvbHZlUm91dGVEYXRhKGRhdGEsIGZpbGVUeXBlKVxuXG4gIHJldHVybiBuZXcgTmV4dFJlc3BvbnNlKGNvbnRlbnQsIHtcbiAgICBoZWFkZXJzOiB7XG4gICAgICAnQ29udGVudC1UeXBlJzogY29udGVudFR5cGUsXG4gICAgICAnQ2FjaGUtQ29udHJvbCc6IFwicHVibGljLCBtYXgtYWdlPTAsIG11c3QtcmV2YWxpZGF0ZVwiLFxuICAgIH0sXG4gIH0pXG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-route-loader.js?filePath=C%3A%5CUsers%5Cismail%5CDownloads%5C11111111111111%5Ctechno-flashi%5Csrc%5Capp%5Cmanifest.ts&isDynamicRouteExtension=1!?__next_metadata_route__\n");

/***/ }),

/***/ "(rsc)/./src/app/manifest.ts":
/*!*****************************!*\
  !*** ./src/app/manifest.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ manifest)\n/* harmony export */ });\nfunction manifest() {\n    return {\n        name: 'TechnoFlash - بوابتك للمستقبل التقني',\n        short_name: 'TechnoFlash',\n        description: 'منصة ويب متكاملة تقدم مقالات تقنية حصرية، ودليل شامل لأدوات الذكاء الاصطناعي، وخدمات متخصصة',\n        start_url: '/',\n        display: 'standalone',\n        background_color: '#0f172a',\n        theme_color: '#38bdf8',\n        orientation: 'portrait',\n        scope: '/',\n        lang: 'ar',\n        dir: 'rtl',\n        icons: [\n            {\n                src: '/favicon.ico',\n                sizes: '48x48',\n                type: 'image/x-icon'\n            },\n            {\n                src: '/icon-192x192.svg',\n                sizes: '192x192',\n                type: 'image/svg+xml'\n            },\n            {\n                src: '/icon-512x512.svg',\n                sizes: '512x512',\n                type: 'image/svg+xml'\n            }\n        ],\n        categories: [\n            'technology',\n            'education',\n            'productivity'\n        ],\n        shortcuts: [\n            {\n                name: 'المقالات',\n                short_name: 'مقالات',\n                description: 'تصفح أحدث المقالات التقنية',\n                url: '/articles',\n                icons: [\n                    {\n                        src: '/icon-192x192.svg',\n                        sizes: '192x192'\n                    }\n                ]\n            },\n            {\n                name: 'أدوات الذكاء الاصطناعي',\n                short_name: 'أدوات AI',\n                description: 'اكتشف أدوات الذكاء الاصطناعي',\n                url: '/ai-tools',\n                icons: [\n                    {\n                        src: '/icon-192x192.svg',\n                        sizes: '192x192'\n                    }\n                ]\n            },\n            {\n                name: 'الخدمات',\n                short_name: 'خدمات',\n                description: 'تصفح خدماتنا التقنية',\n                url: '/services',\n                icons: [\n                    {\n                        src: '/icon-192x192.svg',\n                        sizes: '192x192'\n                    }\n                ]\n            }\n        ]\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/manifest.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fmanifest.webmanifest%2Froute&page=%2Fmanifest.webmanifest%2Froute&appPaths=&pagePath=private-next-app-dir%2Fmanifest.ts&appDir=C%3A%5CUsers%5Cismail%5CDownloads%5C11111111111111%5Ctechno-flashi%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cismail%5CDownloads%5C11111111111111%5Ctechno-flashi&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();