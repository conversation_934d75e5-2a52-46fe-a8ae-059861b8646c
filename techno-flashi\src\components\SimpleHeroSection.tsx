'use client';

import { useEffect, useRef, useState } from 'react';
import Link from 'next/link';

export function SimpleHeroSection() {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number>();
  const mouseRef = useRef({ x: 0, y: 0 });
  const particlesRef = useRef<Array<{
    x: number;
    y: number;
    vx: number;
    vy: number;
    size: number;
    color: string;
    opacity: number;
  }>>([]);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // إعداد الكانفاس
    const resizeCanvas = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    };

    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);

    // إنشاء الجسيمات المحسنة للثيم الفاتح
    const createParticles = () => {
      const particles = [];
      const colors = ['#8b5cf6', '#a855f7', '#c084fc', '#d8b4fe', '#e879f9', '#f0abfc', '#fbbf24', '#f59e0b'];

      for (let i = 0; i < 100; i++) {
        particles.push({
          x: Math.random() * canvas.width,
          y: Math.random() * canvas.height,
          vx: (Math.random() - 0.5) * 1.2,
          vy: (Math.random() - 0.5) * 1.2,
          size: Math.random() * 5 + 2,
          color: colors[Math.floor(Math.random() * colors.length)],
          opacity: Math.random() * 0.4 + 0.1, // شفافية أكثر للثيم الفاتح
          pulse: Math.random() * Math.PI * 2,
          pulseSpeed: 0.015 + Math.random() * 0.025,
          rotationSpeed: (Math.random() - 0.5) * 0.02
        });
      }

      particlesRef.current = particles;
    };

    createParticles();

    // معالج حركة الماوس
    const handleMouseMove = (e: MouseEvent) => {
      mouseRef.current = {
        x: (e.clientX / window.innerWidth) * 2 - 1,
        y: -(e.clientY / window.innerHeight) * 2 + 1
      };
    };

    window.addEventListener('mousemove', handleMouseMove);

    // حلقة الرسم المحسنة
    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      // رسم الشبكة المتحركة للثيم الفاتح
      const time = Date.now() * 0.001;
      const gridSize = 80;

      // شبكة ناعمة مع تأثير موجي
      ctx.strokeStyle = `rgba(139, 92, 246, ${0.03 + Math.sin(time) * 0.01})`;
      ctx.lineWidth = 0.5;

      for (let x = 0; x < canvas.width; x += gridSize) {
        const offset = Math.sin(time + x * 0.008) * 8;
        ctx.beginPath();
        ctx.moveTo(x + offset, 0);
        ctx.lineTo(x + offset, canvas.height);
        ctx.stroke();
      }

      for (let y = 0; y < canvas.height; y += gridSize) {
        const offset = Math.cos(time + y * 0.008) * 6;
        ctx.beginPath();
        ctx.moveTo(0, y + offset);
        ctx.lineTo(canvas.width, y + offset);
        ctx.stroke();
      }

      // رسم دوائر متوهجة ناعمة
      const glowCircles = 5;
      for (let i = 0; i < glowCircles; i++) {
        const x = canvas.width * (0.1 + i * 0.2);
        const y = canvas.height * (0.2 + Math.sin(time * 0.3 + i) * 0.3);
        const radius = 150 + Math.sin(time * 0.4 + i) * 50;

        const gradient = ctx.createRadialGradient(x, y, 0, x, y, radius);
        const colors = [
          `rgba(168, 85, 247, ${0.05 + Math.sin(time + i) * 0.02})`,
          `rgba(236, 72, 153, ${0.03 + Math.sin(time + i + 1) * 0.015})`,
          `rgba(251, 191, 36, ${0.02 + Math.sin(time + i + 2) * 0.01})`
        ];

        gradient.addColorStop(0, colors[i % colors.length]);
        gradient.addColorStop(0.7, colors[(i + 1) % colors.length].replace(/[\d.]+\)/, '0.01)'));
        gradient.addColorStop(1, 'rgba(255, 255, 255, 0)');

        ctx.fillStyle = gradient;
        ctx.beginPath();
        ctx.arc(x, y, radius, 0, Math.PI * 2);
        ctx.fill();
      }

      // رسم أشكال هندسية متحركة
      const shapes = 3;
      for (let i = 0; i < shapes; i++) {
        const x = canvas.width * (0.3 + i * 0.2);
        const y = canvas.height * (0.4 + Math.cos(time * 0.2 + i) * 0.2);
        const size = 30 + Math.sin(time + i) * 10;
        const rotation = time * 0.5 + i;

        ctx.save();
        ctx.translate(x, y);
        ctx.rotate(rotation);

        const gradient = ctx.createLinearGradient(-size, -size, size, size);
        gradient.addColorStop(0, `rgba(168, 85, 247, ${0.1 + Math.sin(time + i) * 0.05})`);
        gradient.addColorStop(1, `rgba(236, 72, 153, ${0.05 + Math.cos(time + i) * 0.03})`);

        ctx.fillStyle = gradient;
        ctx.beginPath();
        ctx.roundRect(-size/2, -size/2, size, size, 8);
        ctx.fill();

        ctx.restore();
      }

      // تحديث ورسم الجسيمات المحسنة
      particlesRef.current.forEach((particle, index) => {
        // تحديث الموضع
        particle.x += particle.vx;
        particle.y += particle.vy;

        // تحديث التأثير النابض
        particle.pulse += particle.pulseSpeed;

        // تأثير الماوس المحسن
        const mouseInfluence = 80;
        const dx = (mouseRef.current.x * canvas.width * 0.5 + canvas.width * 0.5) - particle.x;
        const dy = (mouseRef.current.y * canvas.height * 0.5 + canvas.height * 0.5) - particle.y;
        const distance = Math.sqrt(dx * dx + dy * dy);

        if (distance < mouseInfluence) {
          const force = (mouseInfluence - distance) / mouseInfluence;
          particle.vx += dx * force * 0.002;
          particle.vy += dy * force * 0.002;
        }

        // حدود الشاشة مع ارتداد ناعم
        if (particle.x < 0 || particle.x > canvas.width) {
          particle.vx *= -0.8;
          particle.x = Math.max(0, Math.min(canvas.width, particle.x));
        }
        if (particle.y < 0 || particle.y > canvas.height) {
          particle.vy *= -0.8;
          particle.y = Math.max(0, Math.min(canvas.height, particle.y));
        }

        // رسم الجسيمة مع تأثير متوهج
        const pulseSize = particle.size + Math.sin(particle.pulse) * 1;
        const pulseOpacity = particle.opacity + Math.sin(particle.pulse) * 0.2;

        // رسم الهالة
        const gradient = ctx.createRadialGradient(
          particle.x, particle.y, 0,
          particle.x, particle.y, pulseSize * 3
        );
        gradient.addColorStop(0, particle.color.replace(')', ', 0.3)').replace('rgb', 'rgba'));
        gradient.addColorStop(1, particle.color.replace(')', ', 0)').replace('rgb', 'rgba'));

        ctx.fillStyle = gradient;
        ctx.globalAlpha = pulseOpacity * 0.5;
        ctx.beginPath();
        ctx.arc(particle.x, particle.y, pulseSize * 3, 0, Math.PI * 2);
        ctx.fill();

        // رسم الجسيمة الأساسية
        ctx.beginPath();
        ctx.arc(particle.x, particle.y, pulseSize, 0, Math.PI * 2);
        ctx.fillStyle = particle.color;
        ctx.globalAlpha = pulseOpacity;
        ctx.fill();

        // رسم خطوط الاتصال المحسنة
        particlesRef.current.forEach((otherParticle, otherIndex) => {
          if (index !== otherIndex && index < otherIndex) { // تجنب الرسم المكرر
            const dx = particle.x - otherParticle.x;
            const dy = particle.y - otherParticle.y;
            const distance = Math.sqrt(dx * dx + dy * dy);

            if (distance < 120) {
              const opacity = 0.15 * (1 - distance / 120);
              const gradient = ctx.createLinearGradient(
                particle.x, particle.y,
                otherParticle.x, otherParticle.y
              );
              gradient.addColorStop(0, `rgba(168, 85, 247, ${opacity})`);
              gradient.addColorStop(0.5, `rgba(192, 132, 252, ${opacity * 1.5})`);
              gradient.addColorStop(1, `rgba(168, 85, 247, ${opacity})`);

              ctx.beginPath();
              ctx.moveTo(particle.x, particle.y);
              ctx.lineTo(otherParticle.x, otherParticle.y);
              ctx.strokeStyle = gradient;
              ctx.lineWidth = 1 + opacity * 2;
              ctx.stroke();
            }
          }
        });
      });

      ctx.globalAlpha = 1;
      animationRef.current = requestAnimationFrame(animate);
    };

    animate();

    // التنظيف
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
      window.removeEventListener('resize', resizeCanvas);
      window.removeEventListener('mousemove', handleMouseMove);
    };
  }, []);

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-white via-purple-50 to-pink-50">
      {/* Canvas للخلفية التفاعلية */}
      <canvas
        ref={canvasRef}
        className="absolute inset-0 w-full h-full"
        style={{ zIndex: 1 }}
      />

      {/* طبقة تأثير إضافية للثيم الفاتح */}
      <div className="absolute inset-0 bg-gradient-to-t from-white/30 via-transparent to-purple-50/20" style={{ zIndex: 2 }} />

      {/* المحتوى الرئيسي */}
      <div className="relative z-10 text-center px-4 max-w-6xl mx-auto">
        <div className="mb-8">
          <h1 className="text-5xl md:text-7xl font-bold text-gray-900 mb-6 leading-tight">
            <span className="bg-gradient-to-r from-purple-600 via-pink-600 to-purple-600 bg-clip-text text-transparent">
              TechnoFlash
            </span>
          </h1>
          <p className="text-xl md:text-2xl text-gray-700 mb-8 max-w-3xl mx-auto leading-relaxed">
            منصتك الشاملة لأحدث التقنيات وأدوات الذكاء الاصطناعي
          </p>
        </div>

        {/* الأزرار */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
          <Link
            href="/articles"
            className="group bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-4 rounded-xl font-semibold text-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 flex items-center gap-3"
          >
            <span>استكشف المقالات</span>
            <svg className="w-5 h-5 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
            </svg>
          </Link>
          
          <Link
            href="/ai-tools"
            className="group border-2 border-purple-400 text-purple-400 px-8 py-4 rounded-xl font-semibold text-lg hover:bg-purple-400 hover:text-white transition-all duration-300 flex items-center gap-3"
          >
            <span>أدوات الذكاء الاصطناعي</span>
            <svg className="w-5 h-5 group-hover:rotate-12 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
          </Link>
        </div>

        {/* الإحصائيات */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
          <div className="text-center">
            <div className="text-3xl md:text-4xl font-bold text-gray-900 mb-2">100+</div>
            <div className="text-gray-600">مقال تقني</div>
          </div>
          <div className="text-center">
            <div className="text-3xl md:text-4xl font-bold text-gray-900 mb-2">50+</div>
            <div className="text-gray-600">أداة ذكاء اصطناعي</div>
          </div>
          <div className="text-center">
            <div className="text-3xl md:text-4xl font-bold text-gray-900 mb-2">1000+</div>
            <div className="text-gray-600">قارئ نشط</div>
          </div>
        </div>
      </div>

      {/* مؤشر التمرير */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-10">
        <div className="animate-bounce">
          <div className="w-8 h-8 border-2 border-gray-400/60 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-gray-600/80" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
            </svg>
          </div>
        </div>
      </div>
    </section>
  );
}
