/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5C11111111111111%5C%5Ctechno-flashi%5C%5Csrc%5C%5Ccomponents%5C%5Cads%5C%5CAnimatedAdRenderer.tsx%22%2C%22ids%22%3A%5B%22InContentAnimatedAd%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5C11111111111111%5C%5Ctechno-flashi%5C%5Csrc%5C%5Ccomponents%5C%5Cads%5C%5CTechnoFlashBanner.tsx%22%2C%22ids%22%3A%5B%22TechnoFlashContentBanner%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5C11111111111111%5C%5Ctechno-flashi%5C%5Csrc%5C%5Ccomponents%5C%5CLatestAIToolsSection.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5C11111111111111%5C%5Ctechno-flashi%5C%5Csrc%5C%5Ccomponents%5C%5CNewsletterSubscription.tsx%22%2C%22ids%22%3A%5B%22NewsletterSubscription%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5C11111111111111%5C%5Ctechno-flashi%5C%5Csrc%5C%5Ccomponents%5C%5CPerformanceOptimizer.tsx%22%2C%22ids%22%3A%5B%22PerformanceOptimizer%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5C11111111111111%5C%5Ctechno-flashi%5C%5Csrc%5C%5Ccomponents%5C%5CSimpleHeroSection.tsx%22%2C%22ids%22%3A%5B%22SimpleHeroSection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5C11111111111111%5C%5Ctechno-flashi%5C%5Csrc%5C%5Ccomponents%5C%5CSocialShare.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5C11111111111111%5C%5Ctechno-flashi%5C%5Csrc%5C%5Ccomponents%5C%5CSponsorsSection.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5C11111111111111%5C%5Ctechno-flashi%5C%5Csrc%5C%5Cstyles%5C%5Ccritical-homepage.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5C11111111111111%5C%5Ctechno-flashi%5C%5Csrc%5C%5Ccomponents%5C%5Cads%5C%5CAnimatedAdRenderer.tsx%22%2C%22ids%22%3A%5B%22InContentAnimatedAd%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5C11111111111111%5C%5Ctechno-flashi%5C%5Csrc%5C%5Ccomponents%5C%5Cads%5C%5CTechnoFlashBanner.tsx%22%2C%22ids%22%3A%5B%22TechnoFlashContentBanner%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5C11111111111111%5C%5Ctechno-flashi%5C%5Csrc%5C%5Ccomponents%5C%5CLatestAIToolsSection.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5C11111111111111%5C%5Ctechno-flashi%5C%5Csrc%5C%5Ccomponents%5C%5CNewsletterSubscription.tsx%22%2C%22ids%22%3A%5B%22NewsletterSubscription%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5C11111111111111%5C%5Ctechno-flashi%5C%5Csrc%5C%5Ccomponents%5C%5CPerformanceOptimizer.tsx%22%2C%22ids%22%3A%5B%22PerformanceOptimizer%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5C11111111111111%5C%5Ctechno-flashi%5C%5Csrc%5C%5Ccomponents%5C%5CSimpleHeroSection.tsx%22%2C%22ids%22%3A%5B%22SimpleHeroSection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5C11111111111111%5C%5Ctechno-flashi%5C%5Csrc%5C%5Ccomponents%5C%5CSocialShare.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5C11111111111111%5C%5Ctechno-flashi%5C%5Csrc%5C%5Ccomponents%5C%5CSponsorsSection.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5C11111111111111%5C%5Ctechno-flashi%5C%5Csrc%5C%5Cstyles%5C%5Ccritical-homepage.css%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ads/AnimatedAdRenderer.tsx */ \"(app-pages-browser)/./src/components/ads/AnimatedAdRenderer.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ads/TechnoFlashBanner.tsx */ \"(app-pages-browser)/./src/components/ads/TechnoFlashBanner.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/LatestAIToolsSection.tsx */ \"(app-pages-browser)/./src/components/LatestAIToolsSection.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/NewsletterSubscription.tsx */ \"(app-pages-browser)/./src/components/NewsletterSubscription.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/PerformanceOptimizer.tsx */ \"(app-pages-browser)/./src/components/PerformanceOptimizer.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/SimpleHeroSection.tsx */ \"(app-pages-browser)/./src/components/SimpleHeroSection.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/SocialShare.tsx */ \"(app-pages-browser)/./src/components/SocialShare.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/SponsorsSection.tsx */ \"(app-pages-browser)/./src/components/SponsorsSection.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/styles/critical-homepage.css */ \"(app-pages-browser)/./src/styles/critical-homepage.css\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5C11111111111111%5C%5Ctechno-flashi%5C%5Csrc%5C%5Ccomponents%5C%5Cads%5C%5CAnimatedAdRenderer.tsx%22%2C%22ids%22%3A%5B%22InContentAnimatedAd%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5C11111111111111%5C%5Ctechno-flashi%5C%5Csrc%5C%5Ccomponents%5C%5Cads%5C%5CTechnoFlashBanner.tsx%22%2C%22ids%22%3A%5B%22TechnoFlashContentBanner%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5C11111111111111%5C%5Ctechno-flashi%5C%5Csrc%5C%5Ccomponents%5C%5CLatestAIToolsSection.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5C11111111111111%5C%5Ctechno-flashi%5C%5Csrc%5C%5Ccomponents%5C%5CNewsletterSubscription.tsx%22%2C%22ids%22%3A%5B%22NewsletterSubscription%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5C11111111111111%5C%5Ctechno-flashi%5C%5Csrc%5C%5Ccomponents%5C%5CPerformanceOptimizer.tsx%22%2C%22ids%22%3A%5B%22PerformanceOptimizer%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5C11111111111111%5C%5Ctechno-flashi%5C%5Csrc%5C%5Ccomponents%5C%5CSimpleHeroSection.tsx%22%2C%22ids%22%3A%5B%22SimpleHeroSection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5C11111111111111%5C%5Ctechno-flashi%5C%5Csrc%5C%5Ccomponents%5C%5CSocialShare.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5C11111111111111%5C%5Ctechno-flashi%5C%5Csrc%5C%5Ccomponents%5C%5CSponsorsSection.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5C11111111111111%5C%5Ctechno-flashi%5C%5Csrc%5C%5Cstyles%5C%5Ccritical-homepage.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/LatestAIToolsSection.tsx":
/*!*************************************************!*\
  !*** ./src/components/LatestAIToolsSection.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LatestAIToolsSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction LatestAIToolsSection(param) {\n    let { className = '' } = param;\n    _s();\n    const [aiTools, setAiTools] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LatestAIToolsSection.useEffect\": ()=>{\n            fetchLatestAITools();\n        }\n    }[\"LatestAIToolsSection.useEffect\"], []);\n    const fetchLatestAITools = async ()=>{\n        try {\n            console.log('🤖 Fetching latest AI tools...');\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_4__.supabase.from('ai_tools').select('*').eq('status', 'active').order('created_at', {\n                ascending: false\n            }).limit(8);\n            if (error) {\n                console.error('❌ Error fetching AI tools:', error);\n                return;\n            }\n            console.log(\"✅ Fetched \".concat((data === null || data === void 0 ? void 0 : data.length) || 0, \" AI tools\"));\n            setAiTools(data || []);\n        } catch (error) {\n            console.error('💥 Error in fetchLatestAITools:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n            className: \"py-20 px-4 bg-gradient-to-br from-gray-50 to-white \".concat(className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-4xl font-bold text-gray-900 mb-4\",\n                                children: \"أحدث أدوات الذكاء الاصطناعي\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\LatestAIToolsSection.tsx\",\n                                lineNumber: 51,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n                                children: \"اكتشف أحدث وأقوى أدوات الذكاء الاصطناعي التي تساعدك في تطوير مشاريعك\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\LatestAIToolsSection.tsx\",\n                                lineNumber: 52,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\LatestAIToolsSection.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12\",\n                        children: [\n                            ...Array(8)\n                        ].map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-2xl shadow-lg p-6 animate-pulse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full h-48 bg-gray-200 rounded-xl mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\LatestAIToolsSection.tsx\",\n                                        lineNumber: 61,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-4 bg-gray-200 rounded mb-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\LatestAIToolsSection.tsx\",\n                                        lineNumber: 62,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-3 bg-gray-200 rounded mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\LatestAIToolsSection.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-8 bg-gray-200 rounded\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\LatestAIToolsSection.tsx\",\n                                        lineNumber: 64,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\LatestAIToolsSection.tsx\",\n                                lineNumber: 60,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\LatestAIToolsSection.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\LatestAIToolsSection.tsx\",\n                lineNumber: 49,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\LatestAIToolsSection.tsx\",\n            lineNumber: 48,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-20 px-4 bg-gradient-to-br from-gray-50 to-white \".concat(className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl font-bold text-gray-900 mb-4\",\n                            children: \"أحدث أدوات الذكاء الاصطناعي\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\LatestAIToolsSection.tsx\",\n                            lineNumber: 78,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n                            children: \"اكتشف أحدث وأقوى أدوات الذكاء الاصطناعي التي تساعدك في تطوير مشاريعك وتحسين إنتاجيتك\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\LatestAIToolsSection.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\LatestAIToolsSection.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12\",\n                    children: aiTools.map((tool, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"group bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 overflow-hidden transform hover:-translate-y-2\",\n                            style: {\n                                animationDelay: \"\".concat(index * 100, \"ms\")\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative w-full h-48 overflow-hidden bg-gradient-to-br from-purple-100 to-pink-100\",\n                                    children: [\n                                        tool.logo_url ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            src: tool.logo_url,\n                                            alt: tool.name,\n                                            fill: true,\n                                            className: \"object-cover group-hover:scale-110 transition-transform duration-500\",\n                                            sizes: \"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\LatestAIToolsSection.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-16 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-8 h-8 text-purple-600\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M13 10V3L4 14h7v7l9-11h-7z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\LatestAIToolsSection.tsx\",\n                                                        lineNumber: 110,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\LatestAIToolsSection.tsx\",\n                                                    lineNumber: 109,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\LatestAIToolsSection.tsx\",\n                                                lineNumber: 108,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\LatestAIToolsSection.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-3 right-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"bg-white/90 backdrop-blur-sm text-purple-600 px-3 py-1 rounded-full text-xs font-medium\",\n                                                children: tool.category || 'AI Tool'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\LatestAIToolsSection.tsx\",\n                                                lineNumber: 118,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\LatestAIToolsSection.tsx\",\n                                            lineNumber: 117,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\LatestAIToolsSection.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-bold text-gray-900 mb-2 group-hover:text-purple-600 transition-colors\",\n                                            children: tool.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\LatestAIToolsSection.tsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 text-sm mb-4 line-clamp-2\",\n                                            children: tool.description || 'أداة ذكاء اصطناعي متقدمة لتحسين الإنتاجية'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\LatestAIToolsSection.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        tool.pricing_type === 'free' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"bg-green-100 text-green-600 px-2 py-1 rounded-full text-xs font-medium\",\n                                                            children: \"مجاني\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\LatestAIToolsSection.tsx\",\n                                                            lineNumber: 138,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        tool.pricing_type === 'freemium' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"bg-blue-100 text-blue-600 px-2 py-1 rounded-full text-xs font-medium\",\n                                                            children: \"مجاني جزئياً\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\LatestAIToolsSection.tsx\",\n                                                            lineNumber: 143,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        tool.pricing_type === 'paid' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"bg-orange-100 text-orange-600 px-2 py-1 rounded-full text-xs font-medium\",\n                                                            children: \"مدفوع\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\LatestAIToolsSection.tsx\",\n                                                            lineNumber: 148,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\LatestAIToolsSection.tsx\",\n                                                    lineNumber: 136,\n                                                    columnNumber: 19\n                                                }, this),\n                                                tool.rating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4 text-yellow-400 fill-current\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\LatestAIToolsSection.tsx\",\n                                                                lineNumber: 157,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\LatestAIToolsSection.tsx\",\n                                                            lineNumber: 156,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: tool.rating\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\LatestAIToolsSection.tsx\",\n                                                            lineNumber: 159,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\LatestAIToolsSection.tsx\",\n                                                    lineNumber: 155,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\LatestAIToolsSection.tsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/ai-tools/\".concat(tool.slug || tool.id),\n                                            className: \"block w-full bg-gradient-to-r from-purple-600 to-pink-600 text-white text-center py-3 rounded-xl font-medium hover:from-purple-700 hover:to-pink-700 transition-all duration-300 transform group-hover:scale-105\",\n                                            children: \"استكشف الأداة\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\LatestAIToolsSection.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\LatestAIToolsSection.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, tool.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\LatestAIToolsSection.tsx\",\n                            lineNumber: 89,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\LatestAIToolsSection.tsx\",\n                    lineNumber: 87,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/ai-tools\",\n                        className: \"inline-flex items-center gap-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-4 rounded-xl font-semibold text-lg hover:from-purple-700 hover:to-pink-700 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"عرض جميع الأدوات\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\LatestAIToolsSection.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-5 h-5 group-hover:translate-x-1 transition-transform\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M13 7l5 5m0 0l-5 5m5-5H6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\LatestAIToolsSection.tsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\LatestAIToolsSection.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\LatestAIToolsSection.tsx\",\n                        lineNumber: 178,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\LatestAIToolsSection.tsx\",\n                    lineNumber: 177,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\LatestAIToolsSection.tsx\",\n            lineNumber: 75,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\LatestAIToolsSection.tsx\",\n        lineNumber: 74,\n        columnNumber: 5\n    }, this);\n}\n_s(LatestAIToolsSection, \"V6NyKJ0yR3LaBuM0nA/iEA5Ym4Y=\");\n_c = LatestAIToolsSection;\nvar _c;\n$RefreshReg$(_c, \"LatestAIToolsSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/LatestAIToolsSection.tsx\n"));

/***/ })

});