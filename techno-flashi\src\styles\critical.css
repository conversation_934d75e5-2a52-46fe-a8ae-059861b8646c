/* Critical CSS - الأنماط الأساسية للجزء العلوي من الصفحة */

/* إعادة تعيين أساسية */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* الخطوط المحلية فقط - لا توجد تبعيات خارجية */
/* Fonts loaded from local-fonts.css */

/* متغيرات الألوان الأساسية */
:root {
  --primary: #3333FF;
  --primary-hover: #3399FF;
  --secondary: #FF5722;
  --text-primary: #1C1C1C;
  --text-secondary: #4A4A4A;
  --text-description: #666666;
  --background-primary: #FFFFFF;
  --background-secondary: #F9F9F9;
  --background-tertiary: #FAFAFA;
  --border-light: #E1E5E9;
}

/* الجسم الأساسي */
body {
  font-family: "Cairo", "Tajawal", "Noto Kufi Arabic", sans-serif;
  font-size: 16px;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  background-color: var(--background-primary);
  color: var(--text-primary);
  direction: rtl;
}

/* العناوين الأساسية */
h1 {
  font-family: "Cairo", "Tajawal", "Noto Kufi Arabic", sans-serif;
  font-weight: 700;
  font-size: 32px;
  line-height: 1.3;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

h2 {
  font-family: "Cairo", "Tajawal", "Noto Kufi Arabic", sans-serif;
  font-weight: 600;
  font-size: 24px;
  line-height: 1.3;
  color: var(--text-primary);
  margin-bottom: 0.75rem;
}

h3 {
  font-family: "Cairo", "Tajawal", "Noto Kufi Arabic", sans-serif;
  font-weight: 500;
  font-size: 20px;
  line-height: 1.3;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

/* النص العادي */
p {
  font-size: 18px;
  font-weight: 400;
  line-height: 1.6;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

/* الروابط */
a {
  color: var(--primary);
  text-decoration: none;
  transition: color 0.2s ease;
}

a:hover {
  color: var(--primary-hover);
  text-decoration: underline;
}

/* الحاويات الأساسية */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* الهيدر الأساسي */
.hero-section {
  min-height: 60vh;
  display: flex;
  align-items: center;
  padding: 5rem 1rem;
  background-color: var(--background-primary);
}

/* فئات النصوص */
.heading-1 {
  font-size: 32px;
  font-weight: 700;
  line-height: 1.3;
  color: var(--text-primary);
  font-family: "Cairo", "Tajawal", "Noto Kufi Arabic", sans-serif;
}

.heading-2 {
  font-size: 24px;
  font-weight: 600;
  line-height: 1.3;
  color: var(--text-primary);
  font-family: "Cairo", "Tajawal", "Noto Kufi Arabic", sans-serif;
}

.heading-3 {
  font-size: 20px;
  font-weight: 500;
  line-height: 1.3;
  color: var(--text-primary);
  font-family: "Cairo", "Tajawal", "Noto Kufi Arabic", sans-serif;
}

.body-text {
  font-size: 18px;
  font-weight: 400;
  line-height: 1.6;
  color: var(--text-primary);
}

.text-description {
  color: var(--text-description);
  font-size: 14px;
  font-weight: 400;
}

.text-secondary {
  color: var(--text-secondary);
  font-weight: 500;
}

/* الأزرار الأساسية */
.btn-primary {
  background-color: var(--primary);
  color: black;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 500;
  font-size: 16px;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-block;
}

.btn-primary:hover {
  background-color: var(--primary-hover);
  color: black;
  text-decoration: none;
}

/* التخطيط الأساسي */
.grid {
  display: grid;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.text-center {
  text-align: center;
}

/* الاستجابة للموبايل */
@media (max-width: 768px) {
  body {
    font-size: 16px;
  }
  
  h1, .heading-1 {
    font-size: 28px;
  }
  
  h2, .heading-2 {
    font-size: 22px;
  }
  
  h3, .heading-3 {
    font-size: 18px;
  }
  
  p, .body-text {
    font-size: 16px;
  }
  
  .container {
    padding: 0 0.75rem;
  }
  
  .hero-section {
    padding: 3rem 1rem;
    min-height: 50vh;
  }
}

/* تحسينات الأداء */
.tech-container {
  max-width: 1200px;
  margin: 0 auto;
}

/* إخفاء المحتوى غير الأساسي مؤقتاً */
.non-critical {
  opacity: 0;
  transition: opacity 0.3s ease;
}

.non-critical.loaded {
  opacity: 1;
}
