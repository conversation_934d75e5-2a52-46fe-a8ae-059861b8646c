"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/HeroSection.tsx":
/*!****************************************!*\
  !*** ./src/components/HeroSection.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HeroSection: () => (/* binding */ HeroSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! three */ \"(app-pages-browser)/./node_modules/three/build/three.core.js\");\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! three */ \"(app-pages-browser)/./node_modules/three/build/three.module.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ HeroSection auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction HeroSection() {\n    _s();\n    const mountRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [isWebGLSupported, setIsWebGLSupported] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const sceneRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    const rendererRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    const animationRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    const mouseRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        x: 0,\n        y: 0\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HeroSection.useEffect\": ()=>{\n            if (!mountRef.current) return;\n            let scene;\n            let camera;\n            let renderer;\n            let particles;\n            let geometricShapes;\n            let ambientLight;\n            let pointLight;\n            try {\n                // إعداد المشهد\n                scene = new three__WEBPACK_IMPORTED_MODULE_3__.Scene();\n                sceneRef.current = scene;\n                // إعداد الكاميرا\n                camera = new three__WEBPACK_IMPORTED_MODULE_3__.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);\n                camera.position.z = 5;\n                // إعداد المُرسِل مع معالجة الأخطاء\n                renderer = new three__WEBPACK_IMPORTED_MODULE_4__.WebGLRenderer({\n                    antialias: true,\n                    alpha: true,\n                    powerPreference: \"high-performance\",\n                    failIfMajorPerformanceCaveat: false,\n                    preserveDrawingBuffer: false\n                });\n                // فحص دعم WebGL\n                const gl = renderer.getContext();\n                if (!gl) {\n                    throw new Error('WebGL not supported');\n                }\n                renderer.setSize(window.innerWidth, window.innerHeight);\n                renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));\n                renderer.setClearColor(0x000000, 0);\n                rendererRef.current = renderer;\n                mountRef.current.appendChild(renderer.domElement);\n                // إعداد الإضاءة\n                ambientLight = new three__WEBPACK_IMPORTED_MODULE_3__.AmbientLight(0x8b5cf6, 0.4);\n                scene.add(ambientLight);\n                pointLight = new three__WEBPACK_IMPORTED_MODULE_3__.PointLight(0xa855f7, 1, 100);\n                pointLight.position.set(10, 10, 10);\n                scene.add(pointLight);\n                // إنشاء جسيمات ثلاثية الأبعاد\n                const particleCount = 1000;\n                const positions = new Float32Array(particleCount * 3);\n                const colors = new Float32Array(particleCount * 3);\n                const sizes = new Float32Array(particleCount);\n                const colorPalette = [\n                    new three__WEBPACK_IMPORTED_MODULE_3__.Color(0x8b5cf6),\n                    new three__WEBPACK_IMPORTED_MODULE_3__.Color(0xa855f7),\n                    new three__WEBPACK_IMPORTED_MODULE_3__.Color(0xc084fc),\n                    new three__WEBPACK_IMPORTED_MODULE_3__.Color(0xd8b4fe)\n                ];\n                for(let i = 0; i < particleCount; i++){\n                    // المواقع العشوائية\n                    positions[i * 3] = (Math.random() - 0.5) * 20;\n                    positions[i * 3 + 1] = (Math.random() - 0.5) * 20;\n                    positions[i * 3 + 2] = (Math.random() - 0.5) * 20;\n                    // الألوان العشوائية\n                    const color = colorPalette[Math.floor(Math.random() * colorPalette.length)];\n                    colors[i * 3] = color.r;\n                    colors[i * 3 + 1] = color.g;\n                    colors[i * 3 + 2] = color.b;\n                    // الأحجام العشوائية\n                    sizes[i] = Math.random() * 3 + 1;\n                }\n                const particleGeometry = new three__WEBPACK_IMPORTED_MODULE_3__.BufferGeometry();\n                particleGeometry.setAttribute('position', new three__WEBPACK_IMPORTED_MODULE_3__.BufferAttribute(positions, 3));\n                particleGeometry.setAttribute('color', new three__WEBPACK_IMPORTED_MODULE_3__.BufferAttribute(colors, 3));\n                particleGeometry.setAttribute('size', new three__WEBPACK_IMPORTED_MODULE_3__.BufferAttribute(sizes, 1));\n                // إنشاء Shader Material مع معالجة الأخطاء\n                let particleMaterial;\n                try {\n                    particleMaterial = new three__WEBPACK_IMPORTED_MODULE_3__.ShaderMaterial({\n                        uniforms: {\n                            time: {\n                                value: 0\n                            },\n                            mouse: {\n                                value: new three__WEBPACK_IMPORTED_MODULE_3__.Vector2()\n                            }\n                        },\n                        vertexShader: \"\\n          attribute float size;\\n          attribute vec3 color;\\n          varying vec3 vColor;\\n          uniform float time;\\n          uniform vec2 mouse;\\n\\n          void main() {\\n            vColor = color;\\n            vec3 pos = position;\\n\\n            // تأثير الموجة\\n            pos.y += sin(pos.x * 0.5 + time) * 0.5;\\n            pos.x += cos(pos.z * 0.5 + time) * 0.3;\\n\\n            // تأثير الماوس\\n            vec2 mouseInfluence = mouse * 0.1;\\n            pos.xy += mouseInfluence * (1.0 - length(pos.xy) * 0.1);\\n\\n            vec4 mvPosition = modelViewMatrix * vec4(pos, 1.0);\\n            gl_PointSize = size * (300.0 / max(-mvPosition.z, 1.0));\\n            gl_Position = projectionMatrix * mvPosition;\\n          }\\n        \",\n                        fragmentShader: \"\\n          varying vec3 vColor;\\n\\n          void main() {\\n            float distanceToCenter = distance(gl_PointCoord, vec2(0.5));\\n            float alpha = 1.0 - smoothstep(0.0, 0.5, distanceToCenter);\\n            gl_FragColor = vec4(vColor, alpha * 0.8);\\n          }\\n        \",\n                        transparent: true,\n                        vertexColors: true,\n                        blending: three__WEBPACK_IMPORTED_MODULE_3__.AdditiveBlending\n                    });\n                    // فحص إذا كان الـ shader تم تجميعه بنجاح\n                    if (!particleMaterial.vertexShader || !particleMaterial.fragmentShader) {\n                        throw new Error('Shader compilation failed');\n                    }\n                } catch (shaderError) {\n                    console.warn('Shader creation failed, using basic material:', shaderError);\n                    // استخدام مادة أساسية كبديل\n                    particleMaterial = new three__WEBPACK_IMPORTED_MODULE_3__.PointsMaterial({\n                        size: 2,\n                        vertexColors: true,\n                        transparent: true,\n                        opacity: 0.8,\n                        blending: three__WEBPACK_IMPORTED_MODULE_3__.AdditiveBlending\n                    });\n                }\n                particles = new three__WEBPACK_IMPORTED_MODULE_3__.Points(particleGeometry, particleMaterial);\n                scene.add(particles);\n                // إنشاء أشكال هندسية معقدة\n                geometricShapes = new three__WEBPACK_IMPORTED_MODULE_3__.Group();\n                // مكعبات متحركة\n                for(let i = 0; i < 5; i++){\n                    const cubeGeometry = new three__WEBPACK_IMPORTED_MODULE_3__.BoxGeometry(0.5, 0.5, 0.5);\n                    const cubeMaterial = new three__WEBPACK_IMPORTED_MODULE_3__.MeshPhongMaterial({\n                        color: colorPalette[i % colorPalette.length],\n                        transparent: true,\n                        opacity: 0.3,\n                        wireframe: true\n                    });\n                    const cube = new three__WEBPACK_IMPORTED_MODULE_3__.Mesh(cubeGeometry, cubeMaterial);\n                    cube.position.set((Math.random() - 0.5) * 10, (Math.random() - 0.5) * 10, (Math.random() - 0.5) * 10);\n                    geometricShapes.add(cube);\n                }\n                // كرات متوهجة\n                for(let i = 0; i < 3; i++){\n                    const sphereGeometry = new three__WEBPACK_IMPORTED_MODULE_3__.SphereGeometry(0.3, 16, 16);\n                    const sphereMaterial = new three__WEBPACK_IMPORTED_MODULE_3__.MeshPhongMaterial({\n                        color: colorPalette[(i + 2) % colorPalette.length],\n                        transparent: true,\n                        opacity: 0.6,\n                        emissive: colorPalette[(i + 2) % colorPalette.length],\n                        emissiveIntensity: 0.2\n                    });\n                    const sphere = new three__WEBPACK_IMPORTED_MODULE_3__.Mesh(sphereGeometry, sphereMaterial);\n                    sphere.position.set((Math.random() - 0.5) * 8, (Math.random() - 0.5) * 8, (Math.random() - 0.5) * 8);\n                    geometricShapes.add(sphere);\n                }\n                scene.add(geometricShapes);\n                // حلقة الرسم والتحريك\n                const clock = new three__WEBPACK_IMPORTED_MODULE_3__.Clock();\n                const animate = {\n                    \"HeroSection.useEffect.animate\": ()=>{\n                        const elapsedTime = clock.getElapsedTime();\n                        // تحريك الجسيمات مع فحص النوع\n                        if (particles && particles.material) {\n                            if (particles.material instanceof three__WEBPACK_IMPORTED_MODULE_3__.ShaderMaterial && particles.material.uniforms) {\n                                particles.material.uniforms.time.value = elapsedTime;\n                                particles.material.uniforms.mouse.value.set(mouseRef.current.x, mouseRef.current.y);\n                            }\n                        }\n                        // تدوير الجسيمات\n                        if (particles) {\n                            particles.rotation.y = elapsedTime * 0.05;\n                            particles.rotation.x = Math.sin(elapsedTime * 0.03) * 0.1;\n                        }\n                        // تحريك الأشكال الهندسية\n                        geometricShapes.children.forEach({\n                            \"HeroSection.useEffect.animate\": (shape, index)=>{\n                                if (shape instanceof three__WEBPACK_IMPORTED_MODULE_3__.Mesh) {\n                                    // دوران مختلف لكل شكل\n                                    shape.rotation.x += 0.01 * (index + 1);\n                                    shape.rotation.y += 0.015 * (index + 1);\n                                    // حركة تموجية\n                                    shape.position.y += Math.sin(elapsedTime + index) * 0.002;\n                                    shape.position.x += Math.cos(elapsedTime * 0.5 + index) * 0.001;\n                                }\n                            }\n                        }[\"HeroSection.useEffect.animate\"]);\n                        // تحريك الإضاءة\n                        pointLight.position.x = Math.sin(elapsedTime) * 5;\n                        pointLight.position.z = Math.cos(elapsedTime) * 5;\n                        pointLight.intensity = 0.8 + Math.sin(elapsedTime * 2) * 0.2;\n                        // تأثير الماوس على الكاميرا\n                        camera.position.x += (mouseRef.current.x * 0.001 - camera.position.x) * 0.05;\n                        camera.position.y += (-mouseRef.current.y * 0.001 - camera.position.y) * 0.05;\n                        camera.lookAt(scene.position);\n                        renderer.render(scene, camera);\n                        animationRef.current = requestAnimationFrame(animate);\n                    }\n                }[\"HeroSection.useEffect.animate\"];\n                animate();\n                // معالج حركة الماوس\n                const handleMouseMove = {\n                    \"HeroSection.useEffect.handleMouseMove\": (event)=>{\n                        mouseRef.current.x = event.clientX / window.innerWidth * 2 - 1;\n                        mouseRef.current.y = -(event.clientY / window.innerHeight) * 2 + 1;\n                    }\n                }[\"HeroSection.useEffect.handleMouseMove\"];\n                // معالج تغيير حجم النافذة\n                const handleResize = {\n                    \"HeroSection.useEffect.handleResize\": ()=>{\n                        camera.aspect = window.innerWidth / window.innerHeight;\n                        camera.updateProjectionMatrix();\n                        renderer.setSize(window.innerWidth, window.innerHeight);\n                        renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));\n                    }\n                }[\"HeroSection.useEffect.handleResize\"];\n                // إضافة المستمعين\n                window.addEventListener('mousemove', handleMouseMove);\n                window.addEventListener('resize', handleResize);\n                // التنظيف\n                return ({\n                    \"HeroSection.useEffect\": ()=>{\n                        if (animationRef.current) {\n                            cancelAnimationFrame(animationRef.current);\n                        }\n                        window.removeEventListener('mousemove', handleMouseMove);\n                        window.removeEventListener('resize', handleResize);\n                        // تنظيف Three.js\n                        if (mountRef.current && renderer.domElement) {\n                            mountRef.current.removeChild(renderer.domElement);\n                        }\n                        // تنظيف الذاكرة\n                        scene.clear();\n                        renderer.dispose();\n                        // تنظيف الهندسة والمواد\n                        particles.geometry.dispose();\n                        particles.material.dispose();\n                        geometricShapes.children.forEach({\n                            \"HeroSection.useEffect\": (child)=>{\n                                if (child instanceof three__WEBPACK_IMPORTED_MODULE_3__.Mesh) {\n                                    child.geometry.dispose();\n                                    child.material.dispose();\n                                }\n                            }\n                        }[\"HeroSection.useEffect\"]);\n                    }\n                })[\"HeroSection.useEffect\"];\n            } catch (error) {\n                console.warn('WebGL/Three.js initialization failed:', error);\n                setIsWebGLSupported(false);\n            }\n        }\n    }[\"HeroSection.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900\",\n        children: [\n            isWebGLSupported && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: mountRef,\n                className: \"absolute inset-0 w-full h-full\",\n                style: {\n                    zIndex: 1\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                lineNumber: 328,\n                columnNumber: 9\n            }, this),\n            !isWebGLSupported && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-purple-600/20 via-pink-600/20 to-purple-600/20\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                        lineNumber: 338,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(139,92,246,0.1),transparent_70%)]\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                        lineNumber: 339,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-[conic-gradient(from_0deg_at_50%_50%,rgba(168,85,247,0.1),rgba(192,132,252,0.1),rgba(168,85,247,0.1))]\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                        lineNumber: 340,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                lineNumber: 337,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-t from-slate-900/50 via-transparent to-slate-900/30\",\n                style: {\n                    zIndex: 2\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                lineNumber: 345,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 container mx-auto px-6 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"inline-flex items-center px-6 py-3 bg-white/10 backdrop-blur-md text-purple-200 text-sm font-semibold rounded-full mb-8 border border-purple-300/30 shadow-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"mr-2 text-lg\",\n                                children: \"✨\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                                lineNumber: 351,\n                                columnNumber: 11\n                            }, this),\n                            \"بوابتك للمستقبل التقني\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                        lineNumber: 350,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-5xl md:text-7xl lg:text-8xl font-bold text-white leading-tight mb-6\",\n                        children: [\n                            \"مستقبلك التقني\",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                                lineNumber: 358,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-transparent bg-clip-text bg-gradient-to-r from-purple-400 via-pink-400 to-purple-600 animate-pulse\",\n                                children: \"يبدأ من هنا\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                                lineNumber: 359,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                        lineNumber: 356,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"max-w-4xl mx-auto text-xl md:text-2xl text-gray-300 mb-12 leading-relaxed font-light\",\n                        children: \"اكتشف أحدث التقنيات، أدوات الذكاء الاصطناعي المتطورة، ومقالات تقنية متخصصة لتطوير مهاراتك وتحقيق أهدافك في عالم التكنولوجيا المتطور.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                        lineNumber: 365,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row justify-center items-center gap-6 mb-20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/articles\",\n                                className: \"group relative bg-gradient-to-r from-purple-600 to-pink-600 text-white px-10 py-5 rounded-2xl shadow-2xl hover:shadow-purple-500/25 transition-all duration-500 transform hover:scale-105 font-semibold text-lg min-w-[220px] overflow-hidden\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-gradient-to-r from-purple-700 to-pink-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                                        lineNumber: 376,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"relative flex items-center justify-center\",\n                                        children: [\n                                            \"ابدأ الاستكشاف\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-6 h-6 mr-3 group-hover:translate-x-1 transition-transform duration-300\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M15 19l-7-7 7-7\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                                                    lineNumber: 380,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                                                lineNumber: 379,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                                        lineNumber: 377,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                                lineNumber: 372,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/ai-tools\",\n                                className: \"group relative bg-white/10 backdrop-blur-md text-white px-10 py-5 rounded-2xl border border-white/30 hover:bg-white/20 hover:border-purple-400/50 transition-all duration-500 font-semibold text-lg min-w-[220px] shadow-xl\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"flex items-center justify-center\",\n                                    children: [\n                                        \"أدوات الذكاء الاصطناعي\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-6 h-6 mr-3 group-hover:translate-x-1 transition-transform duration-300\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M13 10V3L4 14h7v7l9-11h-7z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                                                lineNumber: 392,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                                            lineNumber: 391,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                                    lineNumber: 389,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                                lineNumber: 385,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                        lineNumber: 371,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-4 gap-8 max-w-5xl mx-auto\",\n                        children: [\n                            {\n                                number: \"500+\",\n                                label: \"مقال تقني\"\n                            },\n                            {\n                                number: \"50+\",\n                                label: \"أداة ذكية\"\n                            },\n                            {\n                                number: \"10K+\",\n                                label: \"قارئ نشط\"\n                            },\n                            {\n                                number: \"24/7\",\n                                label: \"دعم فني\"\n                            }\n                        ].map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center group\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-4xl md:text-5xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-pink-400 mb-3 group-hover:scale-110 transition-transform duration-300\",\n                                        children: stat.number\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                                        lineNumber: 407,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-sm md:text-base font-medium\",\n                                        children: stat.label\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                                        lineNumber: 410,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                                lineNumber: 406,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                        lineNumber: 399,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                lineNumber: 348,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-8 left-1/2 transform -translate-x-1/2 z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-bounce\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-8 h-8 border-2 border-white/40 rounded-full flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-4 h-4 text-white/60\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M19 14l-7 7m0 0l-7-7m7 7V3\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                                lineNumber: 423,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                            lineNumber: 422,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                        lineNumber: 421,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                    lineNumber: 420,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                lineNumber: 419,\n                columnNumber: 7\n            }, this),\n            isWebGLSupported && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-4 right-4 z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-green-500/20 text-green-400 px-3 py-1 rounded-full text-xs font-medium border border-green-500/30\",\n                    children: \"✓ WebGL Active\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                    lineNumber: 432,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n                lineNumber: 431,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\11111111111111\\\\techno-flashi\\\\src\\\\components\\\\HeroSection.tsx\",\n        lineNumber: 325,\n        columnNumber: 5\n    }, this);\n}\n_s(HeroSection, \"kYS9FaXFF20tBPBsKpAJ4sL0XtE=\");\n_c = HeroSection;\nvar _c;\n$RefreshReg$(_c, \"HeroSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/HeroSection.tsx\n"));

/***/ })

});